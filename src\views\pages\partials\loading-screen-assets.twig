<!-- Loading Screen Assets - Enhanced with Theme Settings Integration -->
<!-- This provides both fallback loading screen and custom loading screen support -->

{% set loading_enabled = theme.settings.get('loading_screen_enabled', true) %}
{% set loading_bg_color = theme.settings.get('loading_screen_background_color', '#ffffff') %}
{% set loading_spinner_color = theme.settings.get('loading_screen_spinner_color', '#5bd5c4') %}

<style>
  /* Fallback loading screen - shown immediately while custom loading screen initializes */
  .loader-init {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: {{ loading_bg_color }};
      transition: opacity 0.75s, visibility 0.75s;
      z-index: 9998; /* Lower than custom loading screen */
  }

  .loader-init--hidden {
      opacity: 0;
      visibility: hidden;
  }

  .loader-init::after {
      content: "";
      width: 36px;
      height: 36px;
      border: 3px solid #eee;
      border-top-color: {{ loading_spinner_color }};
      border-radius: 50%;
      animation: loading 0.75s ease infinite;
  }

  @keyframes loading {
      from {
        transform: rotate(0turn);
      }
      to {
        transform: rotate(1turn);
      }
  }

  /* Hide fallback loader when custom loading screen is present */
  .custom-loading-screen ~ .loader-init {
      display: none;
  }
</style>

<script>
  // Enhanced loading screen management
  (function() {
    'use strict';

    const loadingEnabled = {{ loading_enabled | json_encode }};

    // Hide fallback loader when page loads
    function hideFallbackLoader() {
      const loader = document.querySelector(".loader-init");
      if (!loader) return;

      loader.classList.add("loader-init--hidden");
      loader.addEventListener("transitionend", () => {
        if (loader && loader.parentNode && !document.querySelector(".custom-loading-screen")) {
          loader.parentNode.removeChild(loader);
        }
      });
    }

    // If custom loading screen is disabled, use fallback behavior
    if (!loadingEnabled) {
      window.addEventListener("load", hideFallbackLoader);
    } else {
      // Custom loading screen is enabled, hide fallback after a short delay
      // to allow custom loading screen to initialize
      setTimeout(() => {
        const customLoader = document.querySelector(".custom-loading-screen");
        if (customLoader) {
          // Custom loader is present, hide fallback immediately
          const fallbackLoader = document.querySelector(".loader-init");
          if (fallbackLoader) {
            fallbackLoader.style.display = 'none';
          }
        } else {
          // Custom loader not found, keep fallback and hide on load
          window.addEventListener("load", hideFallbackLoader);
        }
      }, 100);
    }

    // Debug information
    console.log('🔄 Loading Screen Assets initialized');
    console.log('  - Custom loading enabled:', loadingEnabled);
    console.log('  - Background color:', '{{ loading_bg_color }}');
    console.log('  - Spinner color:', '{{ loading_spinner_color }}');
  })();
</script>