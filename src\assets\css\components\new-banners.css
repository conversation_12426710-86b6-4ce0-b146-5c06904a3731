/* Gaming Theme Banner Styles - Optimized for Performance */
/* Enhanced with comprehensive configuration support */

/* CSS Custom Properties for Dynamic Theming */
:root {
    --title-color: #ffffff;
    --subtitle-color: #e0e0e0;
    --hover-overlay-color: #00ff88;
    --glow-border-color: #00d4ff;
    --image-border-radius: 0px;
    --banner-height: 500px;
}

/* Use transform3d for hardware acceleration */
.gaming-theme-slider {
    margin-bottom: 2rem;
    will-change: transform; /* Hint browser for optimization */
}

/* Optimized pop-up animation */
.gaming-banners-section {
    opacity: 0;
    transform: translate3d(0, 20px, 0) scale(0.98);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.gaming-banners-section.animate-in {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
}

.gaming-banner-slide {
    height: 500px;
    position: relative;
    overflow: hidden;
    contain: layout style paint; /* CSS containment for performance */
}

.gaming-banner-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.gaming-banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-image {
    transform: scale3d(1.05, 1.05, 1);
}

/* Optimized overlay with better performance */
.gaming-banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top,
        rgba(10, 10, 15, 0.9) 0%,
        rgba(10, 10, 15, 0.6) 40%,
        rgba(10, 10, 15, 0.3) 70%,
        rgba(10, 10, 15, 0.1) 100%);
    z-index: 1;
}

.gaming-banner-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 3rem;
    z-index: 2;
    color: #fff;
    text-align: start;
    transform: translate3d(0, 0, 0);
    transition: transform 0.4s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-content {
    transform: translate3d(0, -10px, 0);
}

/* Optimized text animations */
.animate-element {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
    transition: opacity 0.6s ease, transform 0.6s ease;
    transition-delay: 0s;
}

.animate-element.animate-in {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}

.gaming-banner-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);
    color: #fff;
    position: relative;
    display: inline-block;
}

/* Optimized pseudo-element animation */
.gaming-banner-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--color-primary, #1DE9B6);
    box-shadow: 0 0 10px var(--color-primary, #1DE9B6);
    transition: width 0.6s ease 0.6s;
}

.gaming-banner-title.animate-in::after {
    width: 60px;
}

.gaming-banner-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.7);
    margin-bottom: 1.5rem;
    max-width: 600px;
}

.gaming-banner-button {
    display: inline-flex;
    align-items: center;
    background: rgba(29, 233, 182, 0.2);
    border: 1px solid var(--color-primary, #1DE9B6);
    color: #fff;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(29, 233, 182, 0.3);
    cursor: pointer;
    will-change: transform, box-shadow;
}

.gaming-banner-button .btn-icon {
    margin-right: 10px;
    margin-left: 10px;
    transition: transform 0.3s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-button {
    background: rgba(29, 233, 182, 0.3);
    box-shadow: 0 0 20px rgba(29, 233, 182, 0.5);
}

.gaming-banner-link:hover .gaming-banner-button .btn-icon {
    transform: translate3d(5px, 0, 0);
}

/* Optimized animated elements */
.gaming-banner-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
    contain: layout style paint;
}

/* Reduced and optimized particles */
.gaming-particle {
    position: absolute;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(29, 233, 182, 0.15) 0%, rgba(29, 233, 182, 0) 70%);
    border-radius: 50%;
    animation: optimizedFloat 12s infinite ease-in-out;
    animation-delay: var(--delay, 0s);
    opacity: 0;
    will-change: transform, opacity;
}

.particle-1 {
    top: 20%;
    left: 15%;
    width: 100px;
    height: 100px;
}

.particle-2 {
    top: 60%;
    right: 20%;
    width: 80px;
    height: 80px;
}

.gaming-glow {
    position: absolute;
    bottom: -30px;
    left: 0;
    right: 0;
    height: 60px;
    background: radial-gradient(ellipse at center, rgba(29, 233, 182, 0.15) 0%, rgba(29, 233, 182, 0) 70%);
    opacity: 0.5;
}

/* Optimized animations using transform3d */
@keyframes optimizedFloat {
    0% {
        transform: translate3d(0, 0, 0);
        opacity: 0;
    }
    25% {
        opacity: 0.4;
    }
    50% {
        transform: translate3d(10px, -15px, 0);
        opacity: 0.6;
    }
    75% {
        opacity: 0.4;
    }
    100% {
        transform: translate3d(0, 0, 0);
        opacity: 0;
    }
}

/* Responsive optimizations */
@media (max-width: 1024px) {
    .gaming-banner-slide {
        height: 450px;
    }
    
    .gaming-banner-title {
        font-size: 2rem;
    }
    
    .gaming-banner-subtitle {
        font-size: 1.1rem;
    }
    
    /* Reduce particles on smaller screens */
    .gaming-particle {
        width: 60px;
        height: 60px;
    }
}

@media (max-width: 768px) {
    .gaming-banner-slide {
        height: 350px;
    }
    
    .gaming-banner-title {
        font-size: 1.75rem;
    }
    
    .gaming-banner-subtitle {
        font-size: 1rem;
    }
    
    .gaming-banner-content {
        padding: 2rem;
    }
    
    .gaming-banner-button {
        padding: 0.6rem 1.2rem;
    }
    
    /* Disable particles on mobile for better performance */
    .gaming-particle {
        display: none;
    }
}

@media (max-width: 480px) {
    .gaming-banner-slide {
        height: 300px;
    }
    
    .gaming-banner-title {
        font-size: 1.5rem;
    }
    
    .gaming-banner-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .gaming-banner-content {
        padding: 1.5rem;
    }
    
    .gaming-banner-button {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    /* Disable glow effects on very small screens */
    .gaming-glow {
        display: none;
    }
}

/* Prefers reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .gaming-particle,
    .gaming-glow {
        animation: none;
        opacity: 0;
    }

    .gaming-banner-image,
    .gaming-banner-content,
    .animate-element {
        transition: none;
    }
}

/* Critical performance optimizations */
.gaming-banners-section {
    contain: layout style paint;
    content-visibility: auto;
    contain-intrinsic-size: 500px;
}

/* Optimize for low-end devices */
@media (max-width: 768px) and (max-resolution: 1.5dppx) {
    .gaming-particle,
    .gaming-glow {
        display: none;
    }

    .gaming-banner-image {
        transition: none;
    }

    .gaming-banner-link:hover .gaming-banner-image {
        transform: none;
    }
}

/* Print styles */
@media print {
    .gaming-particle,
    .gaming-glow,
    .gaming-banner-effects {
        display: none;
    }
}

/* ===== CONFIGURATION-BASED STYLES ===== */

/* Title Font Size Classes */
.title-font-small .gaming-banner-title {
    font-size: 1.5rem;
}

.title-font-medium .gaming-banner-title {
    font-size: 2rem;
}

.title-font-large .gaming-banner-title {
    font-size: 2.5rem;
}

.title-font-extra-large .gaming-banner-title {
    font-size: 3rem;
}

/* Title Font Weight Classes */
.title-weight-normal .gaming-banner-title {
    font-weight: 400;
}

.title-weight-medium .gaming-banner-title {
    font-weight: 500;
}

.title-weight-bold .gaming-banner-title {
    font-weight: 700;
}

.title-weight-extra-bold .gaming-banner-title {
    font-weight: 900;
}

/* Subtitle Font Size Classes */
.subtitle-font-small .gaming-banner-subtitle {
    font-size: 0.875rem;
}

.subtitle-font-medium .gaming-banner-subtitle {
    font-size: 1rem;
}

.subtitle-font-large .gaming-banner-subtitle {
    font-size: 1.25rem;
}

/* Text Alignment Classes */
.text-align-right .gaming-banner-content {
    text-align: right;
}

.text-align-center .gaming-banner-content {
    text-align: center;
}

.text-align-left .gaming-banner-content {
    text-align: left;
}

/* Animation Duration Classes */
.animation-duration-fast .animate-element {
    animation-duration: 0.5s;
    transition-duration: 0.5s;
}

.animation-duration-medium .animate-element {
    animation-duration: 1s;
    transition-duration: 1s;
}

.animation-duration-slow .animate-element {
    animation-duration: 1.5s;
    transition-duration: 1.5s;
}

.animation-duration-extra-slow .animate-element {
    animation-duration: 2s;
    transition-duration: 2s;
}

/* Animation Delay Classes */
.animation-delay-none .animate-element {
    animation-delay: 0s;
}

.animation-delay-short .animate-element:nth-child(1) {
    animation-delay: 0.2s;
}

.animation-delay-short .animate-element:nth-child(2) {
    animation-delay: 0.4s;
}

.animation-delay-medium .animate-element:nth-child(1) {
    animation-delay: 0.5s;
}

.animation-delay-medium .animate-element:nth-child(2) {
    animation-delay: 1s;
}

.animation-delay-long .animate-element:nth-child(1) {
    animation-delay: 1s;
}

.animation-delay-long .animate-element:nth-child(2) {
    animation-delay: 2s;
}

/* Banner Animation Classes */
.banner-animation-fadeIn .gaming-banner-slide {
    opacity: 0;
    animation: fadeInAnimation 1s ease forwards;
}

.banner-animation-slideUp .gaming-banner-slide {
    opacity: 0;
    transform: translateY(50px);
    animation: slideUpAnimation 1s ease forwards;
}

.banner-animation-scaleIn .gaming-banner-slide {
    opacity: 0;
    transform: scale(0.8);
    animation: scaleInAnimation 1s ease forwards;
}

.banner-animation-bounceIn .gaming-banner-slide {
    opacity: 0;
    transform: scale(0.3);
    animation: bounceInAnimation 1s ease forwards;
}

.banner-animation-slideLeft .gaming-banner-slide {
    opacity: 0;
    transform: translateX(-50px);
    animation: slideLeftAnimation 1s ease forwards;
}

/* Text Animation Classes */
.text-animation-fadeIn .animate-element {
    opacity: 0;
    animation: fadeInAnimation 1s ease forwards;
}

.text-animation-slideUp .animate-element {
    opacity: 0;
    transform: translateY(30px);
    animation: slideUpAnimation 1s ease forwards;
}

.text-animation-scaleIn .animate-element {
    opacity: 0;
    transform: scale(0.8);
    animation: scaleInAnimation 1s ease forwards;
}

.text-animation-trackingExpand .animate-element {
    opacity: 0;
    letter-spacing: -0.5em;
    animation: trackingExpandAnimation 1s ease forwards;
}

/* Hover Animation Classes */
.hover-effects-enabled.hover-animation-scale .gaming-banner-link:hover .gaming-banner-image {
    transform: scale(1.05);
}

.hover-effects-enabled.hover-animation-slide .gaming-banner-link:hover .gaming-banner-image {
    transform: translateX(10px);
}

.hover-effects-enabled.hover-animation-pulse .gaming-banner-link:hover .gaming-banner-image {
    animation: pulseAnimation 1s ease infinite;
}

.hover-effects-enabled.hover-animation-glow .gaming-banner-link:hover .gaming-banner-image {
    box-shadow: 0 0 30px var(--glow-border-color);
}

/* Visual Effects Classes */
.image-shadows-enabled .gaming-banner-image {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.glow-effects-enabled .gaming-banner-slide {
    position: relative;
}

.glow-effects-enabled .gaming-banner-slide::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--glow-border-color), transparent, var(--hover-overlay-color));
    border-radius: var(--image-border-radius);
    z-index: -1;
    opacity: 0.3;
}

.particles-enabled .particles-bg {
    display: block;
}

.particles-enabled:not(.particles-enabled) .particles-bg {
    display: none;
}

/* Full Width Layout */
.full-width {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
}

/* Dynamic Color Application */
.gaming-banner-title {
    color: var(--title-color);
}

.gaming-banner-subtitle {
    color: var(--subtitle-color);
}

.gaming-banner-overlay {
    background: linear-gradient(45deg, var(--hover-overlay-color)22, transparent);
}

/* Animation Keyframes */
@keyframes fadeInAnimation {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUpAnimation {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleInAnimation {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceInAnimation {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideLeftAnimation {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes trackingExpandAnimation {
    from {
        opacity: 0;
        letter-spacing: -0.5em;
    }
    to {
        opacity: 1;
        letter-spacing: normal;
    }
}

@keyframes pulseAnimation {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

/* Responsive Adjustments for Configuration Classes */
@media (max-width: 768px) {
    .title-font-small .gaming-banner-title {
        font-size: 1.25rem;
    }

    .title-font-medium .gaming-banner-title {
        font-size: 1.5rem;
    }

    .title-font-large .gaming-banner-title {
        font-size: 1.75rem;
    }

    .title-font-extra-large .gaming-banner-title {
        font-size: 2rem;
    }

    .subtitle-font-small .gaming-banner-subtitle {
        font-size: 0.75rem;
    }

    .subtitle-font-medium .gaming-banner-subtitle {
        font-size: 0.875rem;
    }

    .subtitle-font-large .gaming-banner-subtitle {
        font-size: 1rem;
    }

    .full-width {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}
