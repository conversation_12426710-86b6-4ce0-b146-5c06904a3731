{#
| Variable                        | Type     | Description                                                                  |
|---------------------------------|----------|------------------------------------------------------------------------------|
| component                       | object   | Contains merchant settings for fields from twilight.json `component` section |
| component.banner                | object[] | list of banners                                                              |
| component.banner[].img          | string   | Banner image URL                                                             |
| component.banner[].url          | object   | Banner link (variable-list format)                                           |
| component.banner[].title        | ?string  | Banner title                                                                 |
| component.banner[].subtitle     | ?string  | Banner subtitle/description                                                  |
| component.title_font_size       | string   | Title font size (small, medium, large, extra-large)                         |
| component.title_font_weight     | string   | Title font weight (normal, medium, bold, extra-bold)                        |
| component.subtitle_font_size    | string   | Subtitle font size (small, medium, large)                                   |
| component.text_alignment        | string   | Text alignment (right, center, left)                                        |
| component.title_text_color      | string   | Title text color                                                             |
| component.subtitle_text_color   | string   | Subtitle text color                                                          |
| component.enable_background     | boolean  | Enable/disable background color                                              |
| component.background_color      | string   | Background color (when enabled)                                              |
| component.hover_overlay_color   | string   | Hover overlay color                                                          |
| component.glow_border_color     | string   | Glow border color                                                            |
| component.banner_animation_type | string   | Banner animation type                                                        |
| component.text_animation_type   | string   | Text animation type                                                          |
| component.animation_duration    | string   | Animation duration                                                           |
| component.animation_delay       | string   | Animation delay                                                              |
| component.banner_height         | number   | Banner height in pixels                                                      |
| component.top_margin            | number   | Top margin in pixels                                                         |
| component.bottom_margin         | number   | Bottom margin in pixels                                                      |
| component.full_width            | boolean  | Full width toggle                                                            |
| component.enable_hover_effects  | boolean  | Enable hover effects                                                         |
| component.hover_animation_type  | string   | Hover animation type                                                         |
| component.image_border_radius   | number   | Image border radius in pixels                                                |
| component.enable_image_shadows  | boolean  | Enable image shadows                                                         |
| component.enable_glow_effects   | boolean  | Enable glow effects                                                          |
| component.enable_particles      | boolean  | Enable particle effects                                                      |
| position                        | int      | Sorting number start from zero                                               |
#}

{# Load optimized external CSS and JS for better performance #}
<link rel="stylesheet" href="{{ 'assets/css/components/new-banners.css' | asset }}" media="all">
<script src="{{ 'assets/js/components/new-banners.js' | asset }}" defer></script>

{# Inline critical CSS to ensure styles are applied immediately #}
<style>
/* Critical Gaming Theme Banner Styles */
.gaming-theme-slider {
    margin-bottom: 2rem;
    will-change: transform;
}

.gaming-banners-section {
    opacity: 0;
    transform: translate3d(0, 20px, 0) scale(0.98);
    transition: opacity 0.6s ease, transform 0.6s ease;
    contain: layout style paint;
}

.gaming-banners-section.animate-in {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
}

.gaming-banner-slide {
    height: 500px;
    position: relative;
    overflow: hidden;
    contain: layout style paint;
}

.gaming-banner-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.gaming-banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-image {
    transform: scale3d(1.05, 1.05, 1);
}

.gaming-banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top,
        rgba(10, 10, 15, 0.9) 0%,
        rgba(10, 10, 15, 0.6) 40%,
        rgba(10, 10, 15, 0.3) 70%,
        rgba(10, 10, 15, 0.1) 100%);
    z-index: 1;
}

.gaming-banner-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 3rem;
    z-index: 2;
    color: #fff;
    text-align: start;
    transform: translate3d(0, 0, 0);
    transition: transform 0.4s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-content {
    transform: translate3d(0, -10px, 0);
}

.animate-element {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
    transition: opacity 0.6s ease, transform 0.6s ease;
    transition-delay: 0s;
}

.animate-element.animate-in {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}

.gaming-banner-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);
    color: #fff;
    position: relative;
    display: inline-block;
}

.gaming-banner-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--color-primary, #1DE9B6);
    box-shadow: 0 0 10px var(--color-primary, #1DE9B6);
    transition: width 0.6s ease 0.6s;
}

.gaming-banner-title.animate-in::after {
    width: 60px;
}

.gaming-banner-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.7);
    margin-bottom: 1.5rem;
    max-width: 600px;
}

.gaming-banner-button {
    display: inline-flex;
    align-items: center;
    background: rgba(29, 233, 182, 0.2);
    border: 1px solid var(--color-primary, #1DE9B6);
    color: #fff;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(29, 233, 182, 0.3);
    cursor: pointer;
    will-change: transform, box-shadow;
}

.gaming-banner-button .btn-icon {
    margin-right: 10px;
    margin-left: 10px;
    transition: transform 0.3s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-button {
    background: rgba(29, 233, 182, 0.3);
    box-shadow: 0 0 20px rgba(29, 233, 182, 0.5);
}

.gaming-banner-link:hover .gaming-banner-button .btn-icon {
    transform: translate3d(5px, 0, 0);
}

.gaming-banner-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
    contain: layout style paint;
}

.gaming-particle {
    position: absolute;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(29, 233, 182, 0.15) 0%, rgba(29, 233, 182, 0) 70%);
    border-radius: 50%;
    animation: optimizedFloat 12s infinite ease-in-out;
    animation-delay: var(--delay, 0s);
    opacity: 0;
    will-change: transform, opacity;
}

.particle-1 {
    top: 20%;
    left: 15%;
    width: 100px;
    height: 100px;
}

.particle-2 {
    top: 60%;
    right: 20%;
    width: 80px;
    height: 80px;
}

.gaming-glow {
    position: absolute;
    bottom: -30px;
    left: 0;
    right: 0;
    height: 60px;
    background: radial-gradient(ellipse at center, rgba(29, 233, 182, 0.15) 0%, rgba(29, 233, 182, 0) 70%);
    opacity: 0.5;
}

@keyframes optimizedFloat {
    0% {
        transform: translate3d(0, 0, 0);
        opacity: 0;
    }
    25% {
        opacity: 0.4;
    }
    50% {
        transform: translate3d(10px, -15px, 0);
        opacity: 0.6;
    }
    75% {
        opacity: 0.4;
    }
    100% {
        transform: translate3d(0, 0, 0);
        opacity: 0;
    }
}

/* Responsive */
@media (max-width: 1024px) {
    .gaming-banner-slide { height: 450px; }
    .gaming-banner-title { font-size: 2rem; }
    .gaming-banner-subtitle { font-size: 1.1rem; }
    .gaming-particle { width: 60px; height: 60px; }
}

@media (max-width: 768px) {
    .gaming-banner-slide { height: 350px; }
    .gaming-banner-title { font-size: 1.75rem; }
    .gaming-banner-subtitle { font-size: 1rem; }
    .gaming-banner-content { padding: 2rem; }
    .gaming-banner-button { padding: 0.6rem 1.2rem; }
    .gaming-particle { display: none; }
}

@media (max-width: 480px) {
    .gaming-banner-slide { height: 300px; }
    .gaming-banner-title { font-size: 1.5rem; }
    .gaming-banner-subtitle { font-size: 0.9rem; margin-bottom: 1rem; }
    .gaming-banner-content { padding: 1.5rem; }
    .gaming-banner-button { padding: 0.5rem 1rem; font-size: 0.9rem; }
    .gaming-glow { display: none; }
}

@media (prefers-reduced-motion: reduce) {
    .gaming-particle, .gaming-glow { animation: none; opacity: 0; }
    .gaming-banner-image, .gaming-banner-content, .animate-element { transition: none; }
}
</style>

{# Configuration variables with defaults #}
{% set title_font_size = component.title_font_size.value|default('large') %}
{% set title_font_weight = component.title_font_weight.value|default('bold') %}
{% set subtitle_font_size = component.subtitle_font_size.value|default('medium') %}
{% set text_alignment = component.text_alignment.value|default('left') %}
{% set title_text_color = component.title_text_color|default('#ffffff') %}
{% set subtitle_text_color = component.subtitle_text_color|default('#e0e0e0') %}
{% set enable_background = component.enable_background|default(false) %}
{% set background_color = component.background_color|default('#0a0a0a') %}
{% set hover_overlay_color = component.hover_overlay_color|default('#00ff88') %}
{% set glow_border_color = component.glow_border_color|default('#00d4ff') %}
{% set banner_animation_type = component.banner_animation_type.value|default('fadeIn') %}
{% set text_animation_type = component.text_animation_type.value|default('slideUp') %}
{% set animation_duration = component.animation_duration.value|default('medium') %}
{% set animation_delay = component.animation_delay.value|default('short') %}
{% set banner_height = component.banner_height|default(500) %}
{% set top_margin = component.top_margin|default(0) %}
{% set bottom_margin = component.bottom_margin|default(40) %}
{% set full_width = component.full_width|default(true) %}
{% set enable_hover_effects = component.enable_hover_effects|default(true) %}
{% set hover_animation_type = component.hover_animation_type.value|default('scale') %}
{% set image_border_radius = component.image_border_radius|default(0) %}
{% set enable_image_shadows = component.enable_image_shadows|default(false) %}
{% set enable_glow_effects = component.enable_glow_effects|default(true) %}
{% set enable_particles = component.enable_particles|default(true) %}

{# Build CSS classes #}
{% set container_classes = [
    's-block',
    's-block--new-banners',
    'wide-placeholder',
    'gaming-banners-section',
    'title-font-' ~ title_font_size,
    'title-weight-' ~ title_font_weight,
    'subtitle-font-' ~ subtitle_font_size,
    'text-align-' ~ text_alignment,
    'banner-animation-' ~ banner_animation_type,
    'text-animation-' ~ text_animation_type,
    'animation-duration-' ~ animation_duration,
    'animation-delay-' ~ animation_delay,
    'hover-animation-' ~ hover_animation_type,
    full_width ? 'full-width' : '',
    enable_hover_effects ? 'hover-effects-enabled' : '',
    enable_image_shadows ? 'image-shadows-enabled' : '',
    enable_glow_effects ? 'glow-effects-enabled' : '',
    enable_particles ? 'particles-enabled' : ''
]|join(' ')|trim %}

{# Build inline styles #}
{% set container_styles = [
    'margin-top: ' ~ top_margin ~ 'px',
    'margin-bottom: ' ~ bottom_margin ~ 'px',
    '--title-color: ' ~ title_text_color,
    '--subtitle-color: ' ~ subtitle_text_color,
    '--hover-overlay-color: ' ~ hover_overlay_color,
    '--glow-border-color: ' ~ glow_border_color,
    '--image-border-radius: ' ~ image_border_radius ~ 'px',
    '--banner-height: ' ~ banner_height ~ 'px'
] %}

{% if enable_background %}
    {% set container_styles = container_styles|merge(['background: ' ~ background_color]) %}
{% endif %}

<section class="{{ container_classes }}"
         id="gaming-banners-section-{{ position }}"
         style="{{ container_styles|join('; ') }}"
         data-banner-animation="{{ banner_animation_type }}"
         data-text-animation="{{ text_animation_type }}"
         data-animation-duration="{{ animation_duration }}"
         data-animation-delay="{{ animation_delay }}"
         data-hover-animation="{{ hover_animation_type }}"
         data-enable-hover="{{ enable_hover_effects ? 'true' : 'false' }}"
         data-enable-particles="{{ enable_particles ? 'true' : 'false' }}">
    {% if component.banner|length %}
        {% if enable_particles %}
        <!-- Animated particles background -->
        <div class="particles-bg" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 1;">
            <div class="particle" style="position: absolute; width: 2px; height: 2px; background: {{ hover_overlay_color }}; border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
            <div class="particle" style="position: absolute; width: 3px; height: 3px; background: {{ glow_border_color }}; border-radius: 50%; animation: float 8s ease-in-out infinite 2s;"></div>
            <div class="particle" style="position: absolute; width: 1px; height: 1px; background: #ff0080; border-radius: 50%; animation: float 10s ease-in-out infinite 4s;"></div>
        </div>
        {% endif %}

        <salla-slider
            id="gaming-banners-{{ position }}"
            type="fullwidth"
            auto-play
            pagination
            show-controls="true"
            class="gaming-theme-slider"
            style="height: {{ banner_height }}px;">
            <div slot="items">
                {% for banner in component.banner %}
                    <div class="swiper-slide gaming-banner-slide"
                         style="height: {{ banner_height }}px;"
                         data-slide-index="{{ loop.index0 }}">
                        <a href="{{ banner.url }}" class="gaming-banner-link">
                            <div class="gaming-banner-image-container">
                                <img src="{{ banner.img }}"
                                     alt="{{ banner.title|default('Banner ' ~ loop.index) }}"
                                     class="gaming-banner-image"
                                     style="border-radius: {{ image_border_radius }}px;"
                                     loading="lazy"
                                     decoding="async">

                                <div class="gaming-banner-overlay"
                                     style="background: linear-gradient(45deg, {{ hover_overlay_color }}22, transparent);"></div>

                                {% if banner.title or banner.subtitle %}
                                    <div class="gaming-banner-content text-align-{{ text_alignment }}">
                                        {% if banner.title %}
                                            <h3 class="gaming-banner-title animate-element title-font-{{ title_font_size }} title-weight-{{ title_font_weight }}"
                                                style="color: {{ title_text_color }};">{{ banner.title }}</h3>
                                        {% endif %}

                                        {% if banner.subtitle %}
                                            <p class="gaming-banner-subtitle animate-element subtitle-font-{{ subtitle_font_size }}"
                                               style="color: {{ subtitle_text_color }};">{{ banner.subtitle }}</p>
                                        {% endif %}

                                        <div class="gaming-banner-button animate-element">
                                            <span class="btn-text">اكتشف الآن</span>
                                            <span class="btn-icon">
                                                <i class="sicon-arrow-{{ salla.config.get('theme.is_rtl') ? 'left' : 'right' }}"></i>
                                            </span>
                                        </div>
                                    </div>
                                {% endif %}

                                <!-- Optimized Animated Elements - Reduced from 5 to 2 particles -->
                                <div class="gaming-banner-effects">
                                    {% for i in 1..2 %}
                                        <div class="gaming-particle particle-{{ i }}" style="--delay: {{ i * 1 }}s;"></div>
                                    {% endfor %}
                                    <div class="gaming-glow"></div>
                                </div>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>
        </salla-slider>
    {% endif %}
</section>

{# Inline JavaScript to ensure immediate functionality #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gaming Banners Animation Manager
    class GamingBannersManager {
        constructor() {
            this.observer = null;
            this.animatedElements = new Set();
            this.init();
        }

        init() {
            this.createObserver();
            this.observeBannerSections();
        }

        createObserver() {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.animatedElements.has(entry.target)) {
                        this.animateElement(entry.target);
                        this.animatedElements.add(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.15,
                rootMargin: '50px 0px'
            });
        }

        animateElement(element) {
            requestAnimationFrame(() => {
                element.classList.add('animate-in');

                const animateElements = element.querySelectorAll('.animate-element');
                animateElements.forEach((child, index) => {
                    child.style.transitionDelay = `${0.2 + (index * 0.15)}s`;
                    requestAnimationFrame(() => {
                        child.classList.add('animate-in');
                    });
                });
            });
        }

        observeBannerSections() {
            const bannerSections = document.querySelectorAll('[id^="gaming-banners-section-"]');
            bannerSections.forEach(section => {
                if (section && this.observer) {
                    this.observer.observe(section);
                }
            });
        }
    }

    // Initialize the manager
    new GamingBannersManager();

    // Performance optimization for low-end devices
    const isLowEndDevice = navigator.hardwareConcurrency <= 2 ||
                          navigator.deviceMemory <= 2;

    if (isLowEndDevice) {
        const style = document.createElement('style');
        style.textContent = `
            .gaming-particle,
            .gaming-glow {
                display: none !important;
            }
            .gaming-banner-image {
                transition: none !important;
            }
        `;
        document.head.appendChild(style);
    }
});
</script>

