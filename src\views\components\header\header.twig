{#
| Variable         | Type    | Description                     |
|------------------|---------|---------------------------------|
| cart             | object  |                                 |
| cart.items_count | int     |                                 |
| cart.total       | string  | Formatted total ex: "١٠٠ ر.س"   |
#}
{% set important_links = theme.settings.get('important_links') %}
<header class="store-header">
  {# Top Nav #}
  <div class="top-navbar">
    <div class="container flex justify-between">
      <div class="flex-1 flex items-center gap-2">
          {# Footer Menu #}
          {% if important_links %}
            <salla-menu source="footer" topnav></salla-menu>
          {% endif %}

          {# Language & Currency Component #}
          <div class="header-buttons">
              {% if store.settings.is_multilingual or store.settings.currencies_enabled %}
                  <button type="button" onclick="salla.event.dispatch('localization::open')" class="btn--rounded-gray basis-0">
                      <span class="flag iti__flag iti__{{ user.language.country_code }} rounded-sm"></span>
                      <span class="mx-1.5">|</span> <span>{{ currency.symbol }}</span>
                  </button>
                  <salla-localization-modal></salla-localization-modal>
              {% endif %}
          </div>

          {# Scopes | Branches #}
          {% if store.scope %}
              <button class="btn--rounded-gray"
                      onclick="salla.event.dispatch('scopes::open', {'mode': 'default'})">
                  <i class="sicon-location rtl:ml-1 ltr:mr-1"></i> {{ store.scope.name }}
              </button>
          {% endif %}

          {# Search bar #}
          <div class="header-search flex-1">
            <salla-search inline oval height="36"></salla-search>
          </div>
      </div>

      <salla-contacts is-header></salla-contacts>
    </div>
  </div>

  {# Main Nav #}
  <div id="mainnav" class="main-nav-container gaming-header-nav">
      <div class="inner">
          <div class="container">
              {# Mobile Layout #}
              <div class="flex items-stretch justify-between relative lg:hidden">
                  <div class="flex items-center">
                      <a class="mburger mburger--collapse leading-none rtl:ml-4 ltr:mr-4" href="#mobile-menu" aria-label="Open-menu">
                        <i class="sicon-menu text-primary text-2xl"></i>
                      </a>
                      <a class="navbar-brand" href="{{ store.url }}">
                          <img fetchpriority="high" width="100%" height="100%" loading="eager" src="{{ store.logo }}" alt="{{ store.name }} logo">
                          {% if is_page('index') %}
                            <h1 class="sr-only">{{ store.name }}</h1>
                          {% else %}
                            <h2 class="sr-only">{{ store.name }}</h2>
                          {% endif %}
                      </a>
                  </div>
                  <div class="flex items-center justify-end gap-2">
                      {# <custom-categories-dropdown></custom-categories-dropdown> #}
                      {% if user.type=='user' %}
                        <salla-user-menu avatar-only show-header></salla-user-menu>
                      {% else %}
                          <button class="header-btn" aria-label="user-icon" onclick="salla.event.dispatch('login::open')">
                            <i class="header-btn__icon sicon-user-circle"></i>
                          </button>
                      {% endif %}
                      <salla-cart-summary class="ml-2 rtl:ml-[unset] rtl:mr-2">
                        <i slot="icon" class="header-btn__icon icon sicon-shopping-bag"></i>
                      </salla-cart-summary>
                  </div>
              </div>

              {# Desktop Layout #}
              <div class="hidden lg:block">
                  {# Top Row: Cart - Logo - Profile Icon #}
                  <div class="flex items-center justify-between py-4">
                      {# Cart & Categories - Left Side #}
                      <div class="flex items-center gap-4">
                          {# <custom-categories-dropdown></custom-categories-dropdown> #}
                          <salla-cart-summary>
                            <i slot="icon" class="header-btn__icon icon sicon-shopping-bag"></i>
                          </salla-cart-summary>
                      </div>

                      {# Logo - Center #}
                      <div class="flex justify-center">
                          <a class="navbar-brand" href="{{ store.url }}">
                              <img fetchpriority="high" width="100%" height="100%" loading="eager" src="{{ store.logo }}" alt="{{ store.name }} logo">
                              {% if is_page('index') %}
                                <h1 class="sr-only">{{ store.name }}</h1>
                              {% else %}
                                <h2 class="sr-only">{{ store.name }}</h2>
                              {% endif %}
                          </a>
                      </div>

                      {# Profile Icon - Right Side #}
                      <div class="flex items-center">
                          {% if user.type=='user' %}
                            <salla-user-menu avatar-only show-header></salla-user-menu>
                          {% else %}
                              <button class="header-btn" aria-label="user-icon" onclick="salla.event.dispatch('login::open')">
                                <i class="header-btn__icon sicon-user-circle"></i>
                              </button>
                          {% endif %}
                      </div>
                  </div>

                  {# Bottom Row: Main Menu - Center #}
                  <div class="flex justify-center border-t border-gaming-border pt-4">
                      <custom-main-menu></custom-main-menu>
                  </div>
              </div>
          </div>
      </div>
  </div>
</header>
{% if store.scope %}
    <salla-scopes selection="{{ store.scope.display_as == 'popup' ? 'mandatory' : 'optional' }}"></salla-scopes>
{% endif %}