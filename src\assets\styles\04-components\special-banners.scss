/* Special Banners Component - Optimized Performance */
.special-banners {
    position: relative;
    padding: 2rem 0;
    contain: layout style;

    @media (min-width: 768px) {
        padding: 3rem 0;
    }
}

.container-fluid {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding: 0 1rem;
}

.special-banners__container {
    scroll-behavior: smooth;
    position: relative;

    > div {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
        gap: 1.5rem;
        padding-bottom: 1rem;
        scroll-snap-type: x mandatory;
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
            display: none;
        }
    }
}

/* Banner Item Styles */
.special-banner-item {
    position: relative;
    flex-shrink: 0;
    width: 100%;
    scroll-snap-align: center;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0;
    transform: translateY(20px);
    will-change: opacity, transform;
    backface-visibility: hidden;

    &.animated {
        opacity: 1;
        transform: translateY(0);
    }

    // Responsive widths
    @media (min-width: 768px) {
        width: 100%;
    }

    @media (min-width: 1024px) {
        width: 50%;
    }

    @media (min-width: 1280px) {
        width: 33.333333%;
    }

    @media (max-width: 767px) {
        width: 85%;
    }

    // Banner card container
    > div {
        display: flex;
        flex-direction: column;
        height: 100%;
        border-radius: 0.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        position: relative;
        background: #fff;
        transition: box-shadow 0.3s ease, transform 0.3s ease;

        &:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            transform: translateY(-2px);
        }
    }
}

/* Top Image Styles */
.special-banner-top-image {
    height: 6rem;
    position: absolute;
    top: 0;
    right: 0;
    width: 33.333333%;
    z-index: 10;
    transform: translate(-10%, -30%) scale(0.8);
    transition: opacity 0.6s ease 0.3s, transform 0.6s ease 0.3s;
    opacity: 0;
    will-change: opacity, transform;

    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        transition: all 0.7s ease;
        transition-delay: 0.3s;
    }

    // Default badge when no image
    > div {
        background-color: var(--color-primary, #0066cc);
        border-radius: 50%;
        width: 6rem;
        height: 6rem;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

        span {
            color: white;
            font-weight: bold;
            font-size: 0.875rem;
        }
    }
}

.special-banner-item.animated .special-banner-top-image {
    opacity: 1;
    transform: translate(-10%, -30%) scale(1);
}

/* Main Image Styles */
.special-banner-image {
    height: 12rem;
    opacity: 0;
    transform: translateX(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
    overflow: hidden;
    position: relative;
    background: #f3f4f6;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        will-change: transform;
        backface-visibility: hidden;

        &.lazy {
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        &.loaded {
            opacity: 1;
        }
    }

    // Placeholder when no image
    > div {
        background-color: #e5e7eb;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        span {
            color: #6b7280;
            font-size: 0.875rem;
        }
    }
}

.special-banner-item.animated .special-banner-image {
    opacity: 1;
    transform: translateX(0);
}

.special-banner-item:hover .special-banner-image img {
    transform: scale(1.05);
}

/* Content Styles */
.special-banner-content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    opacity: 0;
    transform: translateX(-20px);
    transition: opacity 0.6s ease 0.2s, transform 0.6s ease 0.2s;

    h3 {
        font-size: 1.25rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        color: #1f2937;
        line-height: 1.3;
    }

    p {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 1rem;
        flex-grow: 1;
        line-height: 1.5;
    }

    a {
        display: inline-block;
        background-color: var(--color-primary, #0066cc);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        text-align: center;
        font-weight: 500;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        &:hover {
            background-color: var(--color-primary-dark, #004c99);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

            &::before {
                left: 100%;
            }
        }
    }
}

.special-banner-item.animated .special-banner-content {
    opacity: 1;
    transform: translateX(0);
}

/* Scroll Buttons */
.scroll-buttons {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
    gap: 0.5rem;
}

.scroll-btn {
    background-color: #e5e7eb;
    border: none;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6b7280;

    &:hover:not(:disabled) {
        background-color: #d1d5db;
        color: #374151;
        transform: scale(1.05);
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    svg {
        width: 1.5rem;
        height: 1.5rem;
        stroke-width: 2;
    }
}

/* Section Title */
.section-title {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
    font-weight: bold;
    color: #1f2937;

    @media (max-width: 767px) {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }
}

/* Loading States */
.special-banner-image.loading {
    &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 2rem;
        height: 2rem;
        margin: -1rem 0 0 -1rem;
        border: 2px solid #e5e7eb;
        border-top: 2px solid var(--color-primary, #0066cc);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
    .special-banner-item,
    .special-banner-image,
    .special-banner-content,
    .special-banner-top-image,
    .scroll-btn {
        transition: none !important;
        animation: none !important;
    }

    .special-banner-item:hover .special-banner-image img {
        transform: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .special-banner-item > div {
        background: #1f2937;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
    }

    .special-banner-content {
        h3 {
            color: #f9fafb;
        }

        p {
            color: #d1d5db;
        }
    }

    .scroll-btn {
        background-color: #374151;
        color: #d1d5db;

        &:hover:not(:disabled) {
            background-color: #4b5563;
            color: #f9fafb;
        }
    }
}
