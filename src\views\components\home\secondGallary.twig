{#
| Variable                        | Type     | Description                                     |
|---------------------------------|----------|-------------------------------------------------|
| component.title                 | string   | Gallery title                                   |
| component.title_font_size       | string   | Title font size (small, medium, large, extra-large) |
| component.title_font_weight     | string   | Title font weight (normal, medium, bold, extra-bold) |
| component.title_text_alignment  | string   | Title text alignment (right, center, left)     |
| component.title_text_color      | string   | Title text color                                |
| component.enable_background     | boolean  | Enable/disable background color                 |
| component.background_color      | string   | Background color (when enabled)                 |
| component.hover_overlay_color   | string   | Hover overlay color                             |
| component.glow_border_color     | string   | Glow border color                               |
| component.animation_type        | string   | Title animation type                            |
| component.gallery_animation_type| string   | Gallery items animation type                    |
| component.animation_duration    | string   | Animation duration                              |
| component.animation_delay       | string   | Animation delay                                 |
| component.items_per_row         | string   | Items per row                                   |
| component.top_margin            | number   | Top margin in pixels                            |
| component.bottom_margin         | number   | Bottom margin in pixels                         |
| component.full_width            | boolean  | Full width toggle                               |
| component.enable_hover_effects  | boolean  | Enable hover effects                            |
| component.hover_animation_type  | string   | Hover animation type                            |
| component.image_border_radius   | number   | Image border radius in pixels                   |
| component.enable_image_shadows  | boolean  | Enable image shadows                            |
| component.enable_glow_effects   | boolean  | Enable glow effects                             |
| component.icon                  | array    | Array of icons to display                       |
| component.icon[].image          | string   | Icon image URL                                  |
| component.icon[].title          | string   | Icon title                                      |
| component.icon[].url            | object   | Icon link configuration                         |
#}

{# Styles moved to separate SCSS file: src/assets/styles/04-components/second-gallery.scss #}
<!-- All styles moved to separate SCSS file for better performance and maintainability -->

{# Configuration Variables #}
{% set component_class = component_class|default('s-block') %}
{% set title = component.title %}
{% set icon_items = component.icon %}
{% set unique_id = 'gallery-' ~ random() %}

{# Text Styling Configuration #}
{% set title_font_size = component.title_font_size|default('large') %}
{% set title_font_weight = component.title_font_weight|default('bold') %}
{% set title_text_alignment = component.title_text_alignment|default('center') %}

{# Color Configuration #}
{% set title_text_color = component.title_text_color|default('#00ff88') %}
{% set enable_background = component.enable_background|default(false) %}
{% set background_color = component.background_color|default('#0a0a0a') %}
{% set hover_overlay_color = component.hover_overlay_color|default('#00ff88') %}
{% set glow_border_color = component.glow_border_color|default('#00d4ff') %}

{# Animation Configuration #}
{% set animation_type = component.animation_type|default('fadeIn') %}
{% set gallery_animation_type = component.gallery_animation_type|default('slideUp') %}
{% set animation_duration = component.animation_duration|default('medium') %}
{% set animation_delay = component.animation_delay|default('short') %}

{# Layout Configuration #}
{% set items_per_row = component.items_per_row|default('3') %}
{% set top_margin = component.top_margin|default(80) %}
{% set bottom_margin = component.bottom_margin|default(80) %}
{% set full_width = component.full_width|default(false) %}

{# Visual Effects Configuration #}
{% set enable_hover_effects = component.enable_hover_effects|default(true) %}
{% set hover_animation_type = component.hover_animation_type|default('scale') %}
{% set image_border_radius = component.image_border_radius|default(15) %}
{% set enable_image_shadows = component.enable_image_shadows|default(true) %}
{% set enable_glow_effects = component.enable_glow_effects|default(true) %}

{# Logic Configuration #}
{% set has_slider = icon_items|length > items_per_row|number_format %}

{# Build CSS classes based on settings #}
{% set gallery_classes = 'gaming-gallery-section enhanced-second-gallery' %}
{% set gallery_classes = gallery_classes ~ ' font-size-' ~ title_font_size %}
{% set gallery_classes = gallery_classes ~ ' font-weight-' ~ title_font_weight %}
{% set gallery_classes = gallery_classes ~ ' text-align-' ~ title_text_alignment %}
{% set gallery_classes = gallery_classes ~ ' animation-' ~ animation_type %}
{% set gallery_classes = gallery_classes ~ ' gallery-animation-' ~ gallery_animation_type %}
{% set gallery_classes = gallery_classes ~ ' duration-' ~ animation_duration %}
{% set gallery_classes = gallery_classes ~ ' delay-' ~ animation_delay %}
{% set gallery_classes = gallery_classes ~ ' items-per-row-' ~ items_per_row %}
{% if enable_hover_effects %}
    {% set gallery_classes = gallery_classes ~ ' hover-effects-enabled hover-' ~ hover_animation_type %}
{% endif %}
{% if enable_image_shadows %}
    {% set gallery_classes = gallery_classes ~ ' shadows-enabled' %}
{% endif %}
{% if enable_glow_effects %}
    {% set gallery_classes = gallery_classes ~ ' glow-enabled' %}
{% endif %}
{% if full_width %}
    {% set gallery_classes = gallery_classes ~ ' full-width-enabled' %}
{% endif %}

{# Build inline styles #}
{% set section_styles = [] %}
{% set section_styles = section_styles|merge(['padding-top: ' ~ top_margin ~ 'px']) %}
{% set section_styles = section_styles|merge(['padding-bottom: ' ~ bottom_margin ~ 'px']) %}
{% if enable_background %}
    {% set section_styles = section_styles|merge(['background-color: ' ~ background_color]) %}
{% endif %}

{# Build title styles #}
{% set title_styles = [] %}
{% set title_styles = title_styles|merge(['color: ' ~ title_text_color]) %}

{# Build CSS custom properties for dynamic styling #}
{% set css_properties = [] %}
{% set css_properties = css_properties|merge(['--title-color: ' ~ title_text_color]) %}
{% set css_properties = css_properties|merge(['--hover-overlay-color: ' ~ hover_overlay_color]) %}
{% set css_properties = css_properties|merge(['--glow-border-color: ' ~ glow_border_color]) %}
{% set css_properties = css_properties|merge(['--image-border-radius: ' ~ image_border_radius ~ 'px']) %}

<section class="{{ component_class }} {{ gallery_classes }}"
         id="{{ unique_id }}"
         data-component="second-gallery"
         data-title-animation="{{ animation_type }}"
         data-gallery-animation="{{ gallery_animation_type }}"
         data-animation-duration="{{ animation_duration }}"
         data-animation-delay="{{ animation_delay }}"
         data-items-per-row="{{ items_per_row }}"
         data-enable-hover-effects="{{ enable_hover_effects ? 'true' : 'false' }}"
         data-hover-animation-type="{{ hover_animation_type }}"
         data-image-border-radius="{{ image_border_radius }}"
         data-enable-image-shadows="{{ enable_image_shadows ? 'true' : 'false' }}"
         data-enable-glow-effects="{{ enable_glow_effects ? 'true' : 'false' }}"
         style="{{ section_styles|join('; ') }}; {{ css_properties|join('; ') }};">

    <div class="gaming-gallery-container {% if full_width %}full-width{% endif %}">
        <div class="gaming-gallery-title-wrapper">
            <h2 class="gaming-gallery-title"
                data-text="{{ title }}"
                style="{{ title_styles|join('; ') }};">{{ title }}</h2>
            <div class="gaming-title-underline"></div>
        </div>

        <div class="gaming-gallery-slider {% if has_slider %}with-slider{% endif %} items-{{ items_per_row }}"
             id="gaming-gallery-slider-{{ unique_id }}"
             data-items-per-row="{{ items_per_row }}">
            <div class="gaming-gallery-track">
                {% for item in icon_items %}
                    {% set item_url = null %}
                    {% if item.url is defined and item.url %}
                        {% if item.url.type is defined %}
                            {% set item_url = item.url.url %}
                        {% else %}
                            {% set item_url = item.url %}
                        {% endif %}
                    {% endif %}

                    <div class="gaming-gallery-item">
                        {% if item_url %}
                            <a href="{{ item_url }}" class="gaming-gallery-link" aria-label="انتقل إلى {{ item.title }}" target="_blank" rel="noopener">
                        {% endif %}

                        <div class="gaming-gallery-image-wrapper">
                            <div class="gaming-gallery-image-glow"></div>
                            <div class="gaming-gallery-image-container">
                                <img src="{{ item.image }}"
                                     alt="{{ item.title }}"
                                     class="gaming-gallery-image"
                                     decoding="async"
                                     width="400"
                                     height="250"
                                     style="opacity: 1 !important; visibility: visible !important; display: block !important;">
                            </div>
                            {% if item_url %}
                                <div class="gaming-gallery-overlay">
                                    <div class="gaming-overlay-content">
                                        <i class="sicon-link gaming-gallery-link-icon"></i>
                                        <span class="gaming-overlay-text">عرض التفاصيل</span>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        <h3 class="gaming-gallery-item-title">{{ item.title }}</h3>

                        {% if item_url %}
                            </a>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
            
            {% if has_slider %}
                <div class="gaming-gallery-nav">
                    <button class="gaming-gallery-prev-btn"
                            aria-label="الشريحة السابقة"
                            data-gallery="{{ unique_id }}">
                        <i class="sicon-chevron-left"></i>
                    </button>
                    <div class="gaming-gallery-dots">
                        {% for i in 0..((icon_items|length / items_per_row|number_format)|round(0, 'ceil') - 1) %}
                            <button class="gaming-gallery-dot {% if loop.first %}active{% endif %}"
                                    data-index="{{ i }}"
                                    data-gallery="{{ unique_id }}"
                                    aria-label="انتقل إلى الشريحة {{ i + 1 }}"></button>
                        {% endfor %}
                    </div>
                    <button class="gaming-gallery-next-btn"
                            aria-label="الشريحة التالية"
                            data-gallery="{{ unique_id }}">
                        <i class="sicon-chevron-right"></i>
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</section>

{# Load optimized JavaScript #}
<script src="{{ 'assets/js/components/second-gallery.js' | asset }}" defer></script>
<script src="{{ 'assets/js/components/second-gallery-enhanced.js' | asset }}" defer></script>


