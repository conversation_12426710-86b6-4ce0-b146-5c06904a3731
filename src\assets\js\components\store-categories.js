/**
 * Store Categories Component JavaScript
 * Gaming Theme with Animations and Interactions
 */

class StoreCategoriesComponent {
    constructor() {
        this.observer = null;
        this.categoryCards = [];
        this.categoriesSection = null;
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onReady());
        } else {
            this.onReady();
        }
    }

    onReady() {
        this.categoriesSection = document.querySelector('.s-block--store-categories');
        this.categoryCards = document.querySelectorAll('.category-card');
        this.categoriesGrid = document.querySelector('.categories-grid');
        this.categoriesSlider = document.querySelector('.categories-slider');

        if (this.categoriesSection) {
            this.setupLazyLoadingAnimation();
            this.setupHoverEffects();
            this.setupAccessibility();
            this.setupResponsiveLayout();
        }
    }

    setupLazyLoadingAnimation() {
        // Create intersection observer for lazy loading animation
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerCategoryCardsAnimation(entry.target);
                    this.observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px 0px'
        });

        this.observer.observe(this.categoriesSection);
    }

    triggerCategoryCardsAnimation(target) {
        const categoryCards = target.querySelectorAll('.category-card');
        
        categoryCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.animationPlayState = 'running';
                card.classList.add('animate-in');
            }, index * 200);
        });
    }

    setupHoverEffects() {
        this.categoryCards.forEach(card => {
            // Enhanced hover effects
            card.addEventListener('mouseenter', (e) => this.onCardHover(e));
            card.addEventListener('mouseleave', (e) => this.onCardLeave(e));
            
            // Touch support for mobile
            card.addEventListener('touchstart', (e) => this.onCardTouch(e));
        });
    }

    onCardHover(event) {
        const card = event.currentTarget;
        
        // Add enhanced hover transform
        card.style.transform = 'translateY(-10px) scale(1.02)';
        card.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        
        // Add glow effect to image
        const image = card.querySelector('.category-image');
        if (image) {
            image.style.filter = 'brightness(1.1) contrast(1.1)';
        }
        
        // Add text glow effect
        const name = card.querySelector('.category-name');
        if (name) {
            name.style.textShadow = '0 0 15px rgba(29, 233, 182, 0.8)';
        }
    }

    onCardLeave(event) {
        const card = event.currentTarget;
        
        // Reset transforms
        card.style.transform = 'translateY(0) scale(1)';
        
        // Reset image effects
        const image = card.querySelector('.category-image');
        if (image) {
            image.style.filter = 'none';
        }
        
        // Reset text effects
        const name = card.querySelector('.category-name');
        if (name) {
            name.style.textShadow = 'none';
        }
    }

    onCardTouch(event) {
        // Add touch feedback for mobile devices
        const card = event.currentTarget;
        card.style.transform = 'scale(0.98)';
        
        setTimeout(() => {
            card.style.transform = 'scale(1)';
        }, 150);
    }

    setupAccessibility() {
        // Add keyboard navigation support
        this.categoryCards.forEach(card => {
            // Make cards focusable
            if (!card.hasAttribute('tabindex')) {
                card.setAttribute('tabindex', '0');
            }
            
            // Add keyboard event listeners
            card.addEventListener('keydown', (e) => this.onCardKeydown(e));
            card.addEventListener('focus', (e) => this.onCardFocus(e));
            card.addEventListener('blur', (e) => this.onCardBlur(e));
        });
    }

    onCardKeydown(event) {
        const card = event.currentTarget;
        
        // Handle Enter and Space key presses
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            
            // Find the link inside the card and trigger click
            const link = card.querySelector('a') || card;
            if (link.href) {
                window.location.href = link.href;
            }
        }
    }

    onCardFocus(event) {
        const card = event.currentTarget;
        
        // Add focus styles
        card.style.outline = '2px solid var(--color-primary)';
        card.style.outlineOffset = '2px';
        
        // Trigger hover effect on focus
        this.onCardHover(event);
    }

    onCardBlur(event) {
        const card = event.currentTarget;
        
        // Remove focus styles
        card.style.outline = 'none';
        
        // Remove hover effect on blur
        this.onCardLeave(event);
    }

    setupResponsiveLayout() {
        // Handle responsive behavior for grid vs slider
        if (this.categoriesGrid) {
            this.setupGridLayout();
        }

        if (this.categoriesSlider) {
            this.setupSliderLayout();
        }
    }

    setupGridLayout() {
        // Add responsive classes and behaviors for grid layout
        const categoryCount = this.categoriesGrid.children.length;

        // Ensure proper grid class is applied
        this.categoriesGrid.classList.add(`categories-${categoryCount}`);

        // Add staggered animation delays for grid items
        Array.from(this.categoriesGrid.children).forEach((card, index) => {
            card.style.animationDelay = `${0.3 + (index * 0.2)}s`;
        });
    }

    setupSliderLayout() {
        // Add specific behaviors for slider layout
        const sliderCards = this.categoriesSlider.querySelectorAll('.category-card');

        // Add staggered animation delays for slider items
        sliderCards.forEach((card, index) => {
            card.style.animationDelay = `${0.3 + (index * 0.1)}s`;
        });

        // Listen for slider events if needed
        this.setupSliderEvents();
    }

    setupSliderEvents() {
        // Add event listeners for slider-specific interactions
        const swallaSlider = this.categoriesSlider.closest('salla-slider');

        if (swallaSlider) {
            // Listen for slide change events
            swallaSlider.addEventListener('slideChange', (e) => {
                // Re-trigger animations for visible slides if needed
                this.handleSlideChange(e);
            });
        }
    }

    handleSlideChange(event) {
        // Handle slide change animations
        const visibleCards = event.target.querySelectorAll('.swiper-slide-active .category-card, .swiper-slide-next .category-card');

        visibleCards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('slide-visible');
            }, index * 100);
        });
    }

    // Method to refresh component if categories are dynamically updated
    refresh() {
        this.categoryCards = document.querySelectorAll('.category-card');
        this.categoriesGrid = document.querySelector('.categories-grid');
        this.categoriesSlider = document.querySelector('.categories-slider');

        this.setupHoverEffects();
        this.setupAccessibility();
        this.setupResponsiveLayout();
    }

    // Cleanup method
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
        
        // Remove event listeners
        this.categoryCards.forEach(card => {
            card.removeEventListener('mouseenter', this.onCardHover);
            card.removeEventListener('mouseleave', this.onCardLeave);
            card.removeEventListener('touchstart', this.onCardTouch);
            card.removeEventListener('keydown', this.onCardKeydown);
            card.removeEventListener('focus', this.onCardFocus);
            card.removeEventListener('blur', this.onCardBlur);
        });
    }
}

// Initialize the component
const storeCategoriesComponent = new StoreCategoriesComponent();

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StoreCategoriesComponent;
}
