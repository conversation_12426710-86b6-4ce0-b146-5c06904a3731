# دليل تخصيص ألوان وتصميم كومبوننت الخرائط

## نظرة عامة
تم إضافة إعدادات شاملة لتخصيص ألوان وتصميم كومبوننت الخرائط (Store Map Component) في الثيم. هذه الإعدادات تتيح للمستخدمين التحكم الكامل في مظهر الكومبوننت.

## الإعدادات المتاحة

### إعدادات التصميم والألوان

#### 1. إعدادات الخلفية
- **تفعيل خلفية الكومبوننت**: تشغيل/إيقاف خلفية الكومبوننت (افتراضياً بدون خلفية)
- **لون الخلفية**: اختيار لون خلفية الكومبوننت (افتراضي: #f8f9fa)

#### 2. إعدادات الألوان
- **لون العنوان الرئيسي**: لون نص العنوان الرئيسي (افتراضي: #212529)
- **لون نص الوصف**: لون نص الوصف التفصيلي (افتراضي: #6c757d)
- **لون اسم الفرع**: لون نص اسم الفرع (افتراضي: #495057)
- **لون عنوان الفرع**: لون نص عنوان الفرع (افتراضي: #6c757d)

#### 3. إعدادات الحدود
- **تفعيل الحدود**: إظهار حدود حول الكومبوننت
- **لون الحدود**: لون حدود الكومبوننت (افتراضي: #dee2e6)
- **انحناء الحدود**: مستوى انحناء زوايا الكومبوننت (0-50 بكسل، افتراضي: 8)

#### 4. إعدادات المسافات الداخلية
- **تفعيل المسافات الداخلية**: إضافة مسافات داخلية للكومبوننت
- **حجم المسافات الداخلية**: 
  - صغير (2rem)
  - متوسط (4rem) - افتراضي
  - كبير (6rem)
  - كبير جداً (8rem)

## الملفات المضافة

### 1. ملفات التكوين
- `twilight.json`: إضافة إعدادات الألوان والتصميم

### 2. ملفات CSS
- `assets/css/google-maps-styling.css`: أنماط أساسية للكومبوننت
- `src/assets/styles/04-components/store-map-custom-styling.scss`: أنماط متقدمة ومخصصة

### 3. ملفات JavaScript
- `assets/js/google-maps-styling.js`: معالج الأنماط الأساسي
- `src/assets/js/components/store-map-styling.js`: معالج أنماط الكومبوننت
- `src/assets/js/theme-settings/store-map-colors.js`: معالج إعدادات الثيم

### 4. ملفات Twig المحدثة
- `src/views/components/home/<USER>

## كيفية الاستخدام

### 1. في لوحة تحكم الثيم
1. انتقل إلى إعدادات الثيم
2. ابحث عن قسم "Store Map Component"
3. اضبط الإعدادات حسب التفضيل:
   - فعّل/أوقف الخلفية
   - اختر الألوان المناسبة
   - اضبط الحدود والمسافات

### 2. في الكود
```twig
{# مثال على استخدام الإعدادات في ملف twig #}
<div class="s-block--store-map background-enabled border-enabled padding-medium"
     style="
        background-color: {{ component.background_color ?: '#f8f9fa' }};
        border: 1px solid {{ component.border_color ?: '#dee2e6' }};
        --title-color: {{ component.title_color ?: '#212529' }};
     ">
```

### 3. تخصيص CSS إضافي
```scss
// إضافة أنماط مخصصة
.s-block--store-map.custom-style {
  --component-title-color: #your-color;
  --component-bg-color: #your-bg-color;
}
```

## الميزات المتقدمة

### 1. دعم الوضع المظلم
الكومبوننت يدعم الوضع المظلم تلقائياً مع ألوان محسنة.

### 2. دعم التباين العالي
تحسينات خاصة للمستخدمين الذين يحتاجون تباين عالي.

### 3. التصميم المتجاوب
جميع الإعدادات تعمل بشكل مثالي على جميع أحجام الشاشات.

### 4. دعم الطباعة
أنماط محسنة للطباعة مع ألوان مناسبة.

## الأنماط المدعومة

### 1. أنماط الخلفية
- `background-enabled`: خلفية مفعلة
- `background-disabled`: خلفية معطلة

### 2. أنماط الحدود
- `border-enabled`: حدود مفعلة
- `border-disabled`: حدود معطلة

### 3. أنماط المسافات
- `padding-small`: مسافات صغيرة
- `padding-medium`: مسافات متوسطة
- `padding-large`: مسافات كبيرة
- `padding-xlarge`: مسافات كبيرة جداً
- `padding-disabled`: بدون مسافات

## متغيرات CSS المخصصة

```css
:root {
  --component-title-color: #212529;
  --component-description-color: #6c757d;
  --component-branch-name-color: #495057;
  --component-branch-address-color: #6c757d;
  --component-bg-color: #f8f9fa;
  --component-border-color: #dee2e6;
}
```

## الدعم والتوافق

- ✅ جميع المتصفحات الحديثة
- ✅ التصميم المتجاوب
- ✅ دعم الوضع المظلم
- ✅ دعم التباين العالي
- ✅ دعم الطباعة
- ✅ دعم إمكانية الوصول

## ملاحظات مهمة

1. **الافتراضي بدون خلفية**: الكومبوننت افتراضياً بدون خلفية للحفاظ على التصميم الأصلي
2. **التوافق مع الثيم**: جميع الإعدادات متوافقة مع نظام الألوان الحالي للثيم
3. **الأداء**: جميع الأنماط محسنة للأداء مع انتقالات سلسة
4. **التخصيص**: يمكن تخصيص الألوان بسهولة من لوحة التحكم

## استكشاف الأخطاء

### المشكلة: الألوان لا تظهر
**الحل**: تأكد من تفعيل الإعدادات في لوحة التحكم

### المشكلة: الخلفية لا تظهر
**الحل**: تأكد من تفعيل "تفعيل خلفية الكومبوننت"

### المشكلة: التصميم لا يتجاوب
**الحل**: تأكد من تضمين ملفات CSS المطلوبة
