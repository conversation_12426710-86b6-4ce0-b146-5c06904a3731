/**
 * Enhanced Special Vertical Gallery Component
 * Gaming-themed gallery with comprehensive configuration support
 * Features: Intersection Observer, Dynamic Styling, Performance Optimization
 */

(function() {
    'use strict';

    let isInitialized = false;
    let observer = null;
    let animatedElements = new Set();
    let configurations = new Map();

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    /**
     * Read configuration from data attributes
     */
    function readConfiguration(element) {
        return {
            galleryAnimation: element.dataset.galleryAnimation || 'fadeIn',
            itemAnimation: element.dataset.itemAnimation || 'slideUp',
            textAnimation: element.dataset.textAnimation || 'trackingExpand',
            animationDuration: element.dataset.animationDuration || 'medium',
            animationDelay: element.dataset.animationDelay || 'short',
            hoverAnimation: element.dataset.hoverAnimation || 'scale',
            enableParticles: element.dataset.enableParticles === 'true'
        };
    }

    /**
     * Apply dynamic styling based on configuration
     */
    function applyDynamicStyling(element, config) {
        // Store configuration for later use
        configurations.set(element.id, config);

        // Apply CSS custom properties for animation timing
        const durationMap = {
            'fast': '0.5s',
            'medium': '1s',
            'slow': '1.5s',
            'extra-slow': '2s'
        };

        element.style.setProperty('--animation-duration', durationMap[config.animationDuration] || '1s');

        // Initialize particles if enabled
        if (config.enableParticles) {
            initializeParticles(element);
        }

        // Apply hover effects to gallery items
        const galleryItems = element.querySelectorAll('.gallery-item');
        galleryItems.forEach((item, index) => {
            // Set staggered animation delays
            const delay = calculateAnimationDelay(config.animationDelay, index);
            item.style.setProperty('--animation-delay', `${delay}s`);
            item.style.animationDelay = `${delay}s`;

            // Apply hover animation classes
            item.classList.add(`hover-animation-${config.hoverAnimation}`);
        });
    }

    /**
     * Calculate animation delay based on configuration and index
     */
    function calculateAnimationDelay(delayType, index) {
        const baseDelays = {
            'none': 0,
            'short': 0.2,
            'medium': 0.5,
            'long': 1
        };

        const baseDelay = baseDelays[delayType] || 0.2;
        return baseDelay + (index * 0.15);
    }

    /**
     * Initialize particle effects
     */
    function initializeParticles(element) {
        const particlesContainer = element.querySelector('.particles-container');
        if (!particlesContainer) return;

        const particles = particlesContainer.querySelectorAll('.particle');
        particles.forEach((particle, index) => {
            // Random positioning
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';

            // Random animation delay
            particle.style.animationDelay = (Math.random() * 2) + 's';

            // Random size variation
            const size = 2 + Math.random() * 4;
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';
        });
    }

    /**
     * Initialize the component
     */
    function init() {
        if (isInitialized) return;

        try {
            createObserver();
            observeGallerySections();
            setupEventListeners();
            isInitialized = true;
        } catch (error) {
            console.warn('Special Vertical Gallery initialization failed:', error);
        }
    }

    /**
     * Create intersection observer
     */
    function createObserver() {
        observer = new IntersectionObserver(handleIntersection, {
            threshold: 0.15,
            rootMargin: '50px 0px'
        });
    }

    /**
     * Observe gallery sections
     */
    function observeGallerySections() {
        const gallerySections = document.querySelectorAll('[id^="special-vertical-gallery-"]');

        gallerySections.forEach(section => {
            if (section && observer) {
                // Read configuration and apply dynamic styling
                const config = readConfiguration(section);
                applyDynamicStyling(section, config);

                // Observe for intersection
                observer.observe(section);
            }
        });
    }

    /**
     * Handle intersection observer events
     */
    function handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && !animatedElements.has(entry.target)) {
                animateElement(entry.target);
                animatedElements.add(entry.target);

                // Unobserve after animation to free up resources
                observer.unobserve(entry.target);
            }
        });
    }

    /**
     * Animate gallery element and its children
     */
    function animateElement(element) {
        // Get configuration for this element
        const config = configurations.get(element.id) || {};

        // Use requestAnimationFrame for smooth animations
        requestAnimationFrame(() => {
            element.classList.add('animate-in');

            // Animate child elements with configuration-based delays
            const animateElements = element.querySelectorAll('.animate-element');

            animateElements.forEach((child, index) => {
                const delay = calculateAnimationDelay(config.animationDelay, index);
                child.style.setProperty('--animation-delay', `${delay}s`);
                child.style.animationDelay = `${delay}s`;

                requestAnimationFrame(() => {
                    child.classList.add('animate-in');
                });
            });
        });
    }

    /**
     * Setup event listeners
     */
    function setupEventListeners() {
        // Handle image loading for better performance
        setupLazyImageLoading();

        // Setup mobile-specific optimizations
        setupMobileOptimizations();

        // Handle orientation changes
        window.addEventListener('orientationchange', handleOrientationChange);
    }

    /**
     * Setup mobile-specific optimizations
     */
    function setupMobileOptimizations() {
        // Detect if device is mobile
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

        if (isMobile || isTouchDevice) {
            // Add mobile class to body for CSS targeting
            document.body.classList.add('mobile-device');

            // Optimize animations for mobile
            const gallerySections = document.querySelectorAll('.special-vertical-gallery-section');
            gallerySections.forEach(section => {
                // Reduce animation complexity on mobile
                section.classList.add('mobile-optimized');

                // Disable particles on very small screens
                if (window.innerWidth < 480) {
                    section.classList.remove('particles-enabled');
                }
            });
        }
    }

    /**
     * Handle orientation changes
     */
    function handleOrientationChange() {
        // Debounce orientation change handling
        setTimeout(() => {
            const gallerySections = document.querySelectorAll('.special-vertical-gallery-section');
            gallerySections.forEach(section => {
                // Force reflow to handle layout changes
                section.style.display = 'none';
                section.offsetHeight; // Trigger reflow
                section.style.display = '';

                // Recalculate grid layout
                const grid = section.querySelector('.gallery-grid');
                if (grid) {
                    grid.style.display = 'none';
                    grid.offsetHeight; // Trigger reflow
                    grid.style.display = '';
                }
            });
        }, 100);
    }

    /**
     * Setup lazy image loading
     */
    function setupLazyImageLoading() {
        const images = document.querySelectorAll('.special-vertical-gallery-section img[data-src]');

        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for older browsers - load all images immediately
            images.forEach(img => {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            });
        }

        // Also handle any images that might not have data-src
        const directImages = document.querySelectorAll('.special-vertical-gallery-section img:not([data-src])');
        directImages.forEach(img => {
            if (!img.src && img.getAttribute('src')) {
                img.src = img.getAttribute('src');
            }
        });
    }
})();
