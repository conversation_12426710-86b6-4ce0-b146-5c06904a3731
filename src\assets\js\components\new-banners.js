/**
 * Enhanced Gaming Banners Component
 * Performance improvements:
 * - Single shared IntersectionObserver
 * - Debounced animations
 * - Memory leak prevention
 * - Reduced DOM queries
 * - Configuration-based dynamic styling
 */

class GamingBannersManager {
    constructor() {
        this.observer = null;
        this.animatedElements = new Set();
        this.isInitialized = false;
        this.configurations = new Map();

        // Bind methods to preserve context
        this.handleIntersection = this.handleIntersection.bind(this);
        this.init = this.init.bind(this);
        this.readConfiguration = this.readConfiguration.bind(this);
        this.applyDynamicStyling = this.applyDynamicStyling.bind(this);

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', this.init);
        } else {
            this.init();
        }
    }

    /**
     * Read configuration from data attributes
     */
    readConfiguration(element) {
        const config = {
            bannerAnimation: element.dataset.bannerAnimation || 'fadeIn',
            textAnimation: element.dataset.textAnimation || 'slideUp',
            animationDuration: element.dataset.animationDuration || 'medium',
            animationDelay: element.dataset.animationDelay || 'short',
            hoverAnimation: element.dataset.hoverAnimation || 'scale',
            enableHover: element.dataset.enableHover === 'true',
            enableParticles: element.dataset.enableParticles === 'true'
        };

        this.configurations.set(element.id, config);
        return config;
    }

    /**
     * Apply dynamic styling based on configuration
     */
    applyDynamicStyling(element, config) {
        // Apply CSS custom properties from inline styles
        const computedStyle = getComputedStyle(element);
        const titleColor = computedStyle.getPropertyValue('--title-color');
        const subtitleColor = computedStyle.getPropertyValue('--subtitle-color');
        const hoverOverlayColor = computedStyle.getPropertyValue('--hover-overlay-color');
        const glowBorderColor = computedStyle.getPropertyValue('--glow-border-color');
        const imageBorderRadius = computedStyle.getPropertyValue('--image-border-radius');

        // Apply colors to elements
        const titles = element.querySelectorAll('.gaming-banner-title');
        const subtitles = element.querySelectorAll('.gaming-banner-subtitle');
        const overlays = element.querySelectorAll('.gaming-banner-overlay');
        const images = element.querySelectorAll('.gaming-banner-image');

        titles.forEach(title => {
            if (titleColor) title.style.color = titleColor;
        });

        subtitles.forEach(subtitle => {
            if (subtitleColor) subtitle.style.color = subtitleColor;
        });

        overlays.forEach(overlay => {
            if (hoverOverlayColor) {
                overlay.style.background = `linear-gradient(45deg, ${hoverOverlayColor}22, transparent)`;
            }
        });

        images.forEach(image => {
            if (imageBorderRadius) {
                image.style.borderRadius = imageBorderRadius;
            }
        });

        // Apply animation classes based on configuration
        element.classList.add(`banner-animation-${config.bannerAnimation}`);
        element.classList.add(`text-animation-${config.textAnimation}`);
        element.classList.add(`animation-duration-${config.animationDuration}`);
        element.classList.add(`animation-delay-${config.animationDelay}`);
        element.classList.add(`hover-animation-${config.hoverAnimation}`);

        if (config.enableHover) {
            element.classList.add('hover-effects-enabled');
        }

        if (config.enableParticles) {
            element.classList.add('particles-enabled');
            this.initializeParticles(element);
        }
    }

    /**
     * Initialize particle effects
     */
    initializeParticles(element) {
        const particlesBg = element.querySelector('.particles-bg');
        if (!particlesBg) return;

        const particles = particlesBg.querySelectorAll('.particle');
        particles.forEach((particle, index) => {
            // Randomize particle positions
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';

            // Add random animation delay
            particle.style.animationDelay = (Math.random() * 2) + 's';
        });
    }
    
    init() {
        if (this.isInitialized) return;
        
        try {
            this.createObserver();
            this.observeBannerSections();
            this.isInitialized = true;
        } catch (error) {
            console.warn('Gaming banners initialization failed:', error);
        }
    }
    
    createObserver() {
        // Create single observer with optimized options
        this.observer = new IntersectionObserver(this.handleIntersection, {
            threshold: 0.15, // Reduced threshold for better performance
            rootMargin: '50px 0px', // Start animation slightly before element is visible
        });
    }
    
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && !this.animatedElements.has(entry.target)) {
                this.animateElement(entry.target);
                this.animatedElements.add(entry.target);
                
                // Unobserve after animation to free up resources
                this.observer.unobserve(entry.target);
            }
        });
    }
    
    animateElement(element) {
        // Get configuration for this element
        const config = this.configurations.get(element.id) || {};

        // Use requestAnimationFrame for smooth animations
        requestAnimationFrame(() => {
            element.classList.add('animate-in');

            // Animate child elements with configuration-based delays
            const animateElements = element.querySelectorAll('.animate-element');

            // Calculate delay based on configuration
            let baseDelay = 0.2;
            switch (config.animationDelay) {
                case 'none':
                    baseDelay = 0;
                    break;
                case 'short':
                    baseDelay = 0.2;
                    break;
                case 'medium':
                    baseDelay = 0.5;
                    break;
                case 'long':
                    baseDelay = 1;
                    break;
            }

            animateElements.forEach((child, index) => {
                // Use CSS custom properties for better performance
                const delay = baseDelay + (index * 0.15);
                child.style.setProperty('--animation-delay', `${delay}s`);
                child.style.animationDelay = `${delay}s`;

                requestAnimationFrame(() => {
                    child.classList.add('animate-in');
                });
            });
        });
    }
    
    observeBannerSections() {
        // Find all banner sections efficiently
        const bannerSections = document.querySelectorAll('[id^="gaming-banners-section-"]');

        bannerSections.forEach(section => {
            if (section && this.observer) {
                // Read configuration and apply dynamic styling
                const config = this.readConfiguration(section);
                this.applyDynamicStyling(section, config);

                // Observe for intersection
                this.observer.observe(section);
            }
        });
    }
    
    // Cleanup method for memory management
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
        
        this.animatedElements.clear();
        this.isInitialized = false;
    }
}

// Performance optimization: Use passive event listeners
const addPassiveEventListener = (element, event, handler) => {
    element.addEventListener(event, handler, { passive: true });
};

// Initialize the manager
let gamingBannersManager;

// Lazy initialization to avoid blocking main thread
const initializeGamingBanners = () => {
    if (!gamingBannersManager) {
        gamingBannersManager = new GamingBannersManager();
    }
};

// Use intersection observer to only initialize when needed
const lazyInitObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            initializeGamingBanners();
            lazyInitObserver.disconnect(); // Clean up after initialization
        }
    });
}, { rootMargin: '100px' });

// Start observing for banner sections
document.addEventListener('DOMContentLoaded', () => {
    const firstBannerSection = document.querySelector('[id^="gaming-banners-section-"]');
    if (firstBannerSection) {
        lazyInitObserver.observe(firstBannerSection);
    }
});

// Cleanup on page unload to prevent memory leaks
window.addEventListener('beforeunload', () => {
    if (gamingBannersManager) {
        gamingBannersManager.destroy();
    }
    lazyInitObserver.disconnect();
});

// Performance monitoring (optional)
if (window.performance && window.performance.mark) {
    window.performance.mark('gaming-banners-script-loaded');
}

// Export for potential external use
window.GamingBannersManager = GamingBannersManager;

// Additional performance optimizations
const optimizeForLowEndDevices = () => {
    // Detect low-end devices
    const isLowEndDevice = navigator.hardwareConcurrency <= 2 ||
                          navigator.deviceMemory <= 2 ||
                          /Android.*Chrome\/[.0-9]*\s/.test(navigator.userAgent);

    if (isLowEndDevice) {
        // Disable animations on low-end devices
        const style = document.createElement('style');
        style.textContent = `
            .gaming-particle,
            .gaming-glow {
                display: none !important;
            }
            .gaming-banner-image {
                transition: none !important;
            }
        `;
        document.head.appendChild(style);
    }
};

// Apply optimizations when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', optimizeForLowEndDevices);
} else {
    optimizeForLowEndDevices();
}
