{# Enhanced Special Vertical Gallery Component with Comprehensive Configuration #}
{# Configuration Variables #}
{% set main_title_font_size = component.main_title_font_size|default('large') %}
{% set main_title_font_weight = component.main_title_font_weight|default('bold') %}
{% set item_title_font_size = component.item_title_font_size|default('medium') %}
{% set item_title_font_weight = component.item_title_font_weight|default('medium') %}
{% set description_font_size = component.description_font_size|default('small') %}
{% set text_alignment = component.text_alignment|default('center') %}

{# Color Configuration #}
{% set main_title_color = component.main_title_color|default('#ffffff') %}
{% set item_title_color = component.item_title_color|default('#ffffff') %}
{% set item_description_color = component.item_description_color|default('#e0e0e0') %}
{% set button_bg_color = component.button_bg_color|default('#00ff88') %}
{% set button_text_color = component.button_text_color|default('#000000') %}
{% set enable_background = component.enable_background|default(false) %}
{% set background_color = component.background_color|default('#0a0a0a') %}
{% set hover_overlay_color = component.hover_overlay_color|default('#00ff88') %}
{% set glow_border_color = component.glow_border_color|default('#00d4ff') %}
{% set item_background_color = component.item_background_color|default('#1a1a1a') %}

{# Animation Configuration #}
{% set gallery_animation_type = component.gallery_animation_type|default('fadeIn') %}
{% set item_animation_type = component.item_animation_type|default('slideUp') %}
{% set text_animation_type = component.text_animation_type|default('trackingExpand') %}
{% set animation_duration = component.animation_duration|default('medium') %}
{% set animation_delay = component.animation_delay|default('short') %}

{# Layout Configuration #}
{% set items_per_row = component.items_per_row|default('3') %}
{% set item_spacing = component.item_spacing|default(20) %}
{% set top_margin = component.top_margin|default(0) %}
{% set bottom_margin = component.bottom_margin|default(40) %}
{% set full_width = component.full_width|default(false) %}

{# Responsive Configuration #}
{% set mobile_items_per_row = component.mobile_items_per_row|default('1') %}
{% set tablet_items_per_row = component.tablet_items_per_row|default('2') %}
{% set mobile_spacing = component.mobile_spacing|default(15) %}
{% set enable_mobile_full_width = component.enable_mobile_full_width|default(true) %}

{# Visual Effects Configuration #}
{% set enable_hover_effects = component.enable_hover_effects|default(true) %}
{% set hover_animation_type = component.hover_animation_type|default('scale') %}
{% set image_border_radius = component.image_border_radius|default(10) %}
{% set enable_image_shadows = component.enable_image_shadows|default(true) %}
{% set enable_glow_effects = component.enable_glow_effects|default(true) %}
{% set enable_particles = component.enable_particles|default(false) %}
{% set enable_image_effects = component.enable_image_effects|default(true) %}

{# Build CSS Classes #}
{% set gallery_classes = [
    'special-vertical-gallery-section',
    'gallery-animation-' ~ gallery_animation_type,
    'animation-duration-' ~ animation_duration,
    'animation-delay-' ~ animation_delay,
    'text-align-' ~ text_alignment,
    'items-per-row-' ~ items_per_row,
    'mobile-items-' ~ mobile_items_per_row,
    'tablet-items-' ~ tablet_items_per_row,
    enable_background ? 'has-background' : '',
    enable_hover_effects ? 'hover-effects-enabled' : '',
    enable_glow_effects ? 'glow-effects-enabled' : '',
    enable_particles ? 'particles-enabled' : '',
    full_width ? 'full-width-gallery' : '',
    enable_mobile_full_width ? 'mobile-full-width' : ''
]|join(' ')|trim %}

{% if component.gallery_items and component.gallery_items|length > 0 %}
<section class="{{ gallery_classes }}"
         id="special-vertical-gallery-{{ component.id }}"
         data-gallery-animation="{{ gallery_animation_type }}"
         data-item-animation="{{ item_animation_type }}"
         data-text-animation="{{ text_animation_type }}"
         data-animation-duration="{{ animation_duration }}"
         data-animation-delay="{{ animation_delay }}"
         data-hover-animation="{{ hover_animation_type }}"
         data-enable-particles="{{ enable_particles ? 'true' : 'false' }}"
         style="--main-title-color: {{ main_title_color }};
                --item-title-color: {{ item_title_color }};
                --item-description-color: {{ item_description_color }};
                --button-bg-color: {{ button_bg_color }};
                --button-text-color: {{ button_text_color }};
                --hover-overlay-color: {{ hover_overlay_color }};
                --glow-border-color: {{ glow_border_color }};
                --item-background-color: {{ item_background_color }};
                --item-spacing: {{ item_spacing }}px;
                --image-border-radius: {{ image_border_radius }}px;
                --top-margin: {{ top_margin }}px;
                --bottom-margin: {{ bottom_margin }}px;
                {% if enable_background %}--background-color: {{ background_color }};{% endif %}">

    {# Particles Background (if enabled) #}
    {% if enable_particles %}
    <div class="particles-container">
        {% for i in 1..20 %}
        <div class="particle particle-{{ i }}"></div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- العنوان الرئيسي والوصف -->
    {% if component.main_title or component.main_description %}
    <div class="gallery-header text-{{ text_alignment }}">
        {% if component.main_title %}
        <h2 class="gallery-main-title
                   font-size-{{ main_title_font_size }}
                   font-weight-{{ main_title_font_weight }}
                   text-animation-{{ text_animation_type }}
                   animate-element">
            {{ component.main_title }}
        </h2>
        {% endif %}

        {% if component.main_description %}
        <p class="gallery-main-description
                 font-size-{{ description_font_size }}
                 text-animation-{{ text_animation_type }}
                 animate-element">
            {{ component.main_description }}
        </p>
        {% endif %}
    </div>
    {% endif %}

    <!-- عناصر المعرض -->
    <div class="gallery-container {{ full_width ? 'container-fluid' : 'container' }}">
        <div class="gallery-grid items-per-row-{{ items_per_row }}">
            {% for item in component.gallery_items %}
            {% set item_classes = [
                'gallery-item',
                'item-animation-' ~ item_animation_type,
                'hover-animation-' ~ hover_animation_type,
                enable_hover_effects ? 'hover-enabled' : '',
                enable_image_shadows ? 'has-shadows' : '',
                enable_glow_effects ? 'has-glow' : '',
                enable_image_effects ? 'image-effects-enabled' : '',
                'animate-element'
            ]|join(' ')|trim %}

            <div class="{{ item_classes }}"
                 data-item-index="{{ loop.index0 }}"
                 style="--animation-delay: {{ (loop.index0 * 0.15) }}s;">

                <!-- صورة العنصر -->
                {% if item.image %}
                <div class="gallery-item-image-wrapper">
                    <div class="image-container">
                        <img data-src="{{ item.image }}"
                             alt="{{ item.title|default('صورة المعرض') }}"
                             class="gallery-item-image"
                             loading="lazy"
                             decoding="async">

                        <!-- تأثيرات الصورة -->
                        {% if enable_hover_effects %}
                        <div class="hover-overlay"></div>
                        {% endif %}

                        {% if enable_glow_effects %}
                        <div class="glow-border"></div>
                        {% endif %}

                        {% if enable_image_effects %}
                        <div class="image-effects">
                            <div class="shine-effect"></div>
                            <div class="pulse-effect"></div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- محتوى العنصر -->
                <div class="gallery-item-content text-{{ text_alignment }}">
                    {% if item.title %}
                    <h3 class="gallery-item-title
                              font-size-{{ item_title_font_size }}
                              font-weight-{{ item_title_font_weight }}
                              text-animation-{{ text_animation_type }}
                              animate-element">
                        {{ item.title }}
                    </h3>
                    {% endif %}

                    {% if item.description %}
                    <p class="gallery-item-description
                             font-size-{{ description_font_size }}
                             text-animation-{{ text_animation_type }}
                             animate-element">
                        {{ item.description }}
                    </p>
                    {% endif %}

                    <!-- زر الرابط -->
                    {% if item.url and item.button_text %}
                    <div class="gallery-item-button-wrapper">
                        <a href="{{ item.url }}"
                           class="gallery-item-button
                                  hover-animation-{{ hover_animation_type }}
                                  animate-element">
                            <span class="button-text">{{ item.button_text|default('اطلب الآن') }}</span>
                            <span class="button-glow"></span>
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

{% else %}
<div style="text-align: center; padding: 40px; color: #ffffff;">
    <p>لا توجد عناصر في المعرض</p>
</div>
{% endif %}