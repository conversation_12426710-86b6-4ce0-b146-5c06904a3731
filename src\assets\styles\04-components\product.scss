  
// Wishlist Product entry
.product-entry {
  @apply h-full transition-shadow duration-300 bg-white hover:shadow-default rounded-lg justify-between flex relative;

  &--wishlist {
    @apply justify-between p-4 flex-col sm:flex-row;

    .product-entry__image {
      @apply overflow-hidden w-16 h-12 md:w-20 md:h-16 rounded-md;
    }

    salla-button{
      @apply flex;
    }
  }
}

.s-comments {
  &-page {
    @apply pt-16;
  }

  &-product {
    @apply bg-gray-100 mb-4 md:mb-14 py-4 md:py-14;

    .s-comments-container {
      @apply container;
    }
  }
}

// share list
.share-btns-list{
  @apply absolute z-10 overflow-hidden opacity-0 top-12 bg-white flex items-center flex-col shadow-huge rounded-3xl;

  a{
    @apply block p-3 hover:text-primary;
  }
}


// cart
.cart-item{
  salla-conditional-fields > section{
    @apply px-0 pt-0 last:mb-0 last:pb-0;
  }
}

// Digital Rating(SVG)---------------------------
.s-product-card-content-pie-svg-base{ 
  transition: stroke-dashoffset 1s linear;
  stroke: #E8EDF2;
  stroke-width: 2px;
  stroke-linecap: round;
  fill: none;
}

.s-product-card-content-pie-svg-bar{
  fill: none;
  stroke: var(--color-primary);
  stroke-dasharray: 100 100;
  stroke-dashoffset: 100;
}

.s-product-card{
    &-content-sub{
      @apply justify-center gap-3;
      text-align: center;
    }

    &-starting-price{
      @apply w-auto;
      text-align: center;
  }
  }

.pie-wrapper {
  @apply w-[72px] h-[72px] absolute top-2.5 rtl:left-5 ltr:right-5 text-primary flex items-center justify-center;

  .pie-svg {
    circle {
      transition: stroke-dashoffset 1s linear;
      stroke: #E8EDF2;
      stroke-width: 2px;
      stroke-linecap: round;
      fill: none;

      &.circle_bar {
        stroke: var(--color-primary);
        stroke-dasharray: 100 100;
        stroke-dashoffset: 100;
      }
    }
  }

  span {
      display: block;
      font-size: var(--font-xsm);
      color: var(--color-text);
      text-align: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      line-height: 1;
      font-size: var(--font-xsm);

      b {
          font-size: var(--font-md) !important;
          margin-bottom: 3px;
          color: var(--color-primary);
      }
  }

}

/* Add to cart sticky bar */
@media (max-width: 640px) {
  .is-sticky-product-bar{
    &.product-single{
      @apply pb-28;
    }

    .sticky-product-bar{
      @apply flex flex-wrap flex-col gap-2.5 sm:gap-4 fixed z-[2] bottom-0 left-0 p-3 w-full justify-between items-center shadow-[-1px_-2px_9px_0_rgba(0,0,0,0.05)] transition duration-700 delay-500 translate-y-100 opacity-0 ease-elastic;

      &__quantity,
      salla-add-product-button{
        @apply translate-y-5 opacity-0 transition duration-700 ease-elastic w-full;
      }

      &__price,
      .form-label{
        @apply hidden;
      }

      &__quantity{
        salla-quantity-input{
          @apply w-full;
          .s-quantity-input-container{
            @apply w-full;
          }
          .s-quantity-input-input{
            @apply w-[80%]
          }
        }
        @apply m-0 delay-[900ms];
      }

      salla-add-product-button{
        @apply w-auto m-0 flex-1 delay-1000;
      }

      .hydrated &{
        &,
        &__quantity,
        salla-add-product-button{
          @apply translate-y-0 opacity-100;
        }
      }
    }
  }
}

// Tabs fix
.s-tabs-header * {
  pointer-events: auto !important;
}
.product__description {
  ul,
  li{
    list-style: inherit !important;
  }

  ul,
  ol {
    @apply px-6;
  }

  ol{
    list-style: auto !important;
  }

  a{
    @apply text-blue-700;
  }
}



// 3d Image Viewer ------------------------------
.model-viewer-wrapper {
  @apply relative mb-4 md:mb-0 w-full;
}

.switcher-3d-view {
  @apply absolute top-5 rtl:left-4 ltr:right-4;

  // .has-calories-badge &{
  //   @apply top-auto bottom-5;
  // }
}

.model-viewer {
  @apply w-full h-full;

  &__poster {
    @apply absolute inset-0 bg-contain bg-no-repeat bg-center;
  }
}

.s-toggle .s-toggle-switcher-has-text:before {
  @apply content-[attr(data-switcher-text)] font-bold text-center text-gray-400 text-xs leading-[22px];
  font-family: arial, serif;
}

.s-toggle .s-toggle-input:checked+div.s-toggle-switcher-bg-white:before {
  @apply text-gray-600 border-white bg-white;
}

// product options file upload component
.product-option-uploader{
  .s-file-upload-wrapper{
    min-height: 120px;
    .filepond--list-scroller{
      overflow-y: inherit !important;
    }
    .filepond--drop-label {
      min-height: 120px !important;
      @apply bg-white cursor-pointer border-dashed rounded-md border border-gray-200;
    }
    .product-option-uploader-placholder{
      @apply flex flex-col justify-center items-center;
      &-icon {
        @apply mb-1;
        i {
          color: #7c8082;
          @apply text-xl;
        }
      }
    }

  }
}

// centered offer modal width fix
@media (max-width: 640px) {
  .s-offer-modal-slider-centered .s-offer-modal-slider-item {
    max-width: 46% !important;
  }
}

// Gaming Theme Product Cards
.s-product-card-entry {
  background: linear-gradient(145deg, var(--gaming-bg-card), var(--gaming-bg-secondary));
  border: 1px solid var(--gaming-border);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, var(--gaming-glow-blue), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 20px var(--gaming-glow-blue);
    border-color: var(--gaming-accent-blue);

    &::before {
      opacity: 0.1;
    }

    .s-product-card-image img {
      transform: scale(1.05);
    }

    .s-product-card-content-title a {
      color: var(--gaming-accent-blue);
    }
  }

  // Gaming glow effect variants
  &:nth-child(3n+1):hover {
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 20px var(--gaming-glow-purple);
    border-color: var(--gaming-accent-purple);

    &::before {
      background: linear-gradient(45deg, transparent, var(--gaming-glow-purple), transparent);
    }

    .s-product-card-content-title a {
      color: var(--gaming-accent-purple);
    }
  }

  &:nth-child(3n+2):hover {
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 20px var(--gaming-glow-green);
    border-color: var(--gaming-accent-green);

    &::before {
      background: linear-gradient(45deg, transparent, var(--gaming-glow-green), transparent);
    }

    .s-product-card-content-title a {
      color: var(--gaming-accent-green);
    }
  }
}

.s-product{
    &-card{
      &-full-image salla-add-product-button {
        @apply bg-white rounded;
      }

      &-wishlist-added i {
        @apply text-red-500
      }
      &-fit-height.s-product-card-vertical .s-product-card-image {
        @apply flex-none sm:flex-1;
      }

      // Center align product price
      &-price {
        text-align: center;
        display: block;
        width: 100%;
        margin: 0 auto;
      }

      // Gaming theme image styling
      &-image {
        position: relative;
        overflow: hidden;
        border-radius: 8px 8px 0 0;

        img {
          transition: transform 0.3s ease;
          filter: brightness(0.9) contrast(1.1);
        }

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
          pointer-events: none;
        }
      }

      // Gaming theme content styling
      &-content {
        position: relative;
        z-index: 2;
        padding: 1rem;

        &-main {
          text-align: center;
        }

        &-title {
          text-align: center;

          a {
            color: var(--gaming-text-primary);
            font-weight: 600;
            transition: color 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          }
        }

        &-sub {
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .s-product-card-price,
          .s-product-card-sale-price h4 {
            color: var(--gaming-accent-blue);
            font-weight: 700;
            text-shadow: 0 0 10px var(--gaming-glow-blue);
          }

          .s-product-card-rating {
            justify-content: center;
          }
        }
      }

      // Gaming wishlist button
      &-wishlist-btn {
        background: rgba(0, 0, 0, 0.7) !important;
        border: 1px solid var(--gaming-border) !important;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease !important;

        &:hover {
          background: var(--gaming-accent-pink) !important;
          border-color: var(--gaming-accent-pink) !important;
          box-shadow: 0 0 15px var(--gaming-glow-blue);
          transform: scale(1.1);
        }

        &.s-product-card-wishlist-added {
          background: var(--gaming-accent-pink) !important;
          border-color: var(--gaming-accent-pink) !important;
          box-shadow: 0 0 15px rgba(255, 0, 128, 0.5);

          i {
            color: white !important;
          }
        }

        i {
          color: var(--gaming-text-secondary);
          transition: color 0.3s ease;
        }
      }
    }

    &-options-colors-wrapper{
      @apply flex flex-wrap gap-4;

      .s-product-options-colors-item{
        @apply m-0 rtl:m-0 ltr:m-0 w-auto;
    }
  }
}

// Gaming Minimal card
.s-product-card-minimal {
  .s-product-card-image {
    @apply rtl:rounded-r ltr:rounded-l;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, var(--gaming-glow-blue), transparent);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  &:hover .s-product-card-image::before {
    opacity: 0.1;
  }

  salla-button.s-product-card-wishlist-btn {
    @apply rtl:left-2.5 ltr:right-2.5 top-2.5;
  }
}

// Gaming Special Cards (Featured/Sale)
.s-product-card-special {
  background: linear-gradient(145deg, var(--gaming-bg-secondary), var(--gaming-bg-primary));
  border: 2px solid var(--gaming-accent-purple);
  box-shadow: 0 0 30px var(--gaming-glow-purple);

  &::before {
    background: linear-gradient(45deg, var(--gaming-glow-purple), transparent, var(--gaming-glow-blue));
    opacity: 0.15;
  }

  .s-product-card-content-pie {
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid var(--gaming-accent-purple);
    border-radius: 50%;
    backdrop-filter: blur(10px);
  }
}

.s-product-card-starting-price{
  @apply justify-center gap-2.5;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;

  h4{
    color: var(--gaming-accent-green);
    text-shadow: 0 0 10px var(--gaming-glow-green);
    margin: 0;
  }

  p {
    color: var(--gaming-text-secondary);
    margin: 0;
  }
}

// Gaming Promotion Badges
.s-product-card-promotion-title {
  background: linear-gradient(45deg, var(--gaming-accent-pink), var(--gaming-accent-purple));
  color: white;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 0 15px rgba(255, 0, 128, 0.5);
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 15px rgba(255, 0, 128, 0.5);
  }
  50% {
    box-shadow: 0 0 25px rgba(255, 0, 128, 0.8);
  }
}

// Gaming Out of Stock Badge
.s-product-card-out-badge {
  background: linear-gradient(45deg, #ff4444, #cc0000);
  color: white;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  opacity: 0.9;
}

// Enhanced image zooming
.magnify-wrapper {
	@apply relative overflow-hidden;

	.img-magnifier-glass {
	  @apply hidden lg:block;
	  position: absolute;
	  z-index: 10;
	  border-radius: 50%;
	  cursor: none;
	  pointer-events: none;
	  /*Set the size of the magnifier glass:*/
	  width: 250px;
	  height: 250px;
	  opacity: 0;
	  transform: scale(0);
	  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	  box-shadow:
		0 0 0 3px rgba(255, 255, 255, 0.8),
		0 0 0 4px rgba(0, 0, 0, 0.1),
		0 10px 30px rgba(0, 0, 0, 0.3);
	  border: 2px solid rgba(255, 255, 255, 0.9);
	  backdrop-filter: blur(1px);
	}

	&:hover {
	  .img-magnifier-glass {
		opacity: 1;
		pointer-events: initial;
		transform: scale(1);
		transition-delay: 0.2s;
	  }
	}

	// Zoom indicator
	&::after {
	  content: '';
	  position: absolute;
	  top: 12px;
	  right: 12px;
	  width: 32px;
	  height: 32px;
	  background: rgba(0, 0, 0, 0.7);
	  border-radius: 50%;
	  display: none;
	  align-items: center;
	  justify-content: center;
	  color: white;
	  font-size: 14px;
	  z-index: 5;
	  transition: all 0.3s ease;

	  @media (min-width: 1024px) {
		display: flex;
	  }
	}

	&:hover::after {
	  background: rgba(0, 0, 0, 0.8);
	  transform: scale(1.1);
	}
}

// Product gallery enhancements
.product-gallery-container {
  @apply relative;

  .image-counter {
	@apply transition-all duration-300 select-none;

	&:hover {
	  @apply bg-black/80 scale-105;
	}
  }

  .model-indicator,
  .video-indicator {
	@apply transition-all duration-300 backdrop-blur-sm select-none;

	&:hover {
	  @apply scale-105;
	}
  }

  // Enhanced lightbox trigger
  .gallery-image-wrapper {
	@apply relative;

	.image-overlay {
	  @apply pointer-events-none;
	}

	&:hover .image-overlay {
	  @apply bg-black/5;
	}

	// Fullscreen icon
	&::before {
	  content: '\ee8d'; // sicon-fullscreen
	  font-family: 'sallaicons';
	  position: absolute;
	  top: 12px;
	  left: 12px;
	  width: 32px;
	  height: 32px;
	  background: rgba(0, 0, 0, 0.7);
	  color: white;
	  border-radius: 50%;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  font-size: 14px;
	  z-index: 5;
	  opacity: 0;
	  transform: scale(0.8);
	  transition: all 0.3s ease;
	}

	&:hover::before {
	  opacity: 1;
	  transform: scale(1);
	}

	// Loading state
	&.loading {
	  .gallery-main-image {
		@apply opacity-50;
	  }

	  &::after {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: 32px;
		height: 32px;
		margin: -16px 0 0 -16px;
		border: 3px solid rgba(255, 255, 255, 0.3);
		border-top-color: white;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	  }
	}
  }

  // Keyboard navigation support
  &:focus-within {
	.gallery-image-wrapper::before {
	  opacity: 1;
	  transform: scale(1);
	}
  }

  // Mobile optimizations
  @media (max-width: 768px) {
	.image-counter {
	  @apply text-xs px-2 py-1;
	}

	.model-indicator,
	.video-indicator {
	  @apply text-xs px-2 py-1;
	}

	.gallery-image-wrapper::before {
	  @apply w-8 h-8 text-xs;
	  margin: -16px 0 0 -16px;
	}
  }

  // High contrast mode support
  @media (prefers-contrast: high) {
	.image-counter,
	.model-indicator,
	.video-indicator {
	  @apply bg-black text-white border border-white;
	}
  }

  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
	.gallery-main-image,
	.image-overlay,
	.gallery-image-wrapper::before {
	  @apply transition-none;
	}

	.magnify-wrapper .img-magnifier-glass {
	  @apply transition-none;
	}
  }
}

// Spin animation for loading state
@keyframes spin {
  to {
	transform: rotate(360deg);
  }
}


// Gaming Horizontal card
.s-product-card-horizontal{
  background: linear-gradient(90deg, var(--gaming-bg-card), var(--gaming-bg-secondary));
  border-left: 3px solid transparent;
  transition: all 0.3s ease;

  &:hover {
    border-left-color: var(--gaming-accent-blue);
    background: linear-gradient(90deg, var(--gaming-bg-secondary), var(--gaming-bg-card));
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  }

  .s-product-card-content{
    @apply mb-0;

    .s-product-card-content-title a {
      color: var(--gaming-text-primary);
    }
  }

  @media(max-width: 480px){
    salla-button.s-product-card-wishlist-btn{
      @apply absolute bottom-2.5 rtl:-right-12 ltr:-left-12
    }
  }

  salla-progress-bar{
    @apply flex flex-col grow;
  }
}

// Gaming Rating Stars
.s-rating-stars-reviews {
  color: var(--gaming-text-secondary);
}

.s-product-card-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  i {
    color: var(--gaming-accent-green);
    filter: drop-shadow(0 0 5px var(--gaming-glow-green));
  }

  span {
    color: var(--gaming-text-primary);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

// Gaming Sale Price
.s-product-card-sale-price {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  h4{
    color: var(--gaming-accent-green) !important;
    text-shadow: 0 0 10px var(--gaming-glow-green);
    font-weight: 700;
    margin: 0;
  }

  span{
    color: var(--gaming-text-secondary);
    text-decoration: line-through;
    opacity: 0.7;
    margin: 0;
  }
}

// Enhanced Product Title and Price Styling
.main-content {
  h1 {
    // Gaming style title with glow effect
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;

    &:hover {
      text-shadow: 0 0 15px rgba(255, 255, 255, 0.5), 0 0 30px rgba(255, 255, 255, 0.2);
    }
  }

  // Enhanced brand styling
  .product-brand {
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
    }
  }

  // Enhanced rating container
  .rating-container {
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.2);
    }

    salla-rating-stars {
      --rating-color: var(--gaming-accent-blue);
      --rating-empty-color: rgba(255, 255, 255, 0.2);
    }
  }

  // Enhanced description styling
  .product__description {
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.2);
    }

    .article--main {
      color: var(--gaming-text-secondary);
      line-height: 1.7;
    }
  }

  // Gaming read more button
  .gaming-read-more-btn {
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);

      i {
        transform: translateY(2px);
      }
    }

    &.is-expanded {
      i {
        transform: rotate(180deg);
      }
    }
  }

  // Gaming tags styling
  .gaming-tag {
    &:hover {
      box-shadow: 0 0 15px rgba(139, 92, 246, 0.4);
      text-shadow: 0 0 8px rgba(139, 92, 246, 0.6);
    }
  }
}

// Gaming Price Styling
.gaming-price-glow {
  text-shadow:
    0 0 10px var(--gaming-glow-blue),
    0 0 20px var(--gaming-glow-blue),
    0 0 30px var(--gaming-glow-blue);
  transition: all 0.3s ease;

  &:hover {
    text-shadow:
      0 0 15px var(--gaming-glow-blue),
      0 0 30px var(--gaming-glow-blue),
      0 0 45px var(--gaming-glow-blue);
    transform: scale(1.05);
  }
}

.shadow-gaming-glow {
  text-shadow:
    0 0 8px var(--gaming-glow-green),
    0 0 16px var(--gaming-glow-green);
}

// Enhanced price container
.main-content .flex.whitespace-nowrap {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }
}

// Gaming Add to Cart Button
salla-add-product-button {
  button {
    background: linear-gradient(45deg, var(--gaming-accent-blue), var(--gaming-accent-purple)) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3) !important;

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 8px 25px rgba(0, 212, 255, 0.5) !important;
      background: linear-gradient(45deg, var(--gaming-accent-purple), var(--gaming-accent-blue)) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }
}

// filters
@media (max-width: 1024px){
  salla-filters{
    &,
    > *{
      @apply px-5 #{!important};
    }
  }
}

// bank offer corner badge
#offer-corner-badge{
  @apply float-left absolute bg-red-600 text-white transform rtl:-rotate-45 ltr:rotate-45 px-12 py-1 h-auto w-auto border-0 m-0 z-1 rtl:top-3 ltr:top-5 rtl:-left-12 ltr:-right-14;
}

// cart options
.cart-options {
  @apply bg-white border-dashed border border-gray-400 rounded-md;
  salla-product-options {
    @apply mb-0 pt-0;
  
    .s-product-options-option:not(div.s-product-options-option-booking) {
      @apply block #{!important};
    }
      &-label {
        @apply mb-3;
      }

      .s-form-control {
        @apply rounded-md border-gray-200
      }

      .s-datetime-picker-input {
        @apply rounded-md border-gray-200
      }

      .s-product-options-multiple-options-wrapper {
        @apply block #{!important};
      
    }
  }
}

// Gaming Full Image Cards
.s-product-card-full-image {
  position: relative;
  overflow: hidden;

  .s-product-card-overlay {
    background: linear-gradient(
      180deg,
      transparent 0%,
      rgba(0, 0, 0, 0.3) 50%,
      rgba(15, 15, 35, 0.9) 100%
    );
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(
        180deg,
        rgba(0, 212, 255, 0.1) 0%,
        rgba(0, 0, 0, 0.4) 50%,
        rgba(15, 15, 35, 0.95) 100%
      );
    }
  }

  .s-product-card-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(180deg, transparent, rgba(15, 15, 35, 0.95));
    backdrop-filter: blur(10px);

    .s-product-card-content-title a {
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    }
  }

  salla-add-product-button {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    z-index: 10;
  }
}

// Gaming Quantity Input
salla-quantity-input {
  .s-quantity-input-container {
    background: var(--gaming-bg-secondary);
    border: 1px solid var(--gaming-border);
    border-radius: 8px;
    overflow: hidden;
  }

  input {
    background: transparent;
    color: var(--gaming-text-primary);
    border: none;
    text-align: center;
    font-weight: 600;
  }

  .s-quantity-input-button {
    background: var(--gaming-bg-card);
    color: var(--gaming-accent-blue);
    border: none;
    transition: all 0.3s ease;

    &:hover {
      background: var(--gaming-accent-blue);
      color: white;
      box-shadow: 0 0 10px var(--gaming-glow-blue);
    }
  }
}

salla-quantity-input[max='1']{
  input {
    @apply pointer-events-none opacity-50;
  }

  .s-quantity-input-button {
    @apply cursor-not-allowed
  }
}

// Gaming Animation Classes
@keyframes gaming-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes gaming-glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--gaming-glow-blue);
  }
  50% {
    box-shadow: 0 0 20px var(--gaming-glow-blue), 0 0 30px var(--gaming-glow-blue);
  }
}

.gaming-pulse {
  animation: gaming-pulse 2s infinite;
}

.gaming-glow {
  animation: gaming-glow 3s infinite;
}