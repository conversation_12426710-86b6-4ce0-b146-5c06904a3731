/**
 * Enhanced Second Gallery Component - Configuration-Based Animation System
 * Handles comprehensive customization settings and dynamic styling
 */

(function() {
    'use strict';
    
    // Enhanced Gallery Manager with Configuration Support
    const EnhancedSecondGallery = {
        
        // Cache for active galleries
        galleries: new Map(),
        
        // Intersection observer for lazy initialization
        observer: null,
        
        init() {
            // Setup intersection observer for performance
            this.setupIntersectionObserver();
            
            // Initialize all enhanced galleries
            this.initializeGalleries();
            
            // Setup reduced motion detection
            this.setupReducedMotionDetection();
            
            // Setup resize handler with debouncing
            this.setupResizeHandler();
        },
        
        setupIntersectionObserver() {
            if (!window.IntersectionObserver) {
                // Fallback for older browsers
                this.initializeGalleries();
                return;
            }
            
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const galleryId = entry.target.id;
                        this.initializeGallery(galleryId);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
        },
        
        initializeGalleries() {
            const galleries = document.querySelectorAll('.enhanced-second-gallery[data-component="second-gallery"]');
            
            galleries.forEach(gallery => {
                if (this.observer) {
                    this.observer.observe(gallery);
                } else {
                    this.initializeGallery(gallery.id);
                }
            });
        },
        
        initializeGallery(galleryId) {
            const gallery = document.getElementById(galleryId);
            if (!gallery || this.galleries.has(galleryId)) return;
            
            // Read configuration from data attributes
            const config = this.readConfiguration(gallery);
            
            // Apply dynamic styling
            this.applyDynamicStyling(gallery, config);
            
            // Setup animations
            this.setupAnimations(gallery, config);
            
            // Setup hover effects
            this.setupHoverEffects(gallery, config);
            
            // Store gallery instance
            this.galleries.set(galleryId, {
                gallery,
                config,
                isAnimated: false
            });
        },
        
        readConfiguration(gallery) {
            return {
                titleAnimation: gallery.dataset.titleAnimation || 'fadeIn',
                galleryAnimation: gallery.dataset.galleryAnimation || 'slideUp',
                animationDuration: gallery.dataset.animationDuration || 'medium',
                animationDelay: gallery.dataset.animationDelay || 'short',
                itemsPerRow: parseInt(gallery.dataset.itemsPerRow) || 3,
                enableHoverEffects: gallery.dataset.enableHoverEffects === 'true',
                hoverAnimationType: gallery.dataset.hoverAnimationType || 'scale',
                imageBorderRadius: parseInt(gallery.dataset.imageBorderRadius) || 15,
                enableImageShadows: gallery.dataset.enableImageShadows === 'true',
                enableGlowEffects: gallery.dataset.enableGlowEffects === 'true'
            };
        },
        
        applyDynamicStyling(gallery, config) {
            // Apply border radius to images
            const images = gallery.querySelectorAll('.gaming-gallery-image, .gaming-gallery-image-container');
            images.forEach(img => {
                img.style.borderRadius = `${config.imageBorderRadius}px`;
            });
            
            // Apply custom CSS properties from inline styles
            const customProps = gallery.style.cssText.match(/--[\w-]+:\s*[^;]+/g);
            if (customProps) {
                customProps.forEach(prop => {
                    const [property, value] = prop.split(':').map(s => s.trim());
                    gallery.style.setProperty(property, value);
                });
            }
        },
        
        setupAnimations(gallery, config) {
            const title = gallery.querySelector('.gaming-gallery-title');
            const items = gallery.querySelectorAll('.gaming-gallery-item');
            
            // Setup title animation
            if (title) {
                this.setupTitleAnimation(title, config);
            }
            
            // Setup gallery items animation with stagger
            items.forEach((item, index) => {
                this.setupItemAnimation(item, config, index);
            });
            
            // Trigger animations when in view
            this.triggerAnimations(gallery, config);
        },
        
        setupTitleAnimation(title, config) {
            // Reset animation state
            title.classList.remove('animate-in');
            
            // Apply animation delay
            const delays = {
                'none': 0,
                'short': 200,
                'medium': 500,
                'long': 1000
            };
            
            setTimeout(() => {
                title.classList.add('animate-in');
            }, delays[config.animationDelay] || 200);
        },
        
        setupItemAnimation(item, config, index) {
            // Reset animation state
            item.classList.remove('animate-in');
            
            // Calculate stagger delay
            const baseDelay = {
                'none': 0,
                'short': 200,
                'medium': 500,
                'long': 1000
            }[config.animationDelay] || 200;
            
            const staggerDelay = baseDelay + (index * 100);
            
            setTimeout(() => {
                item.classList.add('animate-in');
            }, staggerDelay);
        },
        
        setupHoverEffects(gallery, config) {
            if (!config.enableHoverEffects) return;
            
            const items = gallery.querySelectorAll('.gaming-gallery-item');
            
            items.forEach(item => {
                const imageWrapper = item.querySelector('.gaming-gallery-image-wrapper');
                const overlay = item.querySelector('.gaming-gallery-overlay');
                
                if (imageWrapper) {
                    // Apply hover animation type
                    item.addEventListener('mouseenter', () => {
                        this.applyHoverEffect(imageWrapper, config.hoverAnimationType, true);
                    });
                    
                    item.addEventListener('mouseleave', () => {
                        this.applyHoverEffect(imageWrapper, config.hoverAnimationType, false);
                    });
                }
            });
        },
        
        applyHoverEffect(element, type, isHover) {
            const transforms = {
                'scale': isHover ? 'scale(1.05)' : 'scale(1)',
                'rotate': isHover ? 'rotate(2deg) scale(1.02)' : 'rotate(0) scale(1)',
                'slide': isHover ? 'translateY(-5px)' : 'translateY(0)',
                'pulse': isHover ? 'scale(1.02)' : 'scale(1)'
            };
            
            element.style.transform = transforms[type] || transforms['scale'];
        },
        
        triggerAnimations(gallery, config) {
            // Use intersection observer for better performance
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const galleryInstance = this.galleries.get(gallery.id);
                        if (galleryInstance && !galleryInstance.isAnimated) {
                            this.animateGallery(gallery, config);
                            galleryInstance.isAnimated = true;
                            animationObserver.unobserve(entry.target);
                        }
                    }
                });
            }, {
                threshold: 0.2
            });
            
            animationObserver.observe(gallery);
        },
        
        animateGallery(gallery, config) {
            // Add main animation class to gallery
            gallery.classList.add('animate-in');
            
            // Animate title
            const title = gallery.querySelector('.gaming-gallery-title');
            if (title) {
                setTimeout(() => {
                    title.classList.add('animate-in');
                }, 100);
            }
            
            // Animate items with stagger
            const items = gallery.querySelectorAll('.gaming-gallery-item');
            items.forEach((item, index) => {
                setTimeout(() => {
                    item.classList.add('animate-in');
                }, 300 + (index * 100));
            });
            
            // Animate navigation if present
            const nav = gallery.querySelector('.gaming-gallery-nav');
            if (nav) {
                setTimeout(() => {
                    nav.classList.add('animate-in');
                }, 800 + (items.length * 100));
            }
        },
        
        setupReducedMotionDetection() {
            if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                // Disable complex animations for users who prefer reduced motion
                document.body.classList.add('reduced-motion');
            }
        },
        
        setupResizeHandler() {
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    this.handleResize();
                }, 250);
            });
        },
        
        handleResize() {
            // Recalculate layouts for responsive design
            this.galleries.forEach((galleryInstance, galleryId) => {
                const gallery = document.getElementById(galleryId);
                if (gallery) {
                    this.applyDynamicStyling(gallery, galleryInstance.config);
                }
            });
        },
        
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            this.galleries.clear();
        }
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            EnhancedSecondGallery.init();
        });
    } else {
        EnhancedSecondGallery.init();
    }
    
    // Expose for external access if needed
    window.EnhancedSecondGallery = EnhancedSecondGallery;
    
})();
