{#
| Variable                         | Type      | Description                                                         |
|----------------------------------|-----------|---------------------------------------------------------------------|
| component.title                  | string    | Section title                                                       |
| component.description            | string    | Section description                                                 |
| component.main_image             | string    | Main image URL                                                      |
| component.main_image_link_type   | string    | Link type: products, categories, brands, pages, blog_articles,     |
|                                  |           | blog_categories, offers_link, brands_link, blog_link               |
| component.main_image_link_value  | array     | Selected item from dropdown list based on link type                |
| component.right_icons            | array     | Array of icons for right side                                       |
| component.right_icons[].icon     | string    | Icon class name                                                     |
| component.right_icons[].title    | string    | Icon title                                                          |
| component.left_icons             | array     | Array of icons for left side                                        |
| component.left_icons[].icon      | string    | Icon class name                                                     |
| component.left_icons[].title     | string    | Icon title                                                          |
#}

{# Generate link based on type and value #}
{% set main_image_link = null %}
{% if component.main_image_link_type %}
    {% if component.main_image_link_type == 'products' and component.main_image_link_value and component.main_image_link_value|length > 0 %}
        {% set selected_item = component.main_image_link_value[0] %}
        {% set main_image_link = url('product.show', {'id': selected_item.id}) %}
    {% elseif component.main_image_link_type == 'categories' and component.main_image_link_value and component.main_image_link_value|length > 0 %}
        {% set selected_item = component.main_image_link_value[0] %}
        {% set main_image_link = url('category.show', {'id': selected_item.id}) %}
    {% elseif component.main_image_link_type == 'brands' and component.main_image_link_value and component.main_image_link_value|length > 0 %}
        {% set selected_item = component.main_image_link_value[0] %}
        {% set main_image_link = url('brand.show', {'id': selected_item.id}) %}
    {% elseif component.main_image_link_type == 'pages' and component.main_image_link_value and component.main_image_link_value|length > 0 %}
        {% set selected_item = component.main_image_link_value[0] %}
        {% set main_image_link = url('page.show', {'id': selected_item.id}) %}
    {% elseif component.main_image_link_type == 'blog_articles' and component.main_image_link_value and component.main_image_link_value|length > 0 %}
        {% set selected_item = component.main_image_link_value[0] %}
        {% set main_image_link = url('blog.article.show', {'id': selected_item.id}) %}
    {% elseif component.main_image_link_type == 'blog_categories' and component.main_image_link_value and component.main_image_link_value|length > 0 %}
        {% set selected_item = component.main_image_link_value[0] %}
        {% set main_image_link = url('blog.category.show', {'id': selected_item.id}) %}
    {% elseif component.main_image_link_type == 'offers_link' %}
        {% set main_image_link = url('offers.index') %}
    {% elseif component.main_image_link_type == 'brands_link' %}
        {% set main_image_link = url('brands.index') %}
    {% elseif component.main_image_link_type == 'blog_link' %}
        {% set main_image_link = url('blog.index') %}
    {% endif %}
{% endif %}

<style>
/* Product with Icons Component Styles - Inline for immediate effect */
.product-with-icons-section {
  padding: 4rem 2rem;
  margin: 3rem auto;
  max-width: 1200px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 25px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

.product-with-icons-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%);
}

/* Section Header Styles */
.section-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 10;
  transform: translateY(30px);
  opacity: 0;
  animation: fadeInUp 0.8s ease-out forwards;
  animation-play-state: paused;
}

.product-with-icons-section .section-title {
  color: #ffffff;
  font-size: 2.75rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-align: center;
  line-height: 1.2;
  position: relative;
  display: inline-block;
}

.product-with-icons-section .section-title::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
  border-radius: 2px;
  animation: gradientShift 3s ease-in-out infinite;
}

.product-with-icons-section .section-description {
  color: #b8c6db;
  font-size: 1.2rem;
  line-height: 1.7;
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  font-weight: 400;
  opacity: 0.9;
}

.product-with-icons-grid {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4rem;
  position: relative;
  z-index: 10;
  max-width: 1000px;
  margin: 0 auto;
}

.icons-section {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  flex: 1;
  max-width: 250px;
}

.icons-section .icons-container {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem 1.5rem;
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: scale(0.8);
  opacity: 0;
  animation: popupAnimation 0.6s ease-out forwards;
  animation-play-state: paused;
}

.icon-item::before {
  content: '';
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  left: -100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.icon-item:hover {
  transform: translateY(-0.75rem) scale(1.05);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 255, 255, 0.1);
}

.icon-item:hover::before {
  left: 100%;
}

.icon-item:hover .icon-wrapper {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.icon-item:hover .icon-wrapper::before {
  opacity: 1;
}

.icon-item:hover .icon-symbol {
  transform: scale(1.2) rotate(5deg);
}



.icon-wrapper {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.icon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  transition: opacity 0.3s ease;
}

.icon-symbol {
  font-size: 2rem;
  color: #ffffff;
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
}

.icon-title {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.375;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.main-image-section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 2;
  max-width: 400px;
}

.main-image-section .image-wrapper {
  width: 100%;
  position: relative;
  transform: scale(0.8);
  opacity: 0;
  animation: popupAnimation 0.8s ease-out 0.3s forwards;
  animation-play-state: paused;
}

.main-image-link {
  display: block;
  text-decoration: none;
}

.main-image {
  width: 100%;
  height: auto;
  max-height: 350px;
  object-fit: cover;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.main-image:hover {
  transform: translateY(-15px) scale(1.05);
  box-shadow: 0 40px 80px rgba(0, 0, 0, 0.5), 0 0 40px rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes popupAnimation {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Animation delays for staggered effect */
.icon-item:nth-child(1) {
  animation-delay: 0.1s;
}

.icon-item:nth-child(2) {
  animation-delay: 0.3s;
}

.icon-item:nth-child(3) {
  animation-delay: 0.5s;
}

.icon-item:nth-child(4) {
  animation-delay: 0.7s;
}

@media (max-width: 1024px) {
  .product-with-icons-section {
    padding: 3rem 1.5rem;
    margin: 2rem auto;
  }

  .product-with-icons-grid {
    flex-direction: column;
    gap: 3rem;
    max-width: 600px;
  }

  .icons-section {
    max-width: 100%;
    order: 2;
  }

  .icons-section .icons-container {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    gap: 2rem;
  }

  .main-image-section {
    order: 1;
    max-width: 350px;
  }

  .icon-item {
    flex: 0 0 calc(50% - 1rem);
    min-width: 200px;
    max-width: 250px;
  }
}

@media (max-width: 768px) {
  .product-with-icons-section {
    padding: 2.5rem 1rem;
    margin: 1.5rem auto;
    border-radius: 20px;
  }

  .section-header {
    margin-bottom: 2.5rem;
  }

  .product-with-icons-section .section-title {
    font-size: 2.25rem;
  }

  .product-with-icons-section .section-description {
    font-size: 1.1rem;
    max-width: 500px;
  }

  .product-with-icons-grid {
    gap: 2.5rem;
  }

  .icon-item {
    flex: 0 0 100%;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    padding: 1.5rem;
  }

  .icon-wrapper {
    width: 4.5rem;
    height: 4.5rem;
  }

  .icon-symbol {
    font-size: 1.75rem;
  }

  .icon-title {
    font-size: 0.95rem;
  }

  .main-image-section {
    max-width: 280px;
  }

  .main-image {
    max-height: 280px;
  }
}

@media (max-width: 480px) {
  .product-with-icons-section {
    padding: 2rem 0.75rem;
    margin: 1rem auto;
  }

  .section-header {
    margin-bottom: 2rem;
  }

  .product-with-icons-section .section-title {
    font-size: 1.9rem;
  }

  .product-with-icons-section .section-description {
    font-size: 1rem;
    max-width: 400px;
  }

  .icons-section .icons-container {
    gap: 1.5rem;
  }

  .icon-item {
    padding: 1.25rem;
    max-width: 280px;
  }

  .icon-wrapper {
    width: 4rem;
    height: 4rem;
  }

  .icon-symbol {
    font-size: 1.5rem;
  }
}
</style>

{% set section_id = 'product-with-icons-' ~ random() %}
<section class="product-with-icons-section s-block container" id="{{ section_id }}">
    {# Section Header #}
    {% if component.title or component.description %}
        <div class="section-header">
            {% if component.title %}
                <h2 class="section-title">{{ component.title }}</h2>
            {% endif %}
            {% if component.description %}
                <p class="section-description">{{ component.description }}</p>
            {% endif %}
        </div>
    {% endif %}

    {# Main Content Grid #}
    <div class="product-with-icons-grid">
        {# Left Icons Section #}
        {% if component.left_icons and component.left_icons|length > 0 %}
            <div class="icons-section left-icons">
                <div class="icons-container">
                    {% for icon in component.left_icons %}
                        <div class="icon-item">
                            <div class="icon-wrapper">
                                <i class="{{ icon.icon }} icon-symbol"></i>
                            </div>

                            {% if icon.title %}
                                <h3 class="icon-title">{{ icon.title }}</h3>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        {# Main Image Section #}
        {% if component.main_image %}
            <div class="main-image-section">
                <div class="image-wrapper">
                    {% if main_image_link %}
                        <a href="{{ main_image_link }}" class="main-image-link"
                           aria-label="Main Image Link"
                           rel="noopener">
                    {% endif %}

                    <img src="{{ component.main_image }}"
                         alt="{{ component.title|default('Main Image') }}"
                         class="main-image"
                         loading="lazy">

                    {% if main_image_link %}
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}

        {# Right Icons Section #}
        {% if component.right_icons and component.right_icons|length > 0 %}
            <div class="icons-section right-icons">
                <div class="icons-container">
                    {% for icon in component.right_icons %}
                        <div class="icon-item">
                            <div class="icon-wrapper">
                                <i class="{{ icon.icon }} icon-symbol"></i>
                            </div>

                            {% if icon.title %}
                                <h3 class="icon-title">{{ icon.title }}</h3>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>
</section>

<script>
// Intersection Observer for popup animation
document.addEventListener('DOMContentLoaded', function() {
    const section = document.querySelector('#{{ section_id }}');
    if (!section) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Add animation class to trigger popup
                entry.target.classList.add('animate-in');

                // Trigger animations for all elements
                const sectionHeader = entry.target.querySelector('.section-header');
                const iconItems = entry.target.querySelectorAll('.icon-item');
                const mainImage = entry.target.querySelector('.main-image-section .image-wrapper');

                // Animate header first
                if (sectionHeader) {
                    sectionHeader.style.animationPlayState = 'running';
                }

                // Animate icons with staggered delay
                iconItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.style.animationPlayState = 'running';
                    }, 200 + (index * 150));
                });

                // Animate main image
                if (mainImage) {
                    setTimeout(() => {
                        mainImage.style.animationPlayState = 'running';
                    }, 500);
                }

                // Stop observing after animation
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.3,
        rootMargin: '0px 0px -50px 0px'
    });

    observer.observe(section);
});
</script>