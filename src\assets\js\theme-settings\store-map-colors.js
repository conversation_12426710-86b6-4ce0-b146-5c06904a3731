/**
 * Store Map Colors Theme Settings Handler
 * Handles theme customization for store map component colors and styling
 */

(function() {
    'use strict';

    // Theme settings configuration for store map colors
    const storeMapColorSettings = {
        // Background settings
        background_enabled: {
            selector: '.s-block--store-map',
            property: 'background-enabled',
            type: 'class',
            defaultValue: false
        },
        background_color: {
            selector: '.s-block--store-map',
            property: 'background-color',
            type: 'style',
            defaultValue: '#f8f9fa'
        },
        
        // Text color settings
        title_color: {
            selector: '.s-block--store-map .store-map-title',
            property: 'color',
            type: 'style',
            defaultValue: '#212529'
        },
        description_color: {
            selector: '.s-block--store-map .store-map-description',
            property: 'color',
            type: 'style',
            defaultValue: '#6c757d'
        },
        branch_name_color: {
            selector: '.s-block--store-map .branch-name',
            property: 'color',
            type: 'style',
            defaultValue: '#495057'
        },
        branch_address_color: {
            selector: '.s-block--store-map .branch-address',
            property: 'color',
            type: 'style',
            defaultValue: '#6c757d'
        },
        
        // Border settings
        border_enabled: {
            selector: '.s-block--store-map',
            property: 'border-enabled',
            type: 'class',
            defaultValue: false
        },
        border_color: {
            selector: '.s-block--store-map',
            property: 'border-color',
            type: 'style',
            defaultValue: '#dee2e6'
        },
        border_radius: {
            selector: '.s-block--store-map',
            property: 'border-radius',
            type: 'style',
            unit: 'px',
            defaultValue: 8
        },
        
        // Padding settings
        padding_enabled: {
            selector: '.s-block--store-map',
            property: 'padding-enabled',
            type: 'class',
            defaultValue: true
        },
        padding_size: {
            selector: '.s-block--store-map',
            property: 'padding-size',
            type: 'class',
            defaultValue: 'medium'
        }
    };

    function initStoreMapColorSettings() {
        // Wait for theme customizer to be ready
        if (typeof window.ThemeCustomizer !== 'undefined') {
            registerStoreMapColorSettings();
        } else {
            // Wait for theme customizer to load
            document.addEventListener('theme-customizer-ready', registerStoreMapColorSettings);
        }
    }

    function registerStoreMapColorSettings() {
        // Register each setting with the theme customizer
        Object.keys(storeMapColorSettings).forEach(settingKey => {
            const setting = storeMapColorSettings[settingKey];
            
            if (window.ThemeCustomizer && window.ThemeCustomizer.registerSetting) {
                window.ThemeCustomizer.registerSetting(settingKey, {
                    ...setting,
                    callback: (value) => applyStoreMapColorSetting(settingKey, value)
                });
            }
        });
    }

    function applyStoreMapColorSetting(settingKey, value) {
        const setting = storeMapColorSettings[settingKey];
        if (!setting) return;

        const elements = document.querySelectorAll(setting.selector);
        
        elements.forEach(element => {
            switch (setting.type) {
                case 'style':
                    if (setting.unit) {
                        element.style[setting.property] = value + setting.unit;
                    } else {
                        element.style[setting.property] = value;
                    }
                    break;
                    
                case 'class':
                    if (settingKey.includes('enabled')) {
                        // Handle boolean class toggles
                        const className = settingKey.replace('_enabled', '-enabled');
                        const disabledClassName = settingKey.replace('_enabled', '-disabled');
                        
                        if (value) {
                            element.classList.add(className);
                            element.classList.remove(disabledClassName);
                        } else {
                            element.classList.remove(className);
                            element.classList.add(disabledClassName);
                        }
                    } else if (settingKey.includes('size')) {
                        // Handle size class changes
                        const baseClass = settingKey.replace('_size', '');
                        const possibleValues = ['small', 'medium', 'large', 'xlarge'];
                        
                        // Remove all possible size classes
                        possibleValues.forEach(size => {
                            element.classList.remove(`${baseClass}-${size}`);
                        });
                        
                        // Add the selected size class
                        element.classList.add(`${baseClass}-${value}`);
                    }
                    break;
            }
        });

        // Update CSS custom properties for better integration
        updateCSSCustomProperties(settingKey, value);
    }

    function updateCSSCustomProperties(settingKey, value) {
        const root = document.documentElement;
        
        switch (settingKey) {
            case 'title_color':
                root.style.setProperty('--component-title-color', value);
                break;
            case 'description_color':
                root.style.setProperty('--component-description-color', value);
                break;
            case 'branch_name_color':
                root.style.setProperty('--component-branch-name-color', value);
                break;
            case 'branch_address_color':
                root.style.setProperty('--component-branch-address-color', value);
                break;
            case 'background_color':
                root.style.setProperty('--component-bg-color', value);
                break;
            case 'border_color':
                root.style.setProperty('--component-border-color', value);
                break;
        }
    }

    function applyAllStoreMapColorSettings(settings) {
        Object.keys(settings).forEach(settingKey => {
            if (storeMapColorSettings[settingKey]) {
                applyStoreMapColorSetting(settingKey, settings[settingKey]);
            }
        });
    }

    function resetStoreMapColorSettings() {
        Object.keys(storeMapColorSettings).forEach(settingKey => {
            const setting = storeMapColorSettings[settingKey];
            applyStoreMapColorSetting(settingKey, setting.defaultValue);
        });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initStoreMapColorSettings);
    } else {
        initStoreMapColorSettings();
    }

    // Export for external use
    window.StoreMapColorSettings = {
        apply: applyAllStoreMapColorSettings,
        reset: resetStoreMapColorSettings,
        applySingle: applyStoreMapColorSetting,
        settings: storeMapColorSettings
    };

})();
