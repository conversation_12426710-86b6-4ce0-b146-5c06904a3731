/* Gaming Loading Screen - Optimized for Performance */
:root {
    --gaming-primary: #1DE9B6;
    --gaming-primary-rgb: 29, 233, 182;
    --gaming-bg: #0a0e12;
    --gaming-white: #ffffff;
    --gaming-white-alpha-10: rgba(255, 255, 255, 0.1);
    --gaming-primary-alpha-30: rgba(29, 233, 182, 0.3);
    --gaming-primary-alpha-40: rgba(29, 233, 182, 0.4);
    --gaming-primary-alpha-50: rgba(29, 233, 182, 0.5);
    --gaming-primary-alpha-80: rgba(29, 233, 182, 0.8);
}

.gaming-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gaming-bg);
    transition: opacity 0.75s ease, visibility 0.75s ease;
    z-index: var(--z-loading);
    overflow: hidden;
    will-change: opacity, visibility;
}

.gaming-loader-content {
    position: relative;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 280px;
    will-change: transform;
}

.gaming-loader-logo {
    position: relative;
    margin-bottom: 40px;
    width: 100%;
    text-align: center;
}

.gaming-logo-text {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--gaming-white);
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 15px var(--gaming-primary-alpha-80);
    animation: pulse 2s infinite ease-in-out;
    will-change: opacity, text-shadow;
}

.gaming-loader-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, var(--gaming-primary-alpha-40) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: glow 3s infinite ease-in-out alternate;
    will-change: opacity, transform;
}

.gaming-loader-image {
    margin-bottom: 40px;
}

.gaming-loader-image img {
    max-width: 200px;
    height: auto;
    border-radius: 10px;
    border: 2px solid var(--gaming-primary-alpha-30);
    box-shadow: 0 0 20px var(--gaming-primary-alpha-30);
}

.gaming-loader-progress {
    width: 100%;
    height: 4px;
    background: var(--gaming-white-alpha-10);
    border-radius: 4px;
    margin-bottom: 20px;
    overflow: hidden;
    position: relative;
}

.gaming-loader-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, var(--gaming-primary-alpha-50), var(--gaming-primary));
    box-shadow: 0 0 10px var(--gaming-primary-alpha-80);
    border-radius: 4px;
    animation: progress 3s ease-in-out infinite;
    will-change: width;
}

.gaming-loader-text {
    color: var(--gaming-primary);
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 2px;
    text-transform: uppercase;
    display: flex;
    align-items: center;
}

.gaming-loader-dots span {
    opacity: 0;
    animation: dot 1.5s infinite;
    will-change: opacity;
}

.gaming-loader-dots span:nth-child(2) {
    animation-delay: 0.5s;
}

.gaming-loader-dots span:nth-child(3) {
    animation-delay: 1s;
}

/* Optimized background pattern - smaller and more efficient */
.gaming-loader-circuit-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' width='100' height='100'%3E%3Cpath fill='%231de9b6' fill-opacity='0.05' d='M10 10h5v5h-5zm20 0h5v5h-5zm20 0h5v5h-5zm20 0h5v5h-5zm20 0h5v5h-5zM10 30h5v5h-5zm40 0h5v5h-5zm20 0h5v5h-5zM10 50h5v5h-5zm20 0h5v5h-5zm40 0h5v5h-5zM10 70h5v5h-5zm40 0h5v5h-5zm20 0h5v5h-5zM10 90h5v5h-5zm20 0h5v5h-5zm20 0h5v5h-5zm20 0h5v5h-5zm20 0h5v5h-5z'/%3E%3C/svg%3E");
    opacity: 0.15;
    z-index: 1;
}

/* Optimized static particles */
.gaming-particle {
    position: absolute;
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background-color: var(--gaming-primary-alpha-80);
    box-shadow: 0 0 8px 1px var(--gaming-primary-alpha-80);
    z-index: 2;
    will-change: transform;
}

.gaming-particle-1 {
    top: 20%;
    left: 15%;
    animation: float1 8s infinite ease-in-out;
}

.gaming-particle-2 {
    top: 70%;
    right: 20%;
    animation: float2 10s infinite ease-in-out 2s;
}

.gaming-particle-3 {
    bottom: 40%;
    left: 80%;
    animation: float3 12s infinite ease-in-out 4s;
}

/* Optimized Animations - using only transform and opacity */
@keyframes progress {
    0% { width: 0%; }
    50% { width: 100%; }
    100% { width: 0%; }
}

@keyframes dot {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    80%, 100% { opacity: 0; }
}

@keyframes pulse {
    0% { 
        opacity: 0.8; 
        text-shadow: 0 0 5px var(--gaming-primary-alpha-50); 
    }
    50% { 
        opacity: 1; 
        text-shadow: 0 0 15px var(--gaming-primary-alpha-80); 
    }
    100% { 
        opacity: 0.8; 
        text-shadow: 0 0 5px var(--gaming-primary-alpha-50); 
    }
}

@keyframes glow {
    0% { 
        opacity: 0.5; 
        transform: translate(-50%, -50%) scale(0.8); 
    }
    100% { 
        opacity: 0.8; 
        transform: translate(-50%, -50%) scale(1.2); 
    }
}

@keyframes float1 {
    0% { transform: translate(0, 0); }
    25% { transform: translate(-15px, -20px); }
    50% { transform: translate(10px, -10px); }
    75% { transform: translate(20px, 15px); }
    100% { transform: translate(0, 0); }
}

@keyframes float2 {
    0% { transform: translate(0, 0); }
    33% { transform: translate(15px, -25px); }
    66% { transform: translate(-10px, 20px); }
    100% { transform: translate(0, 0); }
}

@keyframes float3 {
    0% { transform: translate(0, 0); }
    20% { transform: translate(-20px, -15px); }
    40% { transform: translate(25px, -5px); }
    60% { transform: translate(-15px, 25px); }
    80% { transform: translate(10px, -20px); }
    100% { transform: translate(0, 0); }
}

/* Hide loader */
.gaming-loader.loaded {
    opacity: 0;
    visibility: hidden;
}

/* Performance optimizations for mobile */
@media (max-width: 768px) {
    .gaming-logo-text {
        font-size: 2rem;
    }
    
    .gaming-loader-content {
        width: 240px;
    }
    
    /* Reduce particles on mobile */
    .gaming-particle-3 {
        display: none;
    }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .gaming-loader * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
