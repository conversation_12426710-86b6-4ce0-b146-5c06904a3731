/**
 * Special Banners Component - Optimized Performance
 * Handles scrolling, animations, and lazy loading
 */
(function() {
    'use strict';
    
    let isInitialized = false;
    const bannerInstances = new Map();
    
    // Optimized banner controller
    class SpecialBannersController {
        constructor(container) {
            this.container = container;
            this.scrollContainer = container.querySelector('.special-banners__container > div');
            this.prevBtn = container.querySelector('.scroll-btn.prev');
            this.nextBtn = container.querySelector('.scroll-btn.next');
            this.bannerItems = container.querySelectorAll('.special-banner-item');
            this.images = container.querySelectorAll('.special-banner-image img');
            
            this.isScrolling = false;
            this.animationObserver = null;
            this.lazyObserver = null;
            
            this.init();
        }
        
        init() {
            if (!this.scrollContainer || this.bannerItems.length === 0) return;
            
            this.initScrollButtons();
            this.initAnimations();
            this.initLazyLoading();
            this.bindEvents();
            
            // Animate first banner immediately
            requestAnimationFrame(() => {
                if (this.bannerItems[0]) {
                    this.bannerItems[0].classList.add('animated');
                }
            });
        }
        
        initScrollButtons() {
            if (!this.prevBtn || !this.nextBtn) return;
            
            this.prevBtn.addEventListener('click', () => this.scrollPrev(), { passive: true });
            this.nextBtn.addEventListener('click', () => this.scrollNext(), { passive: true });
            
            this.updateScrollButtons();
        }
        
        scrollPrev() {
            if (this.isScrolling) return;
            
            this.isScrolling = true;
            const itemWidth = this.bannerItems[0]?.offsetWidth || 300;
            
            this.scrollContainer.scrollBy({
                left: -itemWidth,
                behavior: 'smooth'
            });
            
            setTimeout(() => {
                this.isScrolling = false;
            }, 500);
        }
        
        scrollNext() {
            if (this.isScrolling) return;
            
            this.isScrolling = true;
            const itemWidth = this.bannerItems[0]?.offsetWidth || 300;
            
            this.scrollContainer.scrollBy({
                left: itemWidth,
                behavior: 'smooth'
            });
            
            setTimeout(() => {
                this.isScrolling = false;
            }, 500);
        }
        
        updateScrollButtons() {
            if (!this.prevBtn || !this.nextBtn) return;
            
            const scrollLeft = this.scrollContainer.scrollLeft;
            const maxScrollLeft = this.scrollContainer.scrollWidth - this.scrollContainer.clientWidth;
            
            // Update prev button
            this.prevBtn.disabled = scrollLeft <= 0;
            this.prevBtn.style.opacity = scrollLeft <= 0 ? '0.5' : '1';
            
            // Update next button
            this.nextBtn.disabled = scrollLeft >= maxScrollLeft;
            this.nextBtn.style.opacity = scrollLeft >= maxScrollLeft ? '0.5' : '1';
        }
        
        initAnimations() {
            if (!('IntersectionObserver' in window)) {
                // Fallback for older browsers
                this.bannerItems.forEach(item => {
                    item.classList.add('animated');
                });
                return;
            }
            
            this.animationObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        // Staggered animation with delay
                        setTimeout(() => {
                            entry.target.classList.add('animated');
                        }, index * 100);
                        
                        this.animationObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.2,
                rootMargin: '0px 0px -10% 0px'
            });
            
            // Observe banner items with staggered delays
            this.bannerItems.forEach((item, index) => {
                setTimeout(() => {
                    this.animationObserver.observe(item);
                }, index * 50);
            });
        }
        
        initLazyLoading() {
            if (!this.images.length) return;
            
            if ('IntersectionObserver' in window) {
                this.lazyObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            this.loadImage(entry.target);
                            this.lazyObserver.unobserve(entry.target);
                        }
                    });
                }, {
                    rootMargin: '100px'
                });
                
                this.images.forEach(img => {
                    if (img.dataset.src || !img.complete) {
                        img.classList.add('lazy');
                        this.lazyObserver.observe(img);
                    }
                });
            } else {
                // Fallback: load all images immediately
                this.images.forEach(img => this.loadImage(img));
            }
        }
        
        loadImage(img) {
            const imageContainer = img.closest('.special-banner-image');
            if (imageContainer) {
                imageContainer.classList.add('loading');
            }
            
            // If image has data-src, use it
            if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            }
            
            const onLoad = () => {
                img.classList.remove('lazy');
                img.classList.add('loaded');
                if (imageContainer) {
                    imageContainer.classList.remove('loading');
                }
                img.removeEventListener('load', onLoad);
                img.removeEventListener('error', onError);
            };
            
            const onError = () => {
                img.classList.remove('lazy');
                if (imageContainer) {
                    imageContainer.classList.remove('loading');
                    // Add error state
                    imageContainer.style.background = '#f3f4f6';
                }
                img.removeEventListener('load', onLoad);
                img.removeEventListener('error', onError);
            };
            
            if (img.complete && img.naturalHeight !== 0) {
                onLoad();
            } else {
                img.addEventListener('load', onLoad, { once: true });
                img.addEventListener('error', onError, { once: true });
            }
        }
        
        bindEvents() {
            // Throttled scroll event
            let scrollTimeout;
            this.scrollContainer.addEventListener('scroll', () => {
                if (scrollTimeout) return;
                
                scrollTimeout = setTimeout(() => {
                    this.updateScrollButtons();
                    scrollTimeout = null;
                }, 100);
            }, { passive: true });
            
            // Throttled resize event
            let resizeTimeout;
            window.addEventListener('resize', () => {
                if (resizeTimeout) return;
                
                resizeTimeout = setTimeout(() => {
                    this.updateScrollButtons();
                    resizeTimeout = null;
                }, 250);
            }, { passive: true });
        }
        
        destroy() {
            if (this.animationObserver) {
                this.animationObserver.disconnect();
                this.animationObserver = null;
            }
            
            if (this.lazyObserver) {
                this.lazyObserver.disconnect();
                this.lazyObserver = null;
            }
            
            // Remove event listeners
            if (this.prevBtn) {
                this.prevBtn.removeEventListener('click', this.scrollPrev);
            }
            if (this.nextBtn) {
                this.nextBtn.removeEventListener('click', this.scrollNext);
            }
        }
    }
    
    // Initialize special banners
    function initSpecialBanners() {
        if (isInitialized) return;
        
        const bannerSections = document.querySelectorAll('.special-banners');
        
        bannerSections.forEach((section, index) => {
            const id = section.id || `special-banners-${index}`;
            
            if (!bannerInstances.has(id)) {
                const controller = new SpecialBannersController(section);
                bannerInstances.set(id, controller);
            }
        });
        
        isInitialized = true;
    }
    
    // Cleanup function
    function cleanup() {
        bannerInstances.forEach(controller => {
            controller.destroy();
        });
        bannerInstances.clear();
        isInitialized = false;
    }
    
    // Performance-optimized initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSpecialBanners, { once: true });
    } else {
        requestAnimationFrame(initSpecialBanners);
    }
    
    // Expose for manual control
    window.specialBannersController = {
        init: initSpecialBanners,
        cleanup,
        instances: bannerInstances
    };
    
})();
