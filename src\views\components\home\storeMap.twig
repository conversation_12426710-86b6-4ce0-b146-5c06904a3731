{# Set component styling classes based on settings #}
{% set component_classes = ['s-block', 's-block--store-map'] %}

{# Background styling #}
{% if component.background_enabled %}
    {% set component_classes = component_classes|merge(['background-enabled']) %}
{% else %}
    {% set component_classes = component_classes|merge(['background-disabled']) %}
{% endif %}

{# Border styling #}
{% if component.border_enabled %}
    {% set component_classes = component_classes|merge(['border-enabled']) %}
{% else %}
    {% set component_classes = component_classes|merge(['border-disabled']) %}
{% endif %}

{# Padding styling #}
{% if component.padding_enabled %}
    {% set component_classes = component_classes|merge(['padding-' ~ (component.padding_size ?: 'medium')]) %}
{% else %}
    {% set component_classes = component_classes|merge(['padding-disabled']) %}
{% endif %}

<div class="{{ component_classes|join(' ') }}"
     id="store-map-{{ position }}"
     data-layout="{{ component.layout_style|default('grid') }}"
     data-background-enabled="{{ component.background_enabled ? 'true' : 'false' }}"
     data-background-color="{{ component.background_color ?: '#f8f9fa' }}"
     data-title-color="{{ component.title_color ?: '#212529' }}"
     data-description-color="{{ component.description_color ?: '#6c757d' }}"
     data-branch-name-color="{{ component.branch_name_color ?: '#495057' }}"
     data-branch-address-color="{{ component.branch_address_color ?: '#6c757d' }}"
     data-border-enabled="{{ component.border_enabled ? 'true' : 'false' }}"
     data-border-color="{{ component.border_color ?: '#dee2e6' }}"
     data-border-radius="{{ component.border_radius ?: 8 }}"
     data-padding-enabled="{{ component.padding_enabled ? 'true' : 'false' }}"
     data-padding-size="{{ component.padding_size ?: 'medium' }}"
     style="
        {% if component.background_enabled %}background-color: {{ component.background_color ?: '#f8f9fa' }};{% endif %}
        {% if component.border_enabled %}border: 1px solid {{ component.border_color ?: '#dee2e6' }};{% endif %}
        border-radius: {{ component.border_radius ?: 8 }}px;
        --title-color: {{ component.title_color ?: '#212529' }};
        --description-color: {{ component.description_color ?: '#6c757d' }};
        --branch-name-color: {{ component.branch_name_color ?: '#495057' }};
        --branch-address-color: {{ component.branch_address_color ?: '#6c757d' }};
        --component-bg-color: {{ component.background_color ?: '#f8f9fa' }};
        --component-border-color: {{ component.border_color ?: '#dee2e6' }};
     ">
    <div class="container">
        {% if component.main_title %}
            <h2 class="store-map-title text-center mb-4" style="color: {{ component.title_color ?: '#212529' }};">
                {{ component.main_title }}
            </h2>
        {% endif %}
        {% if component.description %}
            <p class="store-map-description text-center mb-8" style="color: {{ component.description_color ?: '#6c757d' }};">
                {{ component.description }}
            </p>
        {% endif %}

        {% if component.store_branches and component.store_branches|length > 0 %}
            {% set layout = component.layout_style|default('grid') %}

            {% if layout == 'tabs' %}
                <!-- نمط التبويبات -->
                <div class="store-branches-tabs">
                    <!-- أزرار التبويبات -->
                    <div class="tabs-navigation mb-6">
                        {% for branch in component.store_branches %}
                            <button class="tab-button {% if loop.first %}active{% endif %}"
                                    data-tab="branch-{{ loop.index0 }}"
                                    style="color: {{ component.branch_name_color ?: '#495057' }};">
                                {{ branch.branch_name|default('فرع ' ~ loop.index) }}
                            </button>
                        {% endfor %}
                    </div>

                    <!-- محتوى التبويبات -->
                    <div class="tabs-content">
                        {% for branch in component.store_branches %}
                            <div class="tab-panel {% if loop.first %}active{% endif %}"
                                 id="branch-{{ loop.index0 }}">
                                <div class="branch-item">
                                    <!-- معلومات الفرع -->
                                    {% if component.show_branch_info|default(true) %}
                                        <div class="branch-info mb-4">
                                            {% if branch.branch_name %}
                                                <h3 class="branch-name text-lg font-bold">{{ branch.branch_name }}</h3>
                                            {% endif %}
                                            {% if branch.branch_address %}
                                                <p class="branch-address text-gray-600">
                                                    <i class="sicon-location me-2"></i>
                                                    {{ branch.branch_address }}
                                                </p>
                                            {% endif %}
                                        </div>
                                    {% endif %}

                                    <!-- خريطة الفرع -->
                                    <div class="map-container"
                                         style="height: {{ component.map_height|default(400) }}px;"
                                         data-embed-code="{{ branch.google_maps_embed }}">
                                        <div class="map-placeholder">
                                            <div class="text-center">
                                                <i class="sicon-map text-4xl mb-2"></i>
                                                <p>جاري تحميل الخريطة...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% else %}
                <!-- نمط الشبكة أو القائمة -->
                <div class="store-branches-container" data-layout="{{ layout }}">
                    {% for branch in component.store_branches %}
                        <div class="branch-item mb-8">
                            {% if component.show_branch_info|default(true) %}
                                <div class="branch-info mb-4">
                                    <h3 class="branch-name text-lg font-bold" style="color: {{ component.branch_name_color ?: '#495057' }};">
                                        {{ branch.branch_name }}
                                    </h3>
                                    <p class="branch-address text-gray-600" style="color: {{ component.branch_address_color ?: '#6c757d' }};">
                                        <i class="sicon-location me-2"></i>
                                        {{ branch.branch_address }}
                                    </p>
                                </div>
                            {% endif %}

                            <div class="map-container"
                                 style="height: {{ component.map_height|default(400) }}px;"
                                 data-embed-code="{{ branch.google_maps_embed }}">
                                <div class="map-placeholder">
                                    <div class="text-center">
                                        <i class="sicon-map text-4xl mb-2"></i>
                                        <p>جاري تحميل الخريطة...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% else %}
            <div class="no-branches-placeholder text-center py-16">
                <i class="sicon-store text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-bold mb-2">لا توجد فروع مضافة حالياً</h3>
                <p class="text-gray-600">يمكنك إضافة فروع المتجر من إعدادات الكومبوننت</p>
            </div>
        {% endif %}
    </div>
</div>

