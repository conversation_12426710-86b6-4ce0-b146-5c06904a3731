/**
 * Salla ProductList Animation Component
 * Adds gentle bouncing animations to product cards when they come into view
 * Similar to lazy loading behavior
 */
(function() {
    'use strict';
    
    let isInitialized = false;
    const productListObservers = new Map();
    
    class ProductListAnimator {
        constructor(productList) {
            this.productList = productList;
            this.observer = null;
            this.animatedProducts = new Set();
            this.isReady = false;
        }
        
        init() {
            // Wait for the product list to be ready
            this.waitForProducts();
        }
        
        waitForProducts() {
            // Check if products are already loaded
            const products = this.productList.querySelectorAll('custom-salla-product-card');
            
            if (products.length > 0) {
                this.setupAnimation();
            } else {
                // Use MutationObserver to watch for products being added
                const mutationObserver = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            const addedProducts = Array.from(mutation.addedNodes)
                                .filter(node => node.nodeType === 1 && node.tagName === 'CUSTOM-SALLA-PRODUCT-CARD');
                            
                            if (addedProducts.length > 0) {
                                this.setupAnimation();
                                mutationObserver.disconnect();
                            }
                        }
                    });
                });
                
                mutationObserver.observe(this.productList, {
                    childList: true,
                    subtree: true
                });
                
                // Fallback timeout
                setTimeout(() => {
                    mutationObserver.disconnect();
                    this.setupAnimation();
                }, 3000);
            }
        }
        
        setupAnimation() {
            if (this.isReady || !('IntersectionObserver' in window)) {
                this.fallbackAnimation();
                return;
            }
            
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateProduct(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
            
            this.observeProducts();
            this.isReady = true;
            
            // Watch for new products being added (pagination, filtering, etc.)
            this.watchForNewProducts();
        }
        
        observeProducts() {
            const products = this.productList.querySelectorAll('custom-salla-product-card');

            // Note: Product card animations are now handled by the Universal Product Cards Animation system
            // This component now focuses on other product list functionality

            products.forEach((product, index) => {
                if (!this.animatedProducts.has(product)) {
                    // Let the universal system handle the animation
                    // Just observe for other functionality if needed
                    this.animatedProducts.add(product);
                }
            });
        }
        
        animateProduct(product) {
            if (this.animatedProducts.has(product)) return;

            this.animatedProducts.add(product);

            // Product animations are now handled by the Universal Product Cards Animation system
            // This method is kept for compatibility but delegates to the universal system
            if (window.animateProductCards) {
                window.animateProductCards([product]);
            }
        }
        
        watchForNewProducts() {
            const mutationObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        const addedProducts = Array.from(mutation.addedNodes)
                            .filter(node => node.nodeType === 1 && node.tagName === 'CUSTOM-SALLA-PRODUCT-CARD');
                        
                        if (addedProducts.length > 0) {
                            // Small delay to ensure products are fully rendered
                            setTimeout(() => {
                                this.observeProducts();
                            }, 100);
                        }
                    }
                });
            });
            
            mutationObserver.observe(this.productList, {
                childList: true,
                subtree: true
            });
        }
        
        fallbackAnimation() {
            // Immediate animation for browsers without IntersectionObserver
            const products = this.productList.querySelectorAll('custom-salla-product-card');
            products.forEach((product, index) => {
                setTimeout(() => {
                    this.animateProduct(product);
                }, index * 100);
            });
        }
        
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            this.animatedProducts.clear();
        }
    }
    
    function initProductList(productList) {
        const listId = productList.id || `productlist-${Date.now()}-${Math.random()}`;
        
        if (productListObservers.has(listId)) return;
        
        const animator = new ProductListAnimator(productList);
        productListObservers.set(listId, animator);
        
        animator.init();
    }
    
    function init() {
        if (isInitialized) return;
        
        const productLists = document.querySelectorAll('salla-products-list');
        productLists.forEach(initProductList);
        
        // Watch for dynamically added product lists
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    const addedLists = Array.from(mutation.addedNodes)
                        .filter(node => node.nodeType === 1 && node.tagName === 'SALLA-PRODUCTS-LIST');
                    
                    addedLists.forEach(initProductList);
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        isInitialized = true;
    }
    
    function cleanup() {
        productListObservers.forEach(animator => animator.destroy());
        productListObservers.clear();
        isInitialized = false;
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        requestAnimationFrame(init);
    }
    
    // Expose for manual control
    window.sallaProductListAnimator = {
        init,
        cleanup,
        initList: initProductList
    };
    
})();
