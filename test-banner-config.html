<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بنر مع عرض</title>
    <link rel="stylesheet" href="assets/css/components/banner-with-offer.css">
</head>
<body style="background: #f0f0f0; padding: 20px;">
    
    <h1>اختبار بنر مع عرض - الإعدادات المحدثة</h1>
    
    <!-- Test Banner Component -->
    <section class="s-block s-block--banner-with-offer container">
        <div class="s-block__title">
            <div class="right-side">
                <h2>عرض خاص محدود الوقت</h2>
            </div>
        </div>

        <div class="banner-offer-wrapper"
             id="offer-component-test-banner-123"
             style="--offer-bg-color: #f5f5f5; --gaming-primary: #00bfe0; --timer-text-color: #ffffff;">
            <div class="offer-container fadeIn"
                 style="background-color: #f5f5f5 !important;">
                
                <!-- Banner Image -->
                <img src="https://cdn.salla.sa/mQgZlG/pzOCEw10wCfXeVSpKnL0Ifd1O9ZkTfnZ17RkE14R.jpg" 
                     alt="Special Offer"
                     class="banner-image"
                     decoding="async"
                     style="opacity: 1 !important; visibility: visible !important; display: block !important;">

                <!-- محتوى العرض -->
                <div class="offer-content">
                    <h3 class="offer-title">العرض ينتهي خلال:</h3>
                    
                    <!-- Countdown Timer -->
                    <div id="countdown-timer-test-banner-123" class="countdown-timer"
                         style="color: #ffffff;">
                        <div class="countdown-box" style="background: linear-gradient(135deg, #00bfe0, #00bfe0cc); color: #ffffff; border: 1px solid #00bfe0;">
                            <span id="days-test-banner-123" class="countdown-value">07</span>
                            <span class="countdown-label">أيام</span>
                        </div>
                        <div class="countdown-box" style="background: linear-gradient(135deg, #00bfe0, #00bfe0cc); color: #ffffff; border: 1px solid #00bfe0;">
                            <span id="hours-test-banner-123" class="countdown-value">12</span>
                            <span class="countdown-label">ساعات</span>
                        </div>
                        <div class="countdown-box" style="background: linear-gradient(135deg, #00bfe0, #00bfe0cc); color: #ffffff; border: 1px solid #00bfe0;">
                            <span id="minutes-test-banner-123" class="countdown-value">34</span>
                            <span class="countdown-label">دقائق</span>
                        </div>
                        <div class="countdown-box" style="background: linear-gradient(135deg, #00bfe0, #00bfe0cc); color: #ffffff; border: 1px solid #00bfe0;">
                            <span id="seconds-test-banner-123" class="countdown-value">56</span>
                            <span class="countdown-label">ثواني</span>
                        </div>
                    </div>
                    
                    <!-- Offer ended badge -->
                    <div id="offer-ended-badge-test-banner-123" class="offer-ended-badge">
                        <div class="badge-pattern"></div>
                        <span class="badge-text">نهاية<br>العرض</span>
                    </div>

                    <!-- زر العرض -->
                    <a href="#" class="offer-cta-button">
                        <span class="button-text">تسوق الآن</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript Configuration -->
    <script>
        // Initialize global configuration object
        window.bannerOfferConfig = window.bannerOfferConfig || {};
        
        // Add test configuration
        window.bannerOfferConfig['test-banner-123'] = {
            id: 'test-banner-123',
            bg_color: '#00bfe0',
            days: 7,
            timer_enabled: true,
            timer_text_color: '#ffffff',
            offer_button_enabled: true,
            button_text: 'تسوق الآن',
            offer_bg_color: '#f5f5f5',
            offer_animation: 'fadeIn',
            offer_url: '#',
            offer_img: 'https://cdn.salla.sa/mQgZlG/pzOCEw10wCfXeVSpKnL0Ifd1O9ZkTfnZ17RkE14R.jpg'
        };
        
        console.log('Test configuration loaded:', window.bannerOfferConfig);
    </script>
    
    <script src="assets/js/components/banner-with-offer.js"></script>
    
    <script>
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing banner...');
            if (window.BannerWithOffer && typeof window.BannerWithOffer.init === 'function') {
                window.BannerWithOffer.init();
            } else {
                console.error('BannerWithOffer not found!');
            }
        });
    </script>

    <div style="margin-top: 40px; padding: 20px; background: white; border-radius: 8px;">
        <h3>معلومات الاختبار:</h3>
        <ul>
            <li><strong>لون خلفية التايمر:</strong> #00bfe0 (أزرق فاتح)</li>
            <li><strong>لون نص التايمر:</strong> #ffffff (أبيض)</li>
            <li><strong>لون خلفية العرض:</strong> #f5f5f5 (رمادي فاتح)</li>
            <li><strong>عدد أيام العرض:</strong> 7 أيام</li>
            <li><strong>تأثير الحركة:</strong> fadeIn</li>
            <li><strong>حالة التايمر:</strong> مفعل</li>
            <li><strong>حالة زر العرض:</strong> مفعل</li>
        </ul>
    </div>

</body>
</html>
