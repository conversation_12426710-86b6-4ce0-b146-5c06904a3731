/**
 * Google Maps Component Styling Handler
 * Handles color and background styling for the Google Maps component
 */

class GoogleMapsComponentStyling {
    constructor() {
        this.init();
    }

    init() {
        // Apply styling when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.applyComponentStyling());
        } else {
            this.applyComponentStyling();
        }
    }

    applyComponentStyling() {
        const components = document.querySelectorAll('.google-maps-component');
        
        components.forEach(component => {
            this.styleComponent(component);
        });
    }

    styleComponent(component) {
        const settings = this.getComponentSettings(component);
        
        // Apply background styling
        this.applyBackgroundStyling(component, settings);
        
        // Apply text colors
        this.applyTextColors(component, settings);
        
        // Apply border styling
        this.applyBorderStyling(component, settings);
        
        // Apply padding
        this.applyPaddingStyling(component, settings);
    }

    getComponentSettings(component) {
        // Get settings from data attributes or global theme settings
        return {
            backgroundEnabled: component.dataset.backgroundEnabled === 'true',
            backgroundColor: component.dataset.backgroundColor || '#f8f9fa',
            titleColor: component.dataset.titleColor || '#212529',
            descriptionColor: component.dataset.descriptionColor || '#6c757d',
            branchNameColor: component.dataset.branchNameColor || '#495057',
            branchAddressColor: component.dataset.branchAddressColor || '#6c757d',
            borderEnabled: component.dataset.borderEnabled === 'true',
            borderColor: component.dataset.borderColor || '#dee2e6',
            borderRadius: parseInt(component.dataset.borderRadius) || 8,
            paddingEnabled: component.dataset.paddingEnabled !== 'false',
            paddingSize: component.dataset.paddingSize || 'medium'
        };
    }

    applyBackgroundStyling(component, settings) {
        if (settings.backgroundEnabled) {
            component.style.backgroundColor = settings.backgroundColor;
        } else {
            component.style.backgroundColor = 'transparent';
        }
    }

    applyTextColors(component, settings) {
        // Apply title color
        const titleElements = component.querySelectorAll('.component-title, .maps-title, h2, h3, h4');
        titleElements.forEach(element => {
            element.style.color = settings.titleColor;
        });

        // Apply description color
        const descriptionElements = component.querySelectorAll('.component-description, .maps-description, p');
        descriptionElements.forEach(element => {
            element.style.color = settings.descriptionColor;
        });

        // Apply branch name color
        const branchNameElements = component.querySelectorAll('.branch-name, .store-name');
        branchNameElements.forEach(element => {
            element.style.color = settings.branchNameColor;
        });

        // Apply branch address color
        const branchAddressElements = component.querySelectorAll('.branch-address, .store-address');
        branchAddressElements.forEach(element => {
            element.style.color = settings.branchAddressColor;
        });
    }

    applyBorderStyling(component, settings) {
        if (settings.borderEnabled) {
            component.style.border = `1px solid ${settings.borderColor}`;
        } else {
            component.style.border = 'none';
        }
        
        component.style.borderRadius = `${settings.borderRadius}px`;
    }

    applyPaddingStyling(component, settings) {
        if (!settings.paddingEnabled) {
            component.style.padding = '0';
            return;
        }

        const paddingValues = {
            small: '15px',
            medium: '30px',
            large: '45px',
            xlarge: '60px'
        };

        component.style.padding = paddingValues[settings.paddingSize] || paddingValues.medium;
    }

    // Method to update component styling dynamically
    updateComponentStyling(componentId, newSettings) {
        const component = document.getElementById(componentId);
        if (component) {
            // Update data attributes
            Object.keys(newSettings).forEach(key => {
                component.dataset[key] = newSettings[key];
            });
            
            // Reapply styling
            this.styleComponent(component);
        }
    }

    // Method to reset component to default styling
    resetComponentStyling(component) {
        component.style.backgroundColor = '';
        component.style.border = '';
        component.style.borderRadius = '';
        component.style.padding = '';
        
        // Reset text colors
        const textElements = component.querySelectorAll('*');
        textElements.forEach(element => {
            element.style.color = '';
        });
    }
}

// Initialize the styling handler
document.addEventListener('DOMContentLoaded', function() {
    new GoogleMapsComponentStyling();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GoogleMapsComponentStyling;
}
