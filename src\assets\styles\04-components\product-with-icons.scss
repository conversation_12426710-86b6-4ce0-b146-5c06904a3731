/* Product with Icons Component Styles */
.product-with-icons-section {
  padding: 3rem 0;
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 20px;
}

.product-with-icons-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%);
}

.product-with-icons-section .section-title {
  color: #ffffff;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

.product-with-icons-section .section-description {
  color: #b8c6db;
  font-size: 1.125rem;
  line-height: 1.625;
  max-width: 42rem;
  margin: 0 auto;
  text-align: center;
}

/* Main Grid Layout */
.product-with-icons-grid {
  display: grid;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 10;
  grid-template-columns: 1fr 2fr 1fr;
}

/* Icons Sections */
.icons-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.icons-section .icons-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Icon Items */
.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.icon-item::before {
  content: '';
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  left: -100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.icon-item:hover {
  transform: translateY(-0.5rem);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.icon-item:hover::before {
  left: 100%;
}

.icon-item:hover .icon-wrapper {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.icon-item:hover .icon-wrapper::before {
  opacity: 1;
}

.icon-item:hover .icon-image {
  transform: scale(1.2) rotate(5deg);
}

.icon-link {
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* Icon Wrapper */
.icon-wrapper {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.icon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  transition: opacity 0.3s ease;
}

.icon-image {
  width: 3rem;
  height: 3rem;
  object-fit: contain;
  position: relative;
  z-index: 10;
  filter: brightness(0) invert(1);
  transition: all 0.3s ease;
}

/* Icon Title */
.icon-title {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.375;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Product Section */
.product-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-section .product-wrapper {
  width: 100%;
  max-width: 24rem;
  position: relative;
}

.product-section .featured-product-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1.25rem;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.product-section .featured-product-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Gradient Animation */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .product-with-icons-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .icons-section {
    order: 2;
  }

  .icons-section .icons-container {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1.5rem;
  }

  .product-section {
    order: 1;
  }

  .icon-item {
    flex: 0 0 calc(50% - 0.75rem);
    width: 12rem;
    min-width: 0;
  }
}

@media (max-width: 768px) {
  .product-with-icons-section {
    padding: 2rem 1rem;
    margin: 1rem 0;
  }

  .product-with-icons-section .section-title {
    font-size: 1.875rem;
  }

  .product-with-icons-section .section-description {
    font-size: 1rem;
  }

  .icon-item {
    flex: 0 0 100%;
    width: 100%;
    padding: 1rem;
  }

  .icon-wrapper {
    width: 4rem;
    height: 4rem;
  }

  .icon-image {
    width: 2.25rem;
    height: 2.25rem;
  }

  .icon-title {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .product-with-icons-grid {
    gap: 1.5rem;
  }

  .product-with-icons-section .section-title {
    font-size: 1.5rem;
  }

  .icons-section .icons-container {
    gap: 1rem;
  }

  .icon-item {
    padding: 0.75rem;
  }
}
