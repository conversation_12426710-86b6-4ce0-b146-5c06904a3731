/**
 * Enhanced Moving Text Component with Speed Control
 * Gaming-themed banner with customizable animation speed
 */

/* Gaming color variables */
:root {
    --gaming-accent-blue: rgba(0, 212, 255, 0.9);
    --gaming-glow-blue: rgba(0, 212, 255, 0.5);
    --gaming-text-primary: #ffffff;
    --gaming-bg-dark: #1a1a1a;
    --gaming-bg-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* Main container with enhanced gaming aesthetics */
.gaming-moving-text-section {
    position: relative;
    background: var(--section-color, var(--gaming-bg-gradient));
    border-top: 2px solid var(--gaming-accent-blue);
    border-bottom: 2px solid var(--gaming-accent-blue);
    overflow: hidden;
    padding: 10px 0;

    /* Each section can have its own color */
    &[data-color] {
        background: var(--section-color);
        background: linear-gradient(135deg,
            var(--section-color) 0%,
            color-mix(in srgb, var(--section-color) 80%, #1a1a2e) 50%,
            var(--section-color) 100%);
    }
    
    /* Gaming circuit pattern overlay */
    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: 
            radial-gradient(circle at 25% 25%, var(--gaming-glow-blue) 1px, transparent 1px),
            radial-gradient(circle at 75% 75%, var(--gaming-glow-blue) 1px, transparent 1px);
        background-size: 40px 40px;
        opacity: 0.3;
        animation: circuitMove 8s linear infinite;
        pointer-events: none;
    }
    
    /* Enhanced glow effect */
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            var(--gaming-glow-blue) 50%,
            transparent 100%);
        animation: scanLine 3s ease-in-out infinite;
        pointer-events: none;
    }
}

/* Container for the moving text */
.gaming-moving-text-container {
    position: relative;
    height: 40px;
    display: flex;
    align-items: center;
    overflow: hidden;
    z-index: 2;
}

/* Enhanced moving text with speed control */
.gaming-moving-text {
    position: absolute;
    white-space: nowrap;
    font-family: var(--font-main, 'DINNextLTArabic'), Arial, sans-serif;
    font-size: 18px;
    font-weight: 700;
    color: var(--gaming-text-primary);
    text-shadow:
        0 0 15px var(--gaming-accent-blue),
        0 0 25px var(--gaming-glow-blue),
        2px 2px 6px rgba(0, 0, 0, 0.9),
        0 0 35px var(--gaming-glow-blue);
    
    /* Use CSS custom property for animation duration */
    animation: moveTextDesktop var(--animation-speed, 25s) linear infinite;
    will-change: transform;
    letter-spacing: 2px;
    text-transform: uppercase;
    user-select: none;
    display: flex;
    align-items: center;
    
    /* Text repetition using pseudo-elements */
    &::after {
        content: attr(data-text);
        margin-left: 100px;
        margin-right: 100px;
    }

    &::before {
        content: attr(data-text);
        margin-left: 100px;
        margin-right: 100px;
    }
}

/* Enhanced keyframe animations */
@keyframes moveTextDesktop {
    0% {
        transform: translate3d(100%, 0, 0);
    }
    100% {
        transform: translate3d(-100%, 0, 0);
    }
}

@keyframes moveTextMobile {
    0% {
        transform: translate3d(100%, 0, 0);
    }
    100% {
        transform: translate3d(-100%, 0, 0);
    }
}

@keyframes scanLine {
    0%, 100% {
        left: -100%;
    }
    50% {
        left: 100%;
    }
}

@keyframes circuitMove {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* RTL support */
[dir="rtl"] .gaming-moving-text {
    animation-name: moveTextRTL;
}

@keyframes moveTextRTL {
    0% {
        transform: translate3d(-100%, 0, 0);
    }
    100% {
        transform: translate3d(100%, 0, 0);
    }
}

/* Hover effects for enhanced interactivity */
.gaming-moving-text-section:hover {
    .gaming-moving-text {
        animation-play-state: paused;
        text-shadow: 
            0 0 20px var(--gaming-accent-blue),
            0 0 30px var(--gaming-glow-blue),
            2px 2px 4px rgba(0, 0, 0, 0.8),
            0 0 40px var(--gaming-glow-blue);
        transform: scale(1.02);
        transition: all 0.3s ease;
    }
    
    &::before {
        animation-play-state: paused;
    }
}

/* Enhanced responsive design */
@media (min-width: 1024px) {
    .gaming-moving-text {
        font-size: 20px;
        animation-duration: var(--animation-speed, 30s);
        letter-spacing: 3px;
    }

    .gaming-moving-text-container {
        height: 50px;
    }

    .gaming-moving-text-section {
        padding: 15px 0;
    }
}

@media (max-width: 768px) {
    .gaming-moving-text {
        font-size: 16px;
        animation: moveTextMobile var(--animation-speed, 20s) linear infinite;
        letter-spacing: 1px;

        &::before,
        &::after {
            margin-left: 50px;
            margin-right: 50px;
        }
    }

    .gaming-moving-text-container {
        height: 35px;
    }

    .gaming-moving-text-section {
        padding: 10px 0;

        &::after {
            background-size: 30px 30px;
            opacity: 0.25;
        }
    }
}

@media (max-width: 480px) {
    .gaming-moving-text {
        font-size: 14px;
        animation: moveTextMobile var(--animation-speed, 15s) linear infinite;
        letter-spacing: 0.5px;

        &::before,
        &::after {
            margin-left: 30px;
            margin-right: 30px;
        }
    }

    .gaming-moving-text-container {
        height: 30px;
    }

    .gaming-moving-text-section {
        padding: 8px 0;

        &::after {
            background-size: 25px 25px;
            opacity: 0.2;
        }
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .gaming-moving-text {
        animation: none !important;
        position: static;
        transform: none;
        text-align: center;
    }
    
    .gaming-moving-text-section {
        &::before,
        &::after {
            animation: none;
        }
    }
    
    .gaming-moving-text-container {
        justify-content: center;
    }
}

/* Speed control indicators (optional visual feedback) */
.gaming-moving-text[data-speed] {
    &::after {
        opacity: 0.8;
    }
}

/* Custom speed classes for different speeds */
.speed-slow .gaming-moving-text {
    --animation-speed: 45s;
}

.speed-normal .gaming-moving-text {
    --animation-speed: 25s;
}

.speed-fast .gaming-moving-text {
    --animation-speed: 15s;
}

.speed-very-fast .gaming-moving-text {
    --animation-speed: 10s;
}
