/**
 * Store Map Component
 * Handles Google Maps embed processing and HTML entity decoding
 */
(function() {
    'use strict';

    function initStoreMap() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', processStoreMaps);
        } else {
            processStoreMaps();
        }
    }

    function processStoreMaps() {
        const storeMapElements = document.querySelectorAll('.s-block--store-map');

        storeMapElements.forEach(element => {
            const layout = element.getAttribute('data-layout') || 'grid';

            // Apply custom styling
            applyCustomStyling(element);

            // Process maps
            const mapContainers = element.querySelectorAll('.map-container[data-embed-code]');
            mapContainers.forEach(container => {
                const embedCode = container.dataset.embedCode;
                if (embedCode && embedCode.trim()) {
                    renderMap(container, embedCode);
                }
            });

            // Initialize tabs if layout is tabs
            if (layout === 'tabs') {
                initializeTabs(element);
            }
        });
    }

    function applyCustomStyling(element) {
        // Get styling settings from data attributes
        const settings = {
            backgroundEnabled: element.dataset.backgroundEnabled === 'true',
            backgroundColor: element.dataset.backgroundColor || '#f8f9fa',
            titleColor: element.dataset.titleColor || '#212529',
            descriptionColor: element.dataset.descriptionColor || '#6c757d',
            branchNameColor: element.dataset.branchNameColor || '#495057',
            branchAddressColor: element.dataset.branchAddressColor || '#6c757d',
            borderEnabled: element.dataset.borderEnabled === 'true',
            borderColor: element.dataset.borderColor || '#dee2e6',
            borderRadius: parseInt(element.dataset.borderRadius) || 8,
            paddingEnabled: element.dataset.paddingEnabled !== 'false',
            paddingSize: element.dataset.paddingSize || 'medium'
        };

        // Apply CSS custom properties
        element.style.setProperty('--title-color', settings.titleColor);
        element.style.setProperty('--description-color', settings.descriptionColor);
        element.style.setProperty('--branch-name-color', settings.branchNameColor);
        element.style.setProperty('--branch-address-color', settings.branchAddressColor);
        element.style.setProperty('--component-bg-color', settings.backgroundColor);
        element.style.setProperty('--component-border-color', settings.borderColor);

        // Apply background styling
        if (settings.backgroundEnabled) {
            element.style.backgroundColor = settings.backgroundColor;
        } else {
            element.style.backgroundColor = 'transparent';
        }

        // Apply border styling
        if (settings.borderEnabled) {
            element.style.border = `1px solid ${settings.borderColor}`;
        } else {
            element.style.border = 'none';
        }

        // Apply border radius
        element.style.borderRadius = `${settings.borderRadius}px`;
    }

    function renderMap(container, embedCode) {
        try {
            // Decode HTML entities
            const decodedCode = decodeHtmlEntities(embedCode);
            
            // Clear the placeholder
            container.innerHTML = '';
            
            // Check if it's a full iframe or just URL
            if (decodedCode.includes('<iframe')) {
                // It's a full iframe code
                container.innerHTML = decodedCode;
            } else if (decodedCode.startsWith('http')) {
                // It's just a URL, create iframe
                const iframe = createIframe(decodedCode);
                container.appendChild(iframe);
            } else {
                // Invalid code, show error
                showError(container, 'كود الخريطة غير صحيح');
                return;
            }

            // Ensure iframe styling
            const iframe = container.querySelector('iframe');
            if (iframe) {
                iframe.style.width = '100%';
                iframe.style.height = '100%';
                iframe.style.border = '0';
            }

        } catch (error) {
            console.error('Error rendering map:', error);
            showError(container, 'خطأ في تحميل الخريطة');
        }
    }

    function decodeHtmlEntities(str) {
        const textarea = document.createElement('textarea');
        textarea.innerHTML = str;
        return textarea.value;
    }

    function createIframe(url) {
        const iframe = document.createElement('iframe');
        iframe.src = url;
        iframe.width = '100%';
        iframe.height = '100%';
        iframe.style.border = '0';
        iframe.setAttribute('allowfullscreen', '');
        iframe.setAttribute('loading', 'lazy');
        iframe.setAttribute('referrerpolicy', 'no-referrer-when-downgrade');
        return iframe;
    }

    function showError(container, message) {
        container.innerHTML = `
            <div style="height: 100%; display: flex; align-items: center; justify-content: center; background: #f5f5f5; color: #666;">
                <div style="text-align: center;">
                    <i class="sicon-map" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
                    <p>${message}</p>
                </div>
            </div>
        `;
    }

    /**
     * Initialize tabs functionality
     */
    function initializeTabs(element) {
        const tabButtons = element.querySelectorAll('.tab-button');
        const tabPanels = element.querySelectorAll('.tab-panel');

        if (tabButtons.length === 0 || tabPanels.length === 0) {
            return;
        }

        // Add click event listeners to tab buttons
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');

                // Remove active class from all buttons and panels
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanels.forEach(panel => panel.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                // Show corresponding panel
                const targetPanel = element.querySelector(`#${targetTab}`);
                if (targetPanel) {
                    targetPanel.classList.add('active');

                    // Re-render maps in the active panel if needed
                    const mapContainers = targetPanel.querySelectorAll('.map-container[data-embed-code]');
                    mapContainers.forEach(container => {
                        if (container.innerHTML.includes('map-placeholder')) {
                            const embedCode = container.dataset.embedCode;
                            if (embedCode && embedCode.trim()) {
                                renderMap(container, embedCode);
                            }
                        }
                    });
                }
            });
        });

        // Add keyboard navigation
        tabButtons.forEach((button, index) => {
            button.addEventListener('keydown', function(e) {
                let targetIndex = index;

                if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    targetIndex = (index + 1) % tabButtons.length;
                    e.preventDefault();
                } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    targetIndex = (index - 1 + tabButtons.length) % tabButtons.length;
                    e.preventDefault();
                } else if (e.key === 'Home') {
                    targetIndex = 0;
                    e.preventDefault();
                } else if (e.key === 'End') {
                    targetIndex = tabButtons.length - 1;
                    e.preventDefault();
                }

                if (targetIndex !== index) {
                    tabButtons[targetIndex].focus();
                    tabButtons[targetIndex].click();
                }
            });
        });
    }

    // Initialize when DOM is ready
    initStoreMap();

})();
