/**
 * Gaming Theme Moving Text Component
 * Enhanced functionality and animations for the moving text banner
 */

(function() {
    'use strict';

    class GamingMovingText {
        constructor() {
            this.sections = [];
            this.isVisible = false;
            this.animationFrameId = null;
            this.intersectionObserver = null;
            this.resizeTimeout = null;
            
            this.init();
        }

        init() {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.setup());
            } else {
                this.setup();
            }
        }

        setup() {
            this.findMovingTextSections();
            this.setupIntersectionObserver();
            this.setupEventListeners();
            this.enhanceAccessibility();
        }

        findMovingTextSections() {
            const sections = document.querySelectorAll('.gaming-moving-text-section');
            
            sections.forEach((section, index) => {
                const textElement = section.querySelector('.gaming-moving-text');
                if (textElement) {
                    const sectionData = {
                        section: section,
                        textElement: textElement,
                        originalText: textElement.textContent.trim(),
                        isAnimating: false,
                        animationSpeed: this.calculateAnimationSpeed(textElement.textContent.length),
                        id: `moving-text-${index}`
                    };
                    
                    this.sections.push(sectionData);
                    this.setupSection(sectionData);
                }
            });
        }

        setupSection(sectionData) {
            const { section, textElement, animationSpeed } = sectionData;

            // Set unique ID
            if (!section.id) {
                section.id = sectionData.id;
            }

            // Get custom animation speed from CSS variable or use calculated speed
            const customSpeed = section.style.getPropertyValue('--animation-speed');
            const finalSpeed = customSpeed ? parseFloat(customSpeed) : animationSpeed;

            // Set dynamic animation speed
            textElement.style.animationDuration = `${finalSpeed}s`;
            section.style.setProperty('--animation-speed', `${finalSpeed}s`);

            // Add data attributes for CSS customization
            textElement.setAttribute('data-length', sectionData.originalText.length);
            textElement.setAttribute('data-speed', finalSpeed);

            // Setup hover effects
            this.setupHoverEffects(sectionData);
        }

        setupHoverEffects(sectionData) {
            const { section, textElement } = sectionData;
            
            section.addEventListener('mouseenter', () => {
                this.pauseAnimation(sectionData);
            });
            
            section.addEventListener('mouseleave', () => {
                this.resumeAnimation(sectionData);
            });
            
            // Touch support for mobile
            section.addEventListener('touchstart', () => {
                this.pauseAnimation(sectionData);
            });
            
            section.addEventListener('touchend', () => {
                setTimeout(() => this.resumeAnimation(sectionData), 1000);
            });
        }

        pauseAnimation(sectionData) {
            const { textElement } = sectionData;
            textElement.style.animationPlayState = 'paused';
            sectionData.isAnimating = false;
        }

        resumeAnimation(sectionData) {
            const { textElement } = sectionData;
            textElement.style.animationPlayState = 'running';
            sectionData.isAnimating = true;
        }

        calculateAnimationSpeed(textLength) {
            // Enhanced speed calculation for desktop vs mobile
            const isDesktop = window.innerWidth >= 1024;
            const baseSpeed = isDesktop ? 25 : 15;

            // Adjust based on text length for consistent reading speed
            const lengthFactor = Math.max(0.7, Math.min(2.5, textLength / 40));
            const calculatedSpeed = Math.round(baseSpeed * lengthFactor);

            // Ensure minimum and maximum speeds
            return isDesktop ?
                Math.max(20, Math.min(40, calculatedSpeed)) :
                Math.max(10, Math.min(25, calculatedSpeed));
        }

        setupIntersectionObserver() {
            if (!window.IntersectionObserver) {
                // Fallback for older browsers
                this.sections.forEach(sectionData => {
                    this.resumeAnimation(sectionData);
                });
                return;
            }

            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    const sectionData = this.sections.find(s => s.section === entry.target);
                    if (sectionData) {
                        if (entry.isIntersecting) {
                            this.resumeAnimation(sectionData);
                        } else {
                            this.pauseAnimation(sectionData);
                        }
                    }
                });
            }, {
                root: null,
                threshold: 0.1,
                rootMargin: '50px'
            });

            this.sections.forEach(sectionData => {
                this.intersectionObserver.observe(sectionData.section);
            });
        }

        setupEventListeners() {
            // Handle window resize
            window.addEventListener('resize', () => {
                if (this.resizeTimeout) {
                    clearTimeout(this.resizeTimeout);
                }
                
                this.resizeTimeout = setTimeout(() => {
                    this.handleResize();
                }, 250);
            });

            // Handle visibility change (tab switching)
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.sections.forEach(sectionData => this.pauseAnimation(sectionData));
                } else {
                    this.sections.forEach(sectionData => this.resumeAnimation(sectionData));
                }
            });

            // Handle reduced motion preference
            if (window.matchMedia) {
                const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
                mediaQuery.addListener(() => this.handleReducedMotion(mediaQuery.matches));
                this.handleReducedMotion(mediaQuery.matches);
            }
        }

        handleResize() {
            this.sections.forEach(sectionData => {
                const { section, textElement } = sectionData;
                const isDesktop = window.innerWidth >= 1024;

                // Get custom speed from CSS variable or calculate default
                const customSpeed = section.style.getPropertyValue('--animation-speed');
                const finalSpeed = customSpeed ?
                    parseFloat(customSpeed) :
                    this.calculateAnimationSpeed(sectionData.originalText.length);

                // Update animation duration
                textElement.style.animationDuration = `${finalSpeed}s`;
                section.style.setProperty('--animation-speed', `${finalSpeed}s`);
                sectionData.animationSpeed = finalSpeed;

                // Update animation name based on screen size
                const animationName = isDesktop ? 'moveTextDesktop' : 'moveTextMobile';
                textElement.style.animationName = animationName;

                // Update spacing for pseudo-elements on mobile
                if (!isDesktop) {
                    const spacing = window.innerWidth <= 480 ? '30px' :
                                  window.innerWidth <= 768 ? '50px' : '80px';
                    textElement.style.setProperty('--spacing', spacing);
                }
            });
        }

        handleReducedMotion(isReduced) {
            this.sections.forEach(sectionData => {
                if (isReduced) {
                    this.pauseAnimation(sectionData);
                    sectionData.textElement.style.transform = 'none';
                    sectionData.textElement.style.position = 'static';
                    sectionData.section.querySelector('.gaming-moving-text-container').style.justifyContent = 'center';
                } else {
                    this.resumeAnimation(sectionData);
                    sectionData.textElement.style.transform = '';
                    sectionData.textElement.style.position = 'absolute';
                    sectionData.section.querySelector('.gaming-moving-text-container').style.justifyContent = '';
                }
            });
        }

        enhanceAccessibility() {
            this.sections.forEach(sectionData => {
                const { section, textElement } = sectionData;
                
                // Add ARIA labels
                section.setAttribute('role', 'banner');
                section.setAttribute('aria-label', 'إعلان متحرك: ' + sectionData.originalText);
                
                // Add screen reader support
                textElement.setAttribute('aria-live', 'polite');
                textElement.setAttribute('aria-atomic', 'true');
                
                // Add keyboard navigation support
                section.setAttribute('tabindex', '0');
                section.addEventListener('focus', () => this.pauseAnimation(sectionData));
                section.addEventListener('blur', () => this.resumeAnimation(sectionData));
                
                // Add keyboard controls
                section.addEventListener('keydown', (e) => {
                    if (e.key === 'Space' || e.key === 'Enter') {
                        e.preventDefault();
                        if (sectionData.isAnimating) {
                            this.pauseAnimation(sectionData);
                        } else {
                            this.resumeAnimation(sectionData);
                        }
                    }
                });
            });
        }

        // Public API methods
        pauseAll() {
            this.sections.forEach(sectionData => this.pauseAnimation(sectionData));
        }

        resumeAll() {
            this.sections.forEach(sectionData => this.resumeAnimation(sectionData));
        }

        destroy() {
            if (this.intersectionObserver) {
                this.intersectionObserver.disconnect();
            }
            
            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
            }
            
            if (this.resizeTimeout) {
                clearTimeout(this.resizeTimeout);
            }
            
            // Remove event listeners
            this.sections.forEach(sectionData => {
                const { section } = sectionData;
                section.removeEventListener('mouseenter', this.pauseAnimation);
                section.removeEventListener('mouseleave', this.resumeAnimation);
                section.removeEventListener('touchstart', this.pauseAnimation);
                section.removeEventListener('touchend', this.resumeAnimation);
                section.removeEventListener('focus', this.pauseAnimation);
                section.removeEventListener('blur', this.resumeAnimation);
                section.removeEventListener('keydown', this.handleKeydown);
            });
            
            this.sections = [];
        }
    }

    // Initialize when DOM is ready
    let gamingMovingTextInstance = null;
    
    function initGamingMovingText() {
        if (gamingMovingTextInstance) {
            gamingMovingTextInstance.destroy();
        }
        gamingMovingTextInstance = new GamingMovingText();
    }

    // Auto-initialize
    initGamingMovingText();

    // Expose to global scope for manual control
    window.GamingMovingText = {
        instance: () => gamingMovingTextInstance,
        reinit: initGamingMovingText,
        pauseAll: () => gamingMovingTextInstance?.pauseAll(),
        resumeAll: () => gamingMovingTextInstance?.resumeAll()
    };

})();
