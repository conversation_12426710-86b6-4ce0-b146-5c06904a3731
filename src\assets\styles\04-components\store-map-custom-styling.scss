/**
 * Store Map Custom Styling
 * Advanced styling options for store map component customization
 */

/* Custom styling classes for store map component */
.s-block--store-map {
  
  /* Background styling variations */
  &.background-enabled {
    background-color: var(--component-bg-color, #f8f9fa) !important;
    
    &::before {
      opacity: 0.1; // Reduce gaming theme overlay when custom background is enabled
    }
  }
  
  &.background-disabled {
    background-color: transparent !important;
    
    &::before {
      opacity: 1; // Keep gaming theme overlay when no custom background
    }
  }
  
  /* Border styling variations */
  &.border-enabled {
    border: 1px solid var(--component-border-color, #dee2e6);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  }
  
  &.border-disabled {
    border: none;
    box-shadow: none;
  }
  
  /* Padding variations */
  &.padding-small {
    padding: 2rem 0;
    
    .container {
      padding: 1rem;
    }
  }
  
  &.padding-medium {
    padding: 4rem 0;
    
    .container {
      padding: 2rem;
    }
  }
  
  &.padding-large {
    padding: 6rem 0;
    
    .container {
      padding: 3rem;
    }
  }
  
  &.padding-xlarge {
    padding: 8rem 0;
    
    .container {
      padding: 4rem;
    }
  }
  
  &.padding-disabled {
    padding: 0;
    
    .container {
      padding: 0;
    }
  }
  
  /* Custom color overrides */
  .store-map-title {
    color: var(--title-color, var(--component-title-color, var(--store-map-text))) !important;
    transition: color 0.3s ease;
  }
  
  .store-map-description {
    color: var(--description-color, var(--component-description-color, var(--store-map-text-muted))) !important;
    transition: color 0.3s ease;
  }
  
  .branch-name {
    color: var(--branch-name-color, var(--component-branch-name-color, var(--store-map-primary))) !important;
    transition: color 0.3s ease;
  }
  
  .branch-address {
    color: var(--branch-address-color, var(--component-branch-address-color, var(--store-map-text-muted))) !important;
    transition: color 0.3s ease;
  }
  
  /* Tab buttons color customization */
  .tabs-navigation .tab-button {
    color: var(--branch-name-color, var(--component-branch-name-color, var(--store-map-text))) !important;
    transition: all 0.3s ease;
    
    &:hover {
      color: var(--title-color, var(--component-title-color, var(--store-map-primary))) !important;
    }
    
    &.active {
      color: var(--title-color, var(--component-title-color, var(--store-map-primary))) !important;
      background-color: var(--component-bg-color, rgba(29, 233, 182, 0.1));
    }
  }
}

/* Responsive adjustments for custom styling */
@media (max-width: 768px) {
  .s-block--store-map {
    &.padding-small {
      padding: 1.5rem 0;
      
      .container {
        padding: 0.5rem;
      }
    }
    
    &.padding-medium {
      padding: 2rem 0;
      
      .container {
        padding: 1rem;
      }
    }
    
    &.padding-large {
      padding: 3rem 0;
      
      .container {
        padding: 1.5rem;
      }
    }
    
    &.padding-xlarge {
      padding: 4rem 0;
      
      .container {
        padding: 2rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .s-block--store-map {
    &.padding-small {
      padding: 1rem 0;
      
      .container {
        padding: 0.25rem;
      }
    }
    
    &.padding-medium {
      padding: 1.5rem 0;
      
      .container {
        padding: 0.5rem;
      }
    }
    
    &.padding-large {
      padding: 2rem 0;
      
      .container {
        padding: 1rem;
      }
    }
    
    &.padding-xlarge {
      padding: 2.5rem 0;
      
      .container {
        padding: 1.5rem;
      }
    }
  }
}

/* Animation enhancements for custom styled components */
.s-block--store-map.background-enabled {
  .branch-item {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
    }
  }
}

/* Print styles for custom styling */
@media print {
  .s-block--store-map {
    background-color: transparent !important;
    border: 1px solid #000000 !important;
    padding: 1rem 0 !important;
    
    &::before {
      display: none !important;
    }
    
    .store-map-title {
      color: #000000 !important;
      text-shadow: none !important;
    }
    
    .store-map-description {
      color: #333333 !important;
    }
    
    .branch-name {
      color: #000000 !important;
      text-shadow: none !important;
    }
    
    .branch-address {
      color: #333333 !important;
    }
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .s-block--store-map {
    &.border-enabled {
      border-width: 2px !important;
      border-color: #000000 !important;
    }
    
    .store-map-title {
      color: #000000 !important;
      text-shadow: none !important;
    }
    
    .store-map-description {
      color: #333333 !important;
    }
    
    .branch-name {
      color: #000000 !important;
      text-shadow: none !important;
    }
    
    .branch-address {
      color: #333333 !important;
    }
  }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .s-block--store-map.background-enabled {
    --component-bg-color: #2d3748;
    --component-border-color: #4a5568;
    --component-title-color: #ffffff;
    --component-description-color: #b0b0b0;
    --component-branch-name-color: #e0e0e0;
    --component-branch-address-color: #b0b0b0;
  }
}
