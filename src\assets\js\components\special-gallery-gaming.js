/**
 * WhatsApp Gaming Gallery Component - Optimized Performance
 * Handles animations, lazy loading, and interactive effects
 */
(function() {
    'use strict';
    
    let isInitialized = false;
    const galleryInstances = new Map();
    
    // Optimized gallery controller
    class WhatsAppGalleryController {
        constructor(section) {
            this.section = section;
            this.sectionId = section.id;
            this.galleryItems = section.querySelectorAll('.whatsapp-gallery-item');
            this.images = section.querySelectorAll('.whatsapp-gallery-image');
            this.buttons = section.querySelectorAll('.whatsapp-gallery-button');
            this.titles = section.querySelectorAll('.whatsapp-gallery-item-title');
            
            this.animationObserver = null;
            this.lazyObserver = null;
            
            this.init();
        }
        
        init() {
            if (!this.section || this.galleryItems.length === 0) return;
            
            this.initLazyLoading();
            this.initAnimations();
            this.initInteractiveEffects();
            this.setupSmoothScrolling();
        }
        
        initLazyLoading() {
            if (!this.images.length) return;
            
            if ('IntersectionObserver' in window) {
                this.lazyObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            this.loadImage(entry.target);
                            this.lazyObserver.unobserve(entry.target);
                        }
                    });
                }, {
                    rootMargin: '100px'
                });
                
                this.images.forEach(img => {
                    if (img.dataset.src || !img.complete) {
                        img.classList.add('lazy');
                        this.lazyObserver.observe(img);
                    }
                });
            } else {
                // Fallback: load all images immediately
                this.images.forEach(img => this.loadImage(img));
            }
        }
        
        loadImage(img) {
            const container = img.closest('.whatsapp-gallery-item');
            if (container) {
                container.classList.add('loading');
            }
            
            // If image has data-src, use it
            if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            }
            
            const onLoad = () => {
                img.classList.remove('lazy');
                img.classList.add('loaded');
                if (container) {
                    container.classList.remove('loading');
                }
                img.removeEventListener('load', onLoad);
                img.removeEventListener('error', onError);
            };
            
            const onError = () => {
                img.classList.remove('lazy');
                if (container) {
                    container.classList.remove('loading');
                }
                img.removeEventListener('load', onLoad);
                img.removeEventListener('error', onError);
            };
            
            if (img.complete && img.naturalHeight !== 0) {
                onLoad();
            } else {
                img.addEventListener('load', onLoad, { once: true });
                img.addEventListener('error', onError, { once: true });
            }
        }
        
        initAnimations() {
            if (!('IntersectionObserver' in window)) {
                // Fallback for older browsers
                this.galleryItems.forEach(item => {
                    item.classList.add('animate-in');
                });
                return;
            }
            
            this.animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Add staggered animation delay
                        const index = Array.from(this.galleryItems).indexOf(entry.target);
                        setTimeout(() => {
                            entry.target.classList.add('animate-in');
                        }, index * 200);
                        
                        this.animationObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });
            
            this.galleryItems.forEach(item => {
                this.animationObserver.observe(item);
            });
        }
        
        initInteractiveEffects() {
            // Button hover effects
            this.buttons.forEach(button => {
                button.addEventListener('mouseenter', this.handleButtonHover.bind(this), { passive: true });
                button.addEventListener('mouseleave', this.handleButtonLeave.bind(this), { passive: true });
            });
            
            // Image hover effects
            this.galleryItems.forEach(item => {
                const img = item.querySelector('.whatsapp-gallery-image');
                const overlay = item.querySelector('.whatsapp-gallery-overlay');
                
                if (img && overlay) {
                    item.addEventListener('mouseenter', () => this.handleImageHover(img, overlay), { passive: true });
                    item.addEventListener('mouseleave', () => this.handleImageLeave(img, overlay), { passive: true });
                }
            });
            
            // Title hover effects
            this.titles.forEach(title => {
                title.addEventListener('mouseenter', this.handleTitleHover.bind(this), { passive: true });
                title.addEventListener('mouseleave', this.handleTitleLeave.bind(this), { passive: true });
            });
        }
        
        handleButtonHover(event) {
            const button = event.target;
            button.style.transform = 'translateY(-3px) scale(1.05)';
            button.style.boxShadow = '0 8px 25px rgba(37, 211, 102, 0.5), 0 0 20px rgba(37, 211, 102, 0.3)';
        }
        
        handleButtonLeave(event) {
            const button = event.target;
            button.style.transform = '';
            button.style.boxShadow = '';
        }
        
        handleImageHover(img, overlay) {
            img.style.transform = 'scale(1.05)';
            overlay.style.background = 'rgba(37, 211, 102, 0.2)';
        }
        
        handleImageLeave(img, overlay) {
            img.style.transform = '';
            overlay.style.background = '';
        }
        
        handleTitleHover(event) {
            const title = event.target;
            title.style.textShadow = '0 0 15px rgba(37, 211, 102, 0.6)';
        }
        
        handleTitleLeave(event) {
            const title = event.target;
            title.style.textShadow = '';
        }
        
        setupSmoothScrolling() {
            // Only set if not already set
            if (!document.documentElement.style.scrollBehavior) {
                document.documentElement.style.scrollBehavior = 'smooth';
            }
        }
        
        destroy() {
            if (this.animationObserver) {
                this.animationObserver.disconnect();
                this.animationObserver = null;
            }
            
            if (this.lazyObserver) {
                this.lazyObserver.disconnect();
                this.lazyObserver = null;
            }
            
            // Remove event listeners
            this.buttons.forEach(button => {
                button.removeEventListener('mouseenter', this.handleButtonHover);
                button.removeEventListener('mouseleave', this.handleButtonLeave);
            });
            
            this.galleryItems.forEach(item => {
                item.removeEventListener('mouseenter', this.handleImageHover);
                item.removeEventListener('mouseleave', this.handleImageLeave);
            });
            
            this.titles.forEach(title => {
                title.removeEventListener('mouseenter', this.handleTitleHover);
                title.removeEventListener('mouseleave', this.handleTitleLeave);
            });
        }
    }
    
    // Initialize gallery sections
    function initWhatsAppGalleries() {
        if (isInitialized) return;
        
        const gallerySections = document.querySelectorAll('.whatsapp-special-gallery-section');
        
        gallerySections.forEach(section => {
            const id = section.id;
            
            if (id && !galleryInstances.has(id)) {
                const controller = new WhatsAppGalleryController(section);
                galleryInstances.set(id, controller);
            }
        });
        
        isInitialized = true;
    }
    
    // Cleanup function
    function cleanup() {
        galleryInstances.forEach(controller => {
            controller.destroy();
        });
        galleryInstances.clear();
        isInitialized = false;
    }
    
    // Performance-optimized initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initWhatsAppGalleries, { once: true });
    } else {
        requestAnimationFrame(initWhatsAppGalleries);
    }
    
    // Expose for manual control
    window.whatsAppGalleryController = {
        init: initWhatsAppGalleries,
        cleanup,
        instances: galleryInstances
    };
    
})();
