.store-footer {
  @apply relative bg-darker text-white mt-8 sm:mt-16;

  a{
    @apply transition-opacity hover:opacity-75;
  }

  &__inner{
    @apply bg-dark py-8 lg:py-16 border-b border-dashed border-b-green-500/10;
  }  

  h3{
    @apply text-lg font-bold mb-3 lg:mb-5;
  }

  .social-link {
    @apply border rounded-full w-8 h-8 justify-center flex items-center hover:opacity-75 transition duration-300 text-sm;

    /* Enhanced mobile alignment */
    @media (max-width: 768px) {
      @apply w-10 h-10;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;

      svg, i {
        position: relative;
        top: 0;
        left: 0;
        transform: translate(0, 0);
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .contact-social{
    @apply mt-5 pt-5 border-t border-white/10;

    ul {
      @apply mb-0;
    }
  }

  .footer-is-light & {
    @apply bg-gray-50 text-gray-700;

    .store-footer__inner {
      background: linear-gradient(135deg,
        #000000 0%,
        #0a0a0f 15%,
        #1a1a2e 35%,
        #16213e 55%,
        #0f3460 75%,
        #533483 100%);
      border-bottom: 2px solid rgba(0, 255, 255, 0.4);
      box-shadow:
        0 -20px 50px rgba(0, 0, 0, 0.9) inset,
        0 0 30px rgba(0, 255, 255, 0.1) inset;
      position: relative;

      /* إضافة نمط شبكة للخلفية */
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
          radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 1px, transparent 1px),
          radial-gradient(circle at 75% 75%, rgba(138, 43, 226, 0.1) 1px, transparent 1px);
        background-size: 50px 50px;
        opacity: 0.3;
        z-index: 0;
      }

      /* تأكد أن المحتوى فوق الخلفية */
      > * {
        position: relative;
        z-index: 1;
      }
    }

    .social-link {
      @apply border-gray-300;
    }

    .contact-social{
      @apply border-gray-100;
    }
  }
  
  /* Gaming Theme Footer Styling */
  .gaming-theme & {
    @apply text-gray-200 mt-0;
    background: linear-gradient(to bottom,
      #000000 0%,
      #0a0a0f 25%,
      #0f0f1a 50%,
      #1a1a2e 75%,
      #16213e 100%);
    position: relative;

    /* Create a top glow effect */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.4) 20%,
        rgba(138, 43, 226, 0.6) 50%,
        rgba(0, 255, 255, 0.4) 80%,
        transparent 100%);
      box-shadow: 0 0 20px rgba(138, 43, 226, 0.7);
    }

    /* Add animated glow to the footer - Performance Optimized */
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 30%;
      height: 2px;
      background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.8) 50%,
        transparent 100%);
      animation: footerGlow 10s infinite ease-in-out;
      box-shadow: 0 0 25px 8px rgba(0, 255, 255, 0.5);
      will-change: left;
      transform: translate3d(0, 0, 0);
      contain: layout style paint;
    }

    @keyframes footerGlow {
      0% { left: -30%; }
      100% { left: 100%; }
    }
    
    a {
      @apply transition-all hover:opacity-100 hover:text-white;
      text-shadow: 0 0 8px rgba(0, 255, 255, 0);
      transition: transform 0.3s ease, color 0.3s ease, text-shadow 0.3s ease;
      will-change: transform;

      &:hover {
        text-shadow: 0 0 12px rgba(0, 255, 255, 0.8);
        transform: translate3d(3px, 0, 0);
        color: #00ffff;
      }
    }

    &__inner {
      background: linear-gradient(135deg,
        #000000 0%,
        #0a0a0f 15%,
        #1a1a2e 35%,
        #16213e 55%,
        #0f3460 75%,
        #533483 100%);
      border-bottom: 2px solid rgba(0, 255, 255, 0.4);
      box-shadow:
        0 -20px 50px rgba(0, 0, 0, 0.9) inset,
        0 0 30px rgba(0, 255, 255, 0.1) inset;
      position: relative;

      /* إضافة نمط شبكة للخلفية */
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
          radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 1px, transparent 1px),
          radial-gradient(circle at 75% 75%, rgba(138, 43, 226, 0.1) 1px, transparent 1px);
        background-size: 50px 50px;
        opacity: 0.3;
        z-index: 0;
      }

      /* تأكد أن المحتوى فوق الخلفية */
      > * {
        position: relative;
        z-index: 1;
      }
    }

    h3 {
      @apply text-white font-bold;
      text-shadow: 0 0 15px rgba(0, 255, 255, 0.7);
      position: relative;
      color: #00ffff;

      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(90deg, rgba(0, 255, 255, 0.9), rgba(138, 43, 226, 0.6), transparent);
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
      }
    }

    .social-link {
      border: 1px solid rgba(0, 255, 255, 0.4);
      color: #b3b3b3;
      transition: all 0.3s ease;
      box-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
      background: rgba(0, 0, 0, 0.6);

      /* Enhanced mobile alignment for gaming theme */
      @media (max-width: 768px) {
        @apply w-12 h-12;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 6px !important;

        svg, i {
          position: relative !important;
          top: 0 !important;
          left: 0 !important;
          transform: translate(0, 0) !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          width: 16px !important;
          height: 16px !important;
        }
      }

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.6);
        color: #00ffff;
        border-color: #00ffff;
        background: rgba(0, 255, 255, 0.1);
      }
    }

    .contact-social {
      border-top: 1px solid rgba(0, 255, 255, 0.3);
    }
    
    /* Style payment methods section */
    salla-payments {
      .s-payments-wrapper {
        filter: grayscale(70%);
        opacity: 0.7;
        transition: all 0.3s ease;
        
        &:hover {
          filter: grayscale(0%);
          opacity: 1;
        }
      }
    }
  }
}


.copyright-text p{
  @apply text-gray-700;
  
  .gaming-theme & {
    @apply text-gray-400;
  }
}

/* Add a pulse glow effect to app icons in footer */
.gaming-theme salla-apps-icons {
  img {
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 0 rgba(111, 76, 255, 0));
    
    &:hover {
      transform: scale(1.1);
      filter: drop-shadow(0 0 5px rgba(111, 76, 255, 0.7));
    }
  }
}