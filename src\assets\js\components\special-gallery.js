/**
 * Gaming Theme Enhanced Special Gallery - Optimized Performance
 * Handles lazy loading and gaming interactions
 */
(function() {
    'use strict';
    
    let gallerySection;
    let lazyImages;
    let isInitialized = false;
    
    // Cache DOM elements
    function initGallery() {
        gallerySection = document.querySelector('.special-gallery');
        if (!gallerySection) return false;
        
        lazyImages = gallerySection.querySelectorAll('.special-gallery-image.lazy');
        return true;
    }
    
    // Gaming-themed loading indicator (optimized)
    function createGamingLoader(container) {
        const loader = document.createElement('div');
        loader.className = 'gaming-loader';
        loader.innerHTML = `
            <div class="gaming-loader-ring"></div>
            <div class="gaming-loader-text">Loading...</div>
        `;
        loader.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            color: rgba(29, 233, 182, 0.8);
            font-size: 12px;
            text-align: center;
            pointer-events: none;
        `;
        container.appendChild(loader);
        return loader;
    }
    
    // Optimized image loading
    function loadImage(lazyImage) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const imageUrl = lazyImage.dataset.bg;
            
            img.onload = () => {
                lazyImage.style.backgroundImage = `url(${imageUrl})`;
                lazyImage.classList.add('loaded');
                lazyImage.classList.remove('lazy');
                
                // Gaming load effect
                lazyImage.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    lazyImage.style.transform = 'scale(1)';
                }, 200);
                
                resolve();
            };
            
            img.onerror = () => {
                lazyImage.classList.remove('lazy');
                lazyImage.style.background = 'linear-gradient(45deg, rgba(20, 20, 35, 0.8), rgba(30, 30, 45, 0.6))';
                reject();
            };
            
            img.src = imageUrl;
        });
    }
    
    // Modern lazy loading with IntersectionObserver
    function initLazyLoading() {
        if (!lazyImages.length) return;
        
        if ('IntersectionObserver' in window) {
            const observerOptions = {
                root: null,
                rootMargin: '200px',
                threshold: 0.01
            };
            
            const lazyImageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const lazyImage = entry.target;
                        const loader = createGamingLoader(lazyImage.parentElement);
                        
                        // Use requestAnimationFrame for better performance
                        requestAnimationFrame(() => {
                            loadImage(lazyImage)
                                .then(() => {
                                    // Remove loader with fade effect
                                    loader.style.opacity = '0';
                                    setTimeout(() => loader.remove(), 300);
                                })
                                .catch(() => {
                                    loader.remove();
                                });
                        });
                        
                        observer.unobserve(lazyImage);
                    }
                });
            }, observerOptions);
            
            lazyImages.forEach(lazyImage => {
                lazyImageObserver.observe(lazyImage);
            });
        } else {
            // Fallback for older browsers
            initFallbackLazyLoading();
        }
    }
    
    // Fallback lazy loading for older browsers
    function initFallbackLazyLoading() {
        const loadLazyImages = () => {
            const remainingImages = gallerySection.querySelectorAll('.special-gallery-image.lazy');
            if (remainingImages.length === 0) {
                window.removeEventListener('scroll', throttledLoad);
                window.removeEventListener('resize', throttledLoad);
                window.removeEventListener('orientationchange', throttledLoad);
                return;
            }
            
            remainingImages.forEach(lazyImage => {
                const rect = lazyImage.getBoundingClientRect();
                if (rect.top <= window.innerHeight && rect.bottom >= 0 && 
                    getComputedStyle(lazyImage).display !== 'none') {
                    loadImage(lazyImage);
                }
            });
        };
        
        // Throttle function for performance
        const throttle = (callback, limit) => {
            let waiting = false;
            return function() {
                if (!waiting) {
                    callback.apply(this, arguments);
                    waiting = true;
                    setTimeout(() => {
                        waiting = false;
                    }, limit);
                }
            };
        };
        
        const throttledLoad = throttle(loadLazyImages, 200);
        
        window.addEventListener('scroll', throttledLoad, { passive: true });
        window.addEventListener('resize', throttledLoad, { passive: true });
        window.addEventListener('orientationchange', throttledLoad, { passive: true });
        
        throttledLoad();
    }
    
    // Gaming-enhanced interactions (optimized)
    function initGamingInteractions() {
        const galleryItems = gallerySection.querySelectorAll('.special-gallery-item');
        if (!galleryItems.length) return;
        
        galleryItems.forEach((item, index) => {
            // Touch interactions for mobile
            item.addEventListener('touchstart', () => {
                item.style.transform = 'scale(0.98) translateY(1px)';
                item.style.boxShadow = '0 0 20px rgba(111, 76, 255, 0.4)';
            }, { passive: true });
            
            item.addEventListener('touchend', () => {
                setTimeout(() => {
                    item.style.transform = '';
                    item.style.boxShadow = '';
                }, 150);
            }, { passive: true });
            
            // Gaming hover effect
            item.addEventListener('mouseenter', () => {
                const ripple = document.createElement('div');
                ripple.style.cssText = `
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 10px;
                    height: 10px;
                    background: radial-gradient(circle, rgba(29, 233, 182, 0.6), transparent);
                    border-radius: 50%;
                    transform: translate(-50%, -50%) scale(0);
                    animation: gaming-ripple 0.6s ease-out;
                    pointer-events: none;
                    z-index: 5;
                `;
                item.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 600);
            }, { passive: true });
            
            // Staggered animation delay
            item.style.animationDelay = (index * 0.1) + 's';
            item.classList.add('gaming-item-loaded');
        });
    }
    
    // Initialize everything
    function init() {
        if (isInitialized || !initGallery()) return;
        
        // Add gaming theme class
        gallerySection.classList.add('gaming-enhanced');
        
        // Initialize features
        initLazyLoading();
        initGamingInteractions();
        
        isInitialized = true;
    }
    
    // Performance-optimized initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        requestAnimationFrame(init);
    }
    
    // Expose for manual initialization if needed
    window.initSpecialGallery = init;
    
})();
