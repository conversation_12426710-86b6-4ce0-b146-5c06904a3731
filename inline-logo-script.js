/**
 * سكريبت مباشر لتطبيق أحجام اللوجو
 * يمكن إضافته في master.twig مباشرة
 */

(function() {
    'use strict';
    
    console.log('🚀 بدء تحميل سكريبت أحجام اللوجو المباشر');
    
    // الحصول على حجم اللوجو من المتغير العام
    function getLogoSize() {
        let logoSize = 'medium'; // افتراضي
        
        // من window variable
        if (typeof window.navbar_logo_size !== 'undefined') {
            logoSize = window.navbar_logo_size;
        }
        // من salla config
        else if (typeof salla !== 'undefined' && salla.config && salla.config.get) {
            logoSize = salla.config.get('navbar_logo_size', 'medium');
        }
        // من body class
        else {
            const bodyClasses = document.body.className;
            if (bodyClasses.includes('navbar-logo-small')) logoSize = 'small';
            else if (bodyClasses.includes('navbar-logo-large')) logoSize = 'large';
            else if (bodyClasses.includes('navbar-logo-extra-large')) logoSize = 'extra-large';
            else logoSize = 'medium';
        }
        
        return logoSize;
    }
    
    // تطبيق الحجم مباشرة على العناصر
    function applyLogoSize() {
        const logoSize = getLogoSize();
        const logos = document.querySelectorAll('.navbar-brand img');
        
        console.log(`📏 تطبيق حجم اللوجو: ${logoSize} على ${logos.length} عنصر`);
        
        if (logos.length === 0) {
            console.log('⚠️ لم يتم العثور على أي لوجوهات');
            return;
        }
        
        // خريطة الأحجام
        const sizeMap = {
            'small': { maxWidth: '80px', maxHeight: '50px' },
            'medium': { maxWidth: '120px', maxHeight: '70px' },
            'large': { maxWidth: '160px', maxHeight: '90px' },
            'extra-large': { maxWidth: '200px', maxHeight: '110px' }
        };
        
        // أحجام الموبايل
        const mobileSizeMap = {
            'small': { maxWidth: '60px', maxHeight: '40px' },
            'medium': { maxWidth: '90px', maxHeight: '55px' },
            'large': { maxWidth: '120px', maxHeight: '70px' },
            'extra-large': { maxWidth: '140px', maxHeight: '85px' }
        };
        
        const isMobile = window.innerWidth <= 768;
        const targetSize = isMobile ? mobileSizeMap[logoSize] : sizeMap[logoSize];
        
        if (!targetSize) {
            console.log('❌ حجم غير صحيح:', logoSize);
            return;
        }
        
        // تطبيق الحجم على كل لوجو
        logos.forEach((logo, index) => {
            try {
                // طريقة 1: style attribute مباشر
                const styleString = [
                    `max-width: ${targetSize.maxWidth} !important`,
                    `max-height: ${targetSize.maxHeight} !important`,
                    `width: auto !important`,
                    `height: auto !important`,
                    `object-fit: contain !important`,
                    `transition: all 0.3s ease !important`
                ].join('; ') + ';';
                
                logo.setAttribute('style', styleString);
                
                // طريقة 2: style properties مباشرة
                logo.style.setProperty('max-width', targetSize.maxWidth, 'important');
                logo.style.setProperty('max-height', targetSize.maxHeight, 'important');
                logo.style.setProperty('width', 'auto', 'important');
                logo.style.setProperty('height', 'auto', 'important');
                logo.style.setProperty('object-fit', 'contain', 'important');
                
                console.log(`✅ تم تطبيق الحجم على اللوجو ${index + 1}: ${targetSize.maxWidth} x ${targetSize.maxHeight}`);
                
                // تأثير بصري للتأكيد
                logo.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    logo.style.transform = 'scale(1)';
                }, 200);
                
            } catch (error) {
                console.error(`❌ خطأ في تطبيق الحجم على اللوجو ${index + 1}:`, error);
            }
        });
        
        console.log(`🎉 تم الانتهاء من تطبيق الأحجام`);
    }
    
    // تطبيق الحجم عند تحميل DOM
    function initLogoSizing() {
        console.log('🔧 تهيئة نظام أحجام اللوجو');
        
        // تطبيق فوري
        applyLogoSize();
        
        // إعادة تطبيق كل ثانية لمدة 10 ثوان (للتأكد)
        let attempts = 0;
        const maxAttempts = 10;
        const interval = setInterval(() => {
            attempts++;
            console.log(`🔄 محاولة إعادة التطبيق ${attempts}/${maxAttempts}`);
            applyLogoSize();
            
            if (attempts >= maxAttempts) {
                clearInterval(interval);
                console.log('⏹️ تم إيقاف المحاولات المتكررة');
            }
        }, 1000);
        
        // مراقبة تغييرات حجم الشاشة
        window.addEventListener('resize', function() {
            console.log('📱 تم تغيير حجم الشاشة');
            setTimeout(applyLogoSize, 100);
        });
        
        // مراقبة إضافة عناصر جديدة
        const observer = new MutationObserver(function(mutations) {
            let shouldReapply = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    const addedNodes = Array.from(mutation.addedNodes);
                    addedNodes.forEach(node => {
                        if (node.nodeType === 1) { // Element node
                            if (node.matches && node.matches('.navbar-brand img')) {
                                shouldReapply = true;
                            } else if (node.querySelectorAll) {
                                const logos = node.querySelectorAll('.navbar-brand img');
                                if (logos.length > 0) {
                                    shouldReapply = true;
                                }
                            }
                        }
                    });
                }
            });
            
            if (shouldReapply) {
                console.log('🆕 تم اكتشاف إضافة لوجوهات جديدة');
                setTimeout(applyLogoSize, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('✅ تم تهيئة نظام أحجام اللوجو بنجاح');
    }
    
    // تشغيل النظام
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initLogoSizing);
    } else {
        initLogoSizing();
    }
    
    // تشغيل عند جاهزية salla
    if (typeof salla !== 'undefined' && salla.onReady) {
        salla.onReady(function() {
            console.log('🔗 Salla جاهز - إعادة تطبيق أحجام اللوجو');
            setTimeout(initLogoSizing, 500);
        });
    }
    
    // تصدير للاستخدام العام
    window.applyNavbarLogoSize = applyLogoSize;
    window.getNavbarLogoSize = getLogoSize;
    
    console.log('🎯 تم تحميل سكريبت أحجام اللوجو بنجاح');
    
})();
