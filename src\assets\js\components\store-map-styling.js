/**
 * Store Map Component Styling Handler
 * Handles theme customization settings for store map component colors and styling
 */

(function() {
    'use strict';

    function initStoreMapStyling() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', applyStoreMapStyling);
        } else {
            applyStoreMapStyling();
        }

        // Listen for theme customization changes
        document.addEventListener('theme-settings-updated', applyStoreMapStyling);
    }

    function applyStoreMapStyling() {
        const storeMapElements = document.querySelectorAll('.s-block--store-map');
        
        storeMapElements.forEach(element => {
            applyComponentStyling(element);
        });
    }

    function applyComponentStyling(element) {
        const settings = getComponentSettings(element);
        
        // Apply CSS custom properties for colors
        element.style.setProperty('--title-color', settings.titleColor);
        element.style.setProperty('--description-color', settings.descriptionColor);
        element.style.setProperty('--branch-name-color', settings.branchNameColor);
        element.style.setProperty('--branch-address-color', settings.branchAddressColor);
        element.style.setProperty('--component-bg-color', settings.backgroundColor);
        element.style.setProperty('--component-border-color', settings.borderColor);

        // Apply background styling
        if (settings.backgroundEnabled) {
            element.style.backgroundColor = settings.backgroundColor;
            element.classList.add('background-enabled');
            element.classList.remove('background-disabled');
        } else {
            element.style.backgroundColor = 'transparent';
            element.classList.add('background-disabled');
            element.classList.remove('background-enabled');
        }

        // Apply border styling
        if (settings.borderEnabled) {
            element.style.border = `1px solid ${settings.borderColor}`;
            element.classList.add('border-enabled');
            element.classList.remove('border-disabled');
        } else {
            element.style.border = 'none';
            element.classList.add('border-disabled');
            element.classList.remove('border-enabled');
        }

        // Apply border radius
        element.style.borderRadius = `${settings.borderRadius}px`;

        // Apply padding classes
        applyPaddingClasses(element, settings);

        // Apply text colors directly to elements
        applyTextColors(element, settings);
    }

    function getComponentSettings(element) {
        return {
            backgroundEnabled: element.dataset.backgroundEnabled === 'true',
            backgroundColor: element.dataset.backgroundColor || '#f8f9fa',
            titleColor: element.dataset.titleColor || '#212529',
            descriptionColor: element.dataset.descriptionColor || '#6c757d',
            branchNameColor: element.dataset.branchNameColor || '#495057',
            branchAddressColor: element.dataset.branchAddressColor || '#6c757d',
            borderEnabled: element.dataset.borderEnabled === 'true',
            borderColor: element.dataset.borderColor || '#dee2e6',
            borderRadius: parseInt(element.dataset.borderRadius) || 8,
            paddingEnabled: element.dataset.paddingEnabled !== 'false',
            paddingSize: element.dataset.paddingSize || 'medium'
        };
    }

    function applyPaddingClasses(element, settings) {
        // Remove existing padding classes
        element.classList.remove('padding-small', 'padding-medium', 'padding-large', 'padding-xlarge', 'padding-disabled');
        
        if (settings.paddingEnabled) {
            element.classList.add(`padding-${settings.paddingSize}`);
        } else {
            element.classList.add('padding-disabled');
        }
    }

    function applyTextColors(element, settings) {
        // Apply title colors
        const titleElements = element.querySelectorAll('.store-map-title');
        titleElements.forEach(titleElement => {
            titleElement.style.color = settings.titleColor;
        });

        // Apply description colors
        const descriptionElements = element.querySelectorAll('.store-map-description');
        descriptionElements.forEach(descElement => {
            descElement.style.color = settings.descriptionColor;
        });

        // Apply branch name colors
        const branchNameElements = element.querySelectorAll('.branch-name');
        branchNameElements.forEach(nameElement => {
            nameElement.style.color = settings.branchNameColor;
        });

        // Apply branch address colors
        const branchAddressElements = element.querySelectorAll('.branch-address');
        branchAddressElements.forEach(addressElement => {
            addressElement.style.color = settings.branchAddressColor;
        });

        // Apply tab button colors
        const tabButtons = element.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.style.color = settings.branchNameColor;
        });
    }

    // Method to update component styling dynamically (for theme customizer)
    function updateStoreMapStyling(componentId, newSettings) {
        const element = document.getElementById(componentId);
        if (element) {
            // Update data attributes
            Object.keys(newSettings).forEach(key => {
                element.dataset[key] = newSettings[key];
            });
            
            // Reapply styling
            applyComponentStyling(element);
        }
    }

    // Method to reset component to default styling
    function resetStoreMapStyling(element) {
        element.style.backgroundColor = '';
        element.style.border = '';
        element.style.borderRadius = '';
        element.style.removeProperty('--title-color');
        element.style.removeProperty('--description-color');
        element.style.removeProperty('--branch-name-color');
        element.style.removeProperty('--branch-address-color');
        element.style.removeProperty('--component-bg-color');
        element.style.removeProperty('--component-border-color');
        
        // Reset text colors
        const textElements = element.querySelectorAll('.store-map-title, .store-map-description, .branch-name, .branch-address, .tab-button');
        textElements.forEach(textElement => {
            textElement.style.color = '';
        });

        // Reset classes
        element.classList.remove('background-enabled', 'background-disabled', 'border-enabled', 'border-disabled');
        element.classList.remove('padding-small', 'padding-medium', 'padding-large', 'padding-xlarge', 'padding-disabled');
    }

    // Initialize the styling handler
    initStoreMapStyling();

    // Export functions for external use
    window.StoreMapStyling = {
        update: updateStoreMapStyling,
        reset: resetStoreMapStyling,
        apply: applyStoreMapStyling
    };

})();
