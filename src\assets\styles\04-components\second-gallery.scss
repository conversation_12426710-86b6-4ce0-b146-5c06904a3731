/* Enhanced Second Gallery Component - Gaming Theme with Comprehensive Configuration */

/* CSS Variables for Dynamic Theming and Configuration */
:root {
    --gaming-primary: #00ff88;
    --gaming-secondary: #ff0080;
    --gaming-accent: #00d4ff;
    --gaming-primary-rgb: 0, 255, 136;
    --gaming-secondary-rgb: 255, 0, 128;
    --gaming-accent-rgb: 0, 212, 255;
    --gaming-bg: #0a0a0a;
    --gaming-dark: #1a1a1a;
    --gaming-darker: #0d0d0d;
    --gaming-white: #ffffff;
    --gaming-gray: #666666;
    --gaming-light-gray: #cccccc;

    /* Alpha variants for better performance */
    --gaming-primary-alpha-10: rgba(var(--gaming-primary-rgb), 0.1);
    --gaming-primary-alpha-20: rgba(var(--gaming-primary-rgb), 0.2);
    --gaming-primary-alpha-30: rgba(var(--gaming-primary-rgb), 0.3);
    --gaming-primary-alpha-40: rgba(var(--gaming-primary-rgb), 0.4);
    --gaming-primary-alpha-50: rgba(var(--gaming-primary-rgb), 0.5);
    --gaming-primary-alpha-60: rgba(var(--gaming-primary-rgb), 0.6);
    --gaming-primary-alpha-70: rgba(var(--gaming-primary-rgb), 0.7);
    --gaming-primary-alpha-80: rgba(var(--gaming-primary-rgb), 0.8);
    --gaming-primary-alpha-90: rgba(var(--gaming-primary-rgb), 0.9);

    --gaming-secondary-alpha-10: rgba(var(--gaming-secondary-rgb), 0.1);
    --gaming-secondary-alpha-20: rgba(var(--gaming-secondary-rgb), 0.2);
    --gaming-secondary-alpha-30: rgba(var(--gaming-secondary-rgb), 0.3);
    --gaming-secondary-alpha-40: rgba(var(--gaming-secondary-rgb), 0.4);
    --gaming-secondary-alpha-50: rgba(var(--gaming-secondary-rgb), 0.5);
    --gaming-secondary-alpha-60: rgba(var(--gaming-secondary-rgb), 0.6);
    --gaming-secondary-alpha-70: rgba(var(--gaming-secondary-rgb), 0.7);
    --gaming-secondary-alpha-80: rgba(var(--gaming-secondary-rgb), 0.8);
    --gaming-secondary-alpha-90: rgba(var(--gaming-secondary-rgb), 0.9);

    --gaming-accent-alpha-10: rgba(var(--gaming-accent-rgb), 0.1);
    --gaming-accent-alpha-20: rgba(var(--gaming-accent-rgb), 0.2);
    --gaming-accent-alpha-30: rgba(var(--gaming-accent-rgb), 0.3);
    --gaming-accent-alpha-40: rgba(var(--gaming-accent-rgb), 0.4);
    --gaming-accent-alpha-50: rgba(var(--gaming-accent-rgb), 0.5);
    --gaming-accent-alpha-60: rgba(var(--gaming-accent-rgb), 0.6);
    --gaming-accent-alpha-70: rgba(var(--gaming-accent-rgb), 0.7);
    --gaming-accent-alpha-80: rgba(var(--gaming-accent-rgb), 0.8);
    --gaming-accent-alpha-90: rgba(var(--gaming-accent-rgb), 0.9);

    /* Animation timing functions */
    --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
    --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
    --ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
    --ease-in-out-expo: cubic-bezier(0.87, 0, 0.13, 1);

    /* Dynamic configuration variables (overridden by component) */
    --title-color: var(--gaming-primary);
    --hover-overlay-color: var(--gaming-primary);
    --glow-border-color: var(--gaming-accent);
    --image-border-radius: 15px;
}

/* Scale-In-Center Animation Keyframes */
@-webkit-keyframes scale-in-center {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0);
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes scale-in-center {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0);
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }
}

/* Enhanced Scale-In-Center with Bounce */
@keyframes scale-in-center-bounce {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    60% {
        transform: scale(1.1);
        opacity: 0.9;
    }
    80% {
        transform: scale(0.95);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Main Gallery Section with Lazy Loading Animation */
.gaming-gallery-section {
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 212, 255, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #0a0a0a 0%, #0d0d0d 50%, #1a1a1a 100%);
    position: relative;
    overflow: hidden;
    padding: 80px 0;
    width: 100%;
    min-height: auto;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: opacity 1.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 1.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;

    &.animate-in {
        opacity: 1;
        transform: translateY(0) scale(1);
        animation: sectionBounceIn 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200' width='200' height='200'%3E%3Ccircle cx='50' cy='50' r='2' fill='%2300ff88' opacity='0.6'/%3E%3Ccircle cx='150' cy='50' r='1.5' fill='%23ff0080' opacity='0.4'/%3E%3Ccircle cx='100' cy='100' r='1' fill='%2300d4ff' opacity='0.5'/%3E%3Ccircle cx='50' cy='150' r='1.5' fill='%2300ff88' opacity='0.3'/%3E%3Ccircle cx='150' cy='150' r='2' fill='%23ff0080' opacity='0.4'/%3E%3C/svg%3E");
        background-size: 200px 200px;
        opacity: 0.3;
        pointer-events: none;
        animation: backgroundFloat 20s ease-in-out infinite;
    }
}

/* Section Bouncing Animation */
@keyframes sectionBounceIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-5px) scale(1.02);
    }
    85% {
        transform: translateY(2px) scale(0.99);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Background Float Animation */
@keyframes backgroundFloat {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-10px, -10px) rotate(1deg); }
    50% { transform: translate(10px, -5px) rotate(-1deg); }
    75% { transform: translate(-5px, 10px) rotate(0.5deg); }
}

/* Container Animation */
.gaming-gallery-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 0 20px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Title Wrapper Animation */
.gaming-gallery-title-wrapper {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    padding: 0 20px;
    opacity: 0;
    transform: translateY(25px);
    transition: opacity 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Title with Scale-In-Center Animation */
.gaming-gallery-title {
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: 900;
    color: #ffffff;
    background: linear-gradient(45deg, #00ff88, #00d4ff, #ff0080);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-transform: uppercase;
    letter-spacing: clamp(1px, 0.5vw, 4px);
    margin: 0 0 30px 0;
    position: relative;
    display: inline-block;
    filter: drop-shadow(0 0 20px rgba(0, 255, 136, 0.5));
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    line-height: 1.2;
    opacity: 0;
    transform: scale(0);
    transition: opacity 0.3s ease;

    &.animate-in {
        animation: scale-in-center-bounce 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards,
                   gradientText 3s ease-in-out 1.5s infinite;
    }

    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, #00ff88, #00d4ff, #ff0080);
        background-size: 200% 200%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: gradientText 3s ease-in-out infinite reverse;
        opacity: 0.3;
        z-index: -1;
    }
}

/* Gradient Text Animation */
@keyframes gradientText {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Fallback for browsers that don't support background-clip */
@supports not (-webkit-background-clip: text) {
    .gaming-gallery-title {
        color: #00ff88 !important;
        -webkit-text-fill-color: #00ff88 !important;
        background: none !important;
    }
}

/* Title Underline Animation */
.gaming-title-underline {
    width: 120px;
    height: 6px;
    background: linear-gradient(90deg, transparent, #ff0080, #00ff88, #00d4ff, transparent);
    background-size: 200% 100%;
    margin: 0 auto;
    border-radius: 3px;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.6), 0 0 40px rgba(0, 255, 136, 0.3);
    opacity: 0;
    transform: scaleX(0);
    transition: opacity 1s ease, transform 1s ease;

    &.animate-in {
        opacity: 1;
        transform: scaleX(1);
        animation: underlineFlow 2s ease-in-out infinite;
    }
}

@keyframes underlineFlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Slider Animation */
.gaming-gallery-slider {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 50%, rgba(0, 0, 0, 0.1) 100%);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(0, 255, 136, 0.3);
    padding: clamp(20px, 4vw, 50px);
    margin: 0 20px;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 0 50px rgba(0, 255, 136, 0.15);
    width: calc(100% - 40px);
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: opacity 2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0) scale(1);
        animation: sliderBounceIn 2.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
}

/* Slider Bouncing Animation */
@keyframes sliderBounceIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-5px) scale(1.02);
    }
    85% {
        transform: translateY(2px) scale(0.99);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Track Animation */
.gaming-gallery-track {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: clamp(20px, 3vw, 40px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 2.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 2.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Gallery Items with Scale-In-Center Animation */
.gaming-gallery-item {
    position: relative;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    transform-style: preserve-3d;
    opacity: 0;
    transform: scale(0);

    &.animate-in {
        animation: scale-in-center-bounce 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    /* Staggered animation delays */
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.1s; }
    &:nth-child(3) { animation-delay: 0.2s; }
    &:nth-child(4) { animation-delay: 0.3s; }
    &:nth-child(5) { animation-delay: 0.4s; }
    &:nth-child(6) { animation-delay: 0.5s; }
    &:nth-child(7) { animation-delay: 0.6s; }
    &:nth-child(8) { animation-delay: 0.7s; }
    &:nth-child(9) { animation-delay: 0.8s; }

    &:hover {
        transform: translateY(-15px) scale(1.02);
    }
}

/* Image Wrapper Styles */
.gaming-gallery-image-wrapper {
    position: relative;
    margin-bottom: 25px;
    border-radius: 20px;
    overflow: hidden;
    background: linear-gradient(145deg, #1a1a1a 0%, #0d0d0d 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(0, 255, 136, 0.2);

    &:hover {
        box-shadow: 0 8px 40px rgba(0, 0, 0, 0.5), 0 0 50px rgba(0, 255, 136, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border-color: rgba(0, 255, 136, 0.6);
        transform: scale(1.02);
    }
}

.gaming-gallery-image-container {
    position: relative;
    aspect-ratio: 16/10;
    overflow: hidden;
    border-radius: 15px;
}

.gaming-gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    filter: brightness(0.85) contrast(1.15) saturate(1.1);
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.gaming-gallery-image-wrapper:hover .gaming-gallery-image {
    transform: scale(1.08);
    filter: brightness(1) contrast(1.3) saturate(1.3);
}

/* Item Title Styles */
.gaming-gallery-item-title {
    font-size: clamp(1rem, 2.5vw, 1.4rem);
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    letter-spacing: clamp(0.5px, 0.2vw, 1px);
    text-transform: uppercase;
    position: relative;
    padding: 15px 0;
    line-height: 1.3;
}

.gaming-gallery-item:hover .gaming-gallery-item-title {
    background: linear-gradient(45deg, #00ff88, #00d4ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: none;
    filter: drop-shadow(0 0 10px rgba(0, 255, 136, 0.5));
    transform: translateY(-2px);
}

/* Overlay Styles */
.gaming-gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 3;
    border-radius: 15px;
}

.gaming-gallery-item:hover .gaming-gallery-overlay {
    opacity: 1;
}

.gaming-overlay-content {
    text-align: center;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-gallery-item:hover .gaming-overlay-content {
    transform: translateY(0);
}

.gaming-gallery-link-icon {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #00ff88, #00d4ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 0 15px #00ff88);
    animation: iconFloat 3s ease-in-out infinite;
    display: block;
    margin-bottom: 10px;
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0) scale(1);
        filter: drop-shadow(0 0 15px #00ff88);
    }
    50% {
        transform: translateY(-5px) scale(1.1);
        filter: drop-shadow(0 0 25px #00ff88);
    }
}

.gaming-overlay-text {
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
}

/* Navigation Styles */
.gaming-gallery-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 50px;
    gap: 30px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 2.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 2.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
}

.gaming-gallery-prev-btn,
.gaming-gallery-next-btn {
    background: linear-gradient(145deg, rgba(0, 255, 136, 0.15) 0%, rgba(0, 255, 136, 0.1) 100%);
    border: 2px solid rgba(0, 255, 136, 0.4);
    color: #00ff88;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    backdrop-filter: blur(15px);
    font-size: 1.2rem;

    &:hover {
        background: #00ff88;
        color: #0a0a0a;
        border-color: #00ff88;
        box-shadow: 0 0 30px rgba(0, 255, 136, 0.4), 0 0 40px rgba(0, 255, 136, 0.4);
        transform: scale(1.15) translateY(-2px);
    }
}

.gaming-gallery-dots {
    display: flex;
    gap: 15px;
    align-items: center;
}

.gaming-gallery-dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid rgba(0, 255, 136, 0.4);
    background: transparent;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    position: relative;

    &.active,
    &:hover {
        border-color: #00ff88;
        box-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
        transform: scale(1.2);
        background: radial-gradient(circle, #00ff88 0%, #00d4ff 100%);
    }

    &.active {
        animation: dotPulse 2s ease-in-out infinite;
    }
}

@keyframes dotPulse {
    0%, 100% { box-shadow: 0 0 15px rgba(0, 255, 136, 0.6); }
    50% { box-shadow: 0 0 25px rgba(0, 255, 136, 0.8); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .gaming-gallery-container {
        padding: 0 15px;
    }

    .gaming-gallery-slider {
        margin: 0 15px;
        width: calc(100% - 30px);
    }

    .gaming-gallery-track {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .gaming-gallery-section {
        padding: 60px 0;
    }

    .gaming-gallery-container {
        padding: 0 10px;
    }

    .gaming-gallery-title-wrapper {
        margin-bottom: 40px;
        padding: 0 10px;
    }

    .gaming-gallery-slider {
        margin: 0 10px;
        width: calc(100% - 20px);
        border-radius: 15px;
    }

    .gaming-gallery-track {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }

    .gaming-gallery-nav {
        margin-top: 40px;
        gap: 20px;
    }

    .gaming-gallery-prev-btn,
    .gaming-gallery-next-btn {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .gaming-gallery-section {
        padding: 40px 0;
    }

    .gaming-gallery-container {
        padding: 0 5px;
    }

    .gaming-gallery-title-wrapper {
        margin-bottom: 30px;
        padding: 0 5px;
    }

    .gaming-gallery-slider {
        margin: 0 5px;
        width: calc(100% - 10px);
        border-radius: 12px;
    }

    .gaming-gallery-track {
        grid-template-columns: 1fr;
    }

    .gaming-gallery-nav {
        margin-top: 30px;
        gap: 15px;
    }

    .gaming-gallery-prev-btn,
    .gaming-gallery-next-btn {
        width: 45px;
        height: 45px;
        font-size: 0.9rem;
    }

    .gaming-gallery-dots {
        gap: 10px;
    }

    .gaming-gallery-dot {
        width: 12px;
        height: 12px;
    }
}

@media (max-width: 320px) {
    .gaming-gallery-section {
        padding: 30px 0;
    }

    .gaming-gallery-container {
        padding: 0;
    }

    .gaming-gallery-slider {
        margin: 0;
        width: 100%;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    .gaming-gallery-track {
        gap: 15px;
    }
}

/* Accessibility - Disable animations for reduced motion */
@media (prefers-reduced-motion: reduce) {
    .gaming-gallery-section,
    .gaming-gallery-container,
    .gaming-gallery-title-wrapper,
    .gaming-gallery-title,
    .gaming-title-underline,
    .gaming-gallery-slider,
    .gaming-gallery-track,
    .gaming-gallery-item,
    .gaming-gallery-nav {
        animation: none !important;
        transition: opacity 0.3s ease !important;
        transform: none !important;
        opacity: 1 !important;
    }

    .gaming-gallery-image {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }
}

/* ===== ENHANCED CONFIGURATION STYLES ===== */

/* Title Font Size Variants */
.enhanced-second-gallery {
    &.font-size-small .gaming-gallery-title {
        font-size: 2rem;
        letter-spacing: 1px;
    }

    &.font-size-medium .gaming-gallery-title {
        font-size: 2.5rem;
        letter-spacing: 1.5px;
    }

    &.font-size-large .gaming-gallery-title {
        font-size: 3.5rem;
        letter-spacing: 2px;
    }

    &.font-size-extra-large .gaming-gallery-title {
        font-size: 4.5rem;
        letter-spacing: 3px;
    }
}

/* Title Font Weight Variants */
.enhanced-second-gallery {
    &.font-weight-normal .gaming-gallery-title {
        font-weight: 400;
    }

    &.font-weight-medium .gaming-gallery-title {
        font-weight: 500;
    }

    &.font-weight-bold .gaming-gallery-title {
        font-weight: 700;
    }

    &.font-weight-extra-bold .gaming-gallery-title {
        font-weight: 900;
    }
}

/* Title Text Alignment Variants */
.enhanced-second-gallery {
    &.text-align-right .gaming-gallery-title-wrapper {
        text-align: right;
    }

    &.text-align-center .gaming-gallery-title-wrapper {
        text-align: center;
    }

    &.text-align-left .gaming-gallery-title-wrapper {
        text-align: left;
    }
}

/* Title Animation Variants */
.enhanced-second-gallery {
    &.animation-fadeIn .gaming-gallery-title {
        opacity: 0;
        transition: opacity 1s ease;

        &.animate-in {
            opacity: 1;
        }
    }

    &.animation-slideUp .gaming-gallery-title {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 1s ease, transform 1s ease;

        &.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
    }

    &.animation-scaleIn .gaming-gallery-title {
        opacity: 0;
        transform: scale(0.8);
        transition: opacity 1s ease, transform 1s ease;

        &.animate-in {
            opacity: 1;
            transform: scale(1);
        }
    }

    &.animation-bounceIn .gaming-gallery-title {
        opacity: 0;
        transform: scale(0.3);
        transition: opacity 0.6s ease, transform 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

        &.animate-in {
            opacity: 1;
            transform: scale(1);
        }
    }

    &.animation-tracking-expand .gaming-gallery-title {
        opacity: 0;
        letter-spacing: -0.5em;
        transition: opacity 1s ease, letter-spacing 1s ease;

        &.animate-in {
            opacity: 1;
            letter-spacing: inherit;
        }
    }
}

/* Gallery Items Animation Variants */
.enhanced-second-gallery {
    &.gallery-animation-fadeIn .gaming-gallery-item {
        opacity: 0;
        transition: opacity 1s ease;

        &.animate-in {
            opacity: 1;
        }
    }

    &.gallery-animation-slideUp .gaming-gallery-item {
        opacity: 0;
        transform: translateY(50px);
        transition: opacity 1s ease, transform 1s ease;

        &.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
    }

    &.gallery-animation-scaleIn .gaming-gallery-item {
        opacity: 0;
        transform: scale(0);
        transition: opacity 1s ease, transform 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        &.animate-in {
            opacity: 1;
            transform: scale(1);
        }
    }

    &.gallery-animation-bounceIn .gaming-gallery-item {
        opacity: 0;
        transform: scale(0.3);
        transition: opacity 0.8s ease, transform 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);

        &.animate-in {
            opacity: 1;
            transform: scale(1);
        }
    }
}

/* Animation Duration Variants */
.enhanced-second-gallery {
    &.duration-fast {
        .gaming-gallery-title,
        .gaming-gallery-item {
            transition-duration: 0.5s;
        }
    }

    &.duration-medium {
        .gaming-gallery-title,
        .gaming-gallery-item {
            transition-duration: 1s;
        }
    }

    &.duration-slow {
        .gaming-gallery-title,
        .gaming-gallery-item {
            transition-duration: 1.5s;
        }
    }

    &.duration-extra-slow {
        .gaming-gallery-title,
        .gaming-gallery-item {
            transition-duration: 2s;
        }
    }
}

/* Animation Delay Variants */
.enhanced-second-gallery {
    &.delay-none {
        .gaming-gallery-title,
        .gaming-gallery-item {
            transition-delay: 0s;
        }
    }

    &.delay-short {
        .gaming-gallery-title {
            transition-delay: 0.2s;
        }
        .gaming-gallery-item {
            transition-delay: 0.4s;
        }
    }

    &.delay-medium {
        .gaming-gallery-title {
            transition-delay: 0.5s;
        }
        .gaming-gallery-item {
            transition-delay: 0.7s;
        }
    }

    &.delay-long {
        .gaming-gallery-title {
            transition-delay: 1s;
        }
        .gaming-gallery-item {
            transition-delay: 1.2s;
        }
    }
}

/* Items Per Row Layout Variants */
.enhanced-second-gallery {
    &.items-per-row-2 .gaming-gallery-track {
        grid-template-columns: repeat(2, 1fr);

        .gaming-gallery-item {
            flex: 0 0 calc(50% - 15px);
        }
    }

    &.items-per-row-3 .gaming-gallery-track {
        grid-template-columns: repeat(3, 1fr);

        .gaming-gallery-item {
            flex: 0 0 calc(33.333% - 20px);
        }
    }

    &.items-per-row-4 .gaming-gallery-track {
        grid-template-columns: repeat(4, 1fr);

        .gaming-gallery-item {
            flex: 0 0 calc(25% - 22.5px);
        }
    }

    &.items-per-row-5 .gaming-gallery-track {
        grid-template-columns: repeat(5, 1fr);

        .gaming-gallery-item {
            flex: 0 0 calc(20% - 24px);
        }
    }
}

/* Full Width Layout */
.enhanced-second-gallery {
    &.full-width-enabled {
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);

        .gaming-gallery-container.full-width {
            max-width: none;
            padding: 0 40px;
        }
    }
}

/* Hover Effects Variants */
.enhanced-second-gallery.hover-effects-enabled {
    .gaming-gallery-image-wrapper {
        transition: transform 0.3s ease;
    }

    &.hover-scale .gaming-gallery-item:hover .gaming-gallery-image-wrapper {
        transform: scale(1.05);
    }

    &.hover-rotate .gaming-gallery-item:hover .gaming-gallery-image-wrapper {
        transform: rotate(2deg) scale(1.02);
    }

    &.hover-slide .gaming-gallery-item:hover .gaming-gallery-image-wrapper {
        transform: translateY(-5px);
    }

    &.hover-pulse .gaming-gallery-item:hover .gaming-gallery-image-wrapper {
        animation: pulseHover 0.6s ease-in-out;
    }

    .gaming-gallery-overlay {
        background-color: var(--hover-overlay-color);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .gaming-gallery-item:hover .gaming-gallery-overlay {
        opacity: 0.8;
    }
}

/* Image Border Radius */
.enhanced-second-gallery {
    .gaming-gallery-image,
    .gaming-gallery-image-container {
        border-radius: var(--image-border-radius);
    }
}

/* Image Shadows */
.enhanced-second-gallery.shadows-enabled {
    .gaming-gallery-image-wrapper {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transition: box-shadow 0.3s ease;
    }

    .gaming-gallery-item:hover .gaming-gallery-image-wrapper {
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }
}

/* Glow Effects */
.enhanced-second-gallery.glow-enabled {
    .gaming-gallery-image-glow {
        border: 2px solid var(--glow-border-color);
        box-shadow:
            0 0 20px var(--glow-border-color),
            inset 0 0 20px rgba(255, 255, 255, 0.1);
        opacity: 0.7;
        transition: opacity 0.3s ease, box-shadow 0.3s ease;
    }

    .gaming-gallery-item:hover .gaming-gallery-image-glow {
        opacity: 1;
        box-shadow:
            0 0 30px var(--glow-border-color),
            0 0 60px var(--glow-border-color),
            inset 0 0 30px rgba(255, 255, 255, 0.2);
    }
}

/* Dynamic Color Application */
.enhanced-second-gallery {
    .gaming-gallery-title {
        color: var(--title-color);
    }

    .gaming-title-underline::after {
        background: linear-gradient(90deg,
            transparent 0%,
            var(--title-color) 50%,
            transparent 100%);
    }
}

/* Pulse Hover Animation */
@keyframes pulseHover {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Responsive Adjustments for Enhanced Configuration */
@media (max-width: 768px) {
    .enhanced-second-gallery {
        &.font-size-small .gaming-gallery-title {
            font-size: 1.5rem;
        }

        &.font-size-medium .gaming-gallery-title {
            font-size: 2rem;
        }

        &.font-size-large .gaming-gallery-title {
            font-size: 2.5rem;
        }

        &.font-size-extra-large .gaming-gallery-title {
            font-size: 3rem;
        }

        &.items-per-row-2 .gaming-gallery-item,
        &.items-per-row-3 .gaming-gallery-item,
        &.items-per-row-4 .gaming-gallery-item,
        &.items-per-row-5 .gaming-gallery-item {
            flex: 0 0 calc(50% - 15px);
        }
    }
}

@media (max-width: 480px) {
    .enhanced-second-gallery {
        &.items-per-row-2 .gaming-gallery-item,
        &.items-per-row-3 .gaming-gallery-item,
        &.items-per-row-4 .gaming-gallery-item,
        &.items-per-row-5 .gaming-gallery-item {
            flex: 0 0 100%;
        }
    }
}
