{#
| Variable                      | Type      | Description                                                         |
|-------------------------------|-----------|---------------------------------------------------------------------|
| component.title               | string    | Title text from admin panel                                         |
| component.font_size           | string    | Font size setting (small, medium, large, extra-large)             |
| component.font_weight         | string    | Font weight setting (normal, medium, bold, extra-bold)             |
| component.text_alignment      | string    | Text alignment (right, center, left)                               |
| component.text_color          | string    | Primary text color                                                  |
| component.text_shadow_color   | string    | Text shadow color                                                   |
| component.decoration_color    | string    | Decoration elements color                                           |
| component.enable_background   | boolean   | Enable/disable background color                                     |
| component.background_color    | string    | Background color (when enabled)                                     |
| component.animation_type      | string    | Animation type (tracking-expand, fadeIn, slideUp, scaleIn, bounceIn)|
| component.animation_duration  | string    | Animation duration (fast, medium, slow, extra-slow)                |
| component.animation_delay     | string    | Animation delay (none, short, medium, long)                        |
| component.full_width          | boolean   | Full width toggle                                                   |
| component.top_margin          | number    | Top margin in pixels                                                |
| component.bottom_margin       | number    | Bottom margin in pixels                                             |
| component.enable_text_shadow  | boolean   | Enable text shadow effect                                           |
| component.text_shadow_intensity| number   | Text shadow intensity                                               |
| component.enable_particles    | boolean   | Enable animated particles                                           |
| component.enable_circuit_decoration| boolean| Enable circuit decoration                                          |
| component.decoration_style    | string    | Decoration style (lines-with-icon, lines-only, icon-only, none)    |
#}

{# Set default values #}
{% set font_size = component.font_size.value|default('large') %}
{% set font_weight = component.font_weight.value|default('bold') %}
{% set text_alignment = component.text_alignment.value|default('center') %}
{% set text_color = component.text_color|default('#1DE9B6') %}
{% set text_shadow_color = component.text_shadow_color|default('#1DE9B6') %}
{% set decoration_color = component.decoration_color|default('#1DE9B6') %}
{% set enable_background = component.enable_background|default(false) %}
{% set background_color = component.background_color|default('#000000') %}
{% set animation_type = component.animation_type.value|default('tracking-expand') %}
{% set animation_duration = component.animation_duration.value|default('medium') %}
{% set animation_delay = component.animation_delay.value|default('none') %}
{% set full_width = component.full_width|default(false) %}
{% set top_margin = component.top_margin|default(60) %}
{% set bottom_margin = component.bottom_margin|default(60) %}
{% set enable_text_shadow = component.enable_text_shadow|default(true) %}
{% set text_shadow_intensity = component.text_shadow_intensity|default(20) %}
{% set enable_particles = component.enable_particles|default(true) %}
{% set enable_circuit_decoration = component.enable_circuit_decoration|default(true) %}
{% set decoration_style = component.decoration_style.value|default('lines-with-icon') %}

{# Build CSS classes #}
{% set font_size_class = 'font-size-' ~ font_size %}
{% set font_weight_class = 'font-weight-' ~ font_weight %}
{% set text_align_class = 'text-align-' ~ text_alignment %}
{% set animation_type_class = 'animation-' ~ animation_type %}
{% set animation_duration_class = 'duration-' ~ animation_duration %}
{% set animation_delay_class = 'delay-' ~ animation_delay %}

{# Build inline styles #}
{% set section_styles = [] %}
{% set section_styles = section_styles|merge(['padding-top: ' ~ top_margin ~ 'px']) %}
{% set section_styles = section_styles|merge(['padding-bottom: ' ~ bottom_margin ~ 'px']) %}
{% if enable_background %}
    {% set section_styles = section_styles|merge(['background-color: ' ~ background_color]) %}
{% endif %}

<section class="s-block special-title-component {{ font_size_class }} {{ font_weight_class }} {{ text_align_class }} {{ animation_type_class }} {{ animation_duration_class }} {{ animation_delay_class }}{% if full_width %} full-width{% endif %}"
         style="{{ section_styles|join('; ') }}"
         data-text-color="{{ text_color }}"
         data-text-shadow-color="{{ text_shadow_color }}"
         data-decoration-color="{{ decoration_color }}"
         data-enable-text-shadow="{{ enable_text_shadow ? 'true' : 'false' }}"
         data-text-shadow-intensity="{{ text_shadow_intensity }}">
    {% if not full_width %}<div class="container">{% endif %}
        <div class="special-title-wrapper">
            <!-- Conditional particle effects -->
            {% if enable_particles %}
            <div class="special-title-particles">
                <div class="particle particle-1"></div>
                <div class="particle particle-2"></div>
                <div class="particle particle-3"></div>
                <div class="particle particle-4"></div>
                <div class="particle particle-5"></div>
                <div class="particle particle-6"></div>
            </div>
            {% endif %}

            <!-- Main title with dynamic styling -->
            {% set title_styles = [] %}
            {% set title_styles = title_styles|merge(['color: ' ~ text_color]) %}
            {% if enable_text_shadow %}
                {% set title_styles = title_styles|merge(['text-shadow: 0 0 ' ~ text_shadow_intensity ~ 'px ' ~ text_shadow_color ~ ', 0 0 ' ~ (text_shadow_intensity * 2) ~ 'px ' ~ text_shadow_color]) %}
            {% endif %}

            <h1 class="special-title-text font-primary" style="{{ title_styles|join('; ') }}">
                {{ component.title ? component.title : 'ادخل عنوان او نص' }}
            </h1>

            <!-- Conditional decoration with dynamic styling -->
            {% if decoration_style != 'none' %}
            <div class="special-title-decoration" style="color: {{ decoration_color }}">
                {% if decoration_style in ['lines-with-icon', 'lines-only'] %}
                <span class="decoration-line" style="background: linear-gradient(90deg, transparent, {{ decoration_color }}, transparent); box-shadow: 0 0 10px {{ decoration_color }}"></span>
                {% endif %}

                {% if decoration_style in ['lines-with-icon', 'icon-only'] %}
                <span class="decoration-icon" style="color: {{ decoration_color }}">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="filter: drop-shadow(0 0 10px {{ decoration_color }})">
                        <circle cx="12" cy="12" r="8" stroke="currentColor" stroke-width="1.5"/>
                        <circle cx="12" cy="12" r="3" fill="currentColor"/>
                        <path d="M3 12H7" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M17 12H21" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M12 3V7" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M12 17V21" stroke="currentColor" stroke-width="1.5"/>
                    </svg>
                </span>
                {% endif %}

                {% if decoration_style in ['lines-with-icon', 'lines-only'] %}
                <span class="decoration-line" style="background: linear-gradient(90deg, transparent, {{ decoration_color }}, transparent); box-shadow: 0 0 10px {{ decoration_color }}"></span>
                {% endif %}
            </div>
            {% endif %}

            <!-- Simplified circuit decoration -->
            <div class="circuit-decoration">
                <div class="circuit-line circuit-left"></div>
                <div class="circuit-line circuit-right"></div>
                <div class="circuit-dot circuit-dot-1"></div>
                <div class="circuit-dot circuit-dot-2"></div>
            </div>
            <!-- Conditional circuit decoration -->
            {% if enable_circuit_decoration %}
            <div class="circuit-decoration">
                <div class="circuit-line circuit-left" style="background: linear-gradient(90deg, transparent, {{ decoration_color }}, transparent)"></div>
                <div class="circuit-line circuit-right" style="background: linear-gradient(90deg, transparent, {{ decoration_color }}, transparent)"></div>
                <div class="circuit-dot circuit-dot-1" style="background-color: {{ decoration_color }}; box-shadow: 0 0 10px {{ decoration_color }}"></div>
                <div class="circuit-dot circuit-dot-2" style="background-color: {{ decoration_color }}; box-shadow: 0 0 10px {{ decoration_color }}"></div>
                <div class="circuit-dot circuit-dot-3" style="background-color: {{ decoration_color }}; box-shadow: 0 0 10px {{ decoration_color }}"></div>
            </div>
            {% endif %}
        </div>
    {% if not full_width %}</div>{% endif %}
</section>

