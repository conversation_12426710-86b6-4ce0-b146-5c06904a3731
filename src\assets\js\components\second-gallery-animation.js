/**
 * Second Gallery Animation Component
 * Adds scale-in-center animation and gentle bouncing effects when component comes into view
 * Similar to lazy loading behavior
 */
(function() {
    'use strict';
    
    let isInitialized = false;
    const galleryObservers = new Map();
    
    class SecondGalleryAnimator {
        constructor(gallerySection) {
            this.gallerySection = gallerySection;
            this.observer = null;
            this.hasAnimated = false;
            this.elements = {
                section: null,
                container: null,
                titleWrapper: null,
                title: null,
                underline: null,
                slider: null,
                track: null,
                items: [],
                nav: null
            };
        }
        
        init() {
            this.findElements();
            this.setupAnimation();
        }
        
        findElements() {
            this.elements.section = this.gallerySection;
            this.elements.container = this.gallerySection.querySelector('.gaming-gallery-container');
            this.elements.titleWrapper = this.gallerySection.querySelector('.gaming-gallery-title-wrapper');
            this.elements.title = this.gallerySection.querySelector('.gaming-gallery-title');
            this.elements.underline = this.gallerySection.querySelector('.gaming-title-underline');
            this.elements.slider = this.gallerySection.querySelector('.gaming-gallery-slider');
            this.elements.track = this.gallerySection.querySelector('.gaming-gallery-track');
            this.elements.items = Array.from(this.gallerySection.querySelectorAll('.gaming-gallery-item'));
            this.elements.nav = this.gallerySection.querySelector('.gaming-gallery-nav');
        }
        
        setupAnimation() {
            if (!('IntersectionObserver' in window)) {
                this.fallbackAnimation();
                return;
            }
            
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.hasAnimated) {
                        // Add slight delay for smoother effect
                        setTimeout(() => {
                            this.animateGallery();
                        }, 100);
                        this.hasAnimated = true;
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
            
            this.observer.observe(this.gallerySection);
        }
        
        animateGallery() {
            // Animate main section
            if (this.elements.section) {
                this.elements.section.classList.add('animate-in');
            }
            
            // Animate container with delay
            if (this.elements.container) {
                setTimeout(() => {
                    this.elements.container.classList.add('animate-in');
                }, 200);
            }
            
            // Animate title wrapper with delay
            if (this.elements.titleWrapper) {
                setTimeout(() => {
                    this.elements.titleWrapper.classList.add('animate-in');
                }, 400);
            }
            
            // Animate title with scale-in-center effect
            if (this.elements.title) {
                setTimeout(() => {
                    this.elements.title.classList.add('animate-in');
                }, 600);
            }
            
            // Animate underline with delay
            if (this.elements.underline) {
                setTimeout(() => {
                    this.elements.underline.classList.add('animate-in');
                }, 800);
            }
            
            // Animate slider with delay
            if (this.elements.slider) {
                setTimeout(() => {
                    this.elements.slider.classList.add('animate-in');
                }, 1000);
            }
            
            // Animate track with delay
            if (this.elements.track) {
                setTimeout(() => {
                    this.elements.track.classList.add('animate-in');
                }, 1200);
            }
            
            // Animate gallery items with scale-in-center and staggered delay
            this.elements.items.forEach((item, index) => {
                setTimeout(() => {
                    item.classList.add('animate-in');
                    
                    // Ensure images in this item are visible
                    const img = item.querySelector('.gaming-gallery-image');
                    if (img) {
                        img.style.opacity = '1';
                        img.style.visibility = 'visible';
                        img.style.display = 'block';
                    }
                }, 1400 + (index * 100));
            });
            
            // Animate navigation with delay
            if (this.elements.nav) {
                setTimeout(() => {
                    this.elements.nav.classList.add('animate-in');
                }, 2000);
            }
        }
        
        fallbackAnimation() {
            // Immediate animation for browsers without IntersectionObserver
            if (!this.hasAnimated) {
                this.animateGallery();
                this.hasAnimated = true;
            }
        }
        
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            this.hasAnimated = false;
        }
    }
    
    function initGallery(gallerySection) {
        const galleryId = gallerySection.id || `second-gallery-${Date.now()}-${Math.random()}`;
        
        if (galleryObservers.has(galleryId)) return;
        
        const animator = new SecondGalleryAnimator(gallerySection);
        galleryObservers.set(galleryId, animator);
        
        animator.init();
    }
    
    function init() {
        if (isInitialized) return;
        
        const gallerySections = document.querySelectorAll('.gaming-gallery-section');
        gallerySections.forEach(initGallery);
        
        // Watch for dynamically added gallery sections
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    const addedGalleries = Array.from(mutation.addedNodes)
                        .filter(node => node.nodeType === 1 && node.classList?.contains('gaming-gallery-section'));
                    
                    addedGalleries.forEach(initGallery);
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        isInitialized = true;
    }
    
    function cleanup() {
        galleryObservers.forEach(animator => animator.destroy());
        galleryObservers.clear();
        isInitialized = false;
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        requestAnimationFrame(init);
    }
    
    // Expose for manual control
    window.secondGalleryAnimator = {
        init,
        cleanup,
        initGallery: initGallery
    };
    
})();

// Enhanced gallery interactions
document.addEventListener('DOMContentLoaded', function() {
    const gallerySections = document.querySelectorAll('.gaming-gallery-section');
    
    gallerySections.forEach(section => {
        // Force all images to be visible immediately
        const allImages = section.querySelectorAll('img, .gaming-gallery-image');
        allImages.forEach(img => {
            img.style.opacity = '1';
            img.style.visibility = 'visible';
            img.style.display = 'block';
            img.removeAttribute('loading'); // Remove lazy loading
        });
        
        const galleryItems = section.querySelectorAll('.gaming-gallery-item');
        
        // Enhanced gallery item interactions
        galleryItems.forEach((item, index) => {
            const imageWrapper = item.querySelector('.gaming-gallery-image-wrapper');
            const image = item.querySelector('.gaming-gallery-image');
            const title = item.querySelector('.gaming-gallery-item-title');
            
            if (imageWrapper && image) {
                // Add enhanced hover effects
                let isAnimating = false;
                
                const handleMouseEnter = () => {
                    if (isAnimating) return;
                    isAnimating = true;
                    
                    requestAnimationFrame(() => {
                        item.style.transform = 'translateY(-20px) scale(1.05)';
                        imageWrapper.style.boxShadow = '0 15px 50px rgba(0, 0, 0, 0.6), 0 0 60px rgba(0, 255, 136, 0.8)';
                        imageWrapper.style.borderColor = 'rgba(0, 255, 136, 0.8)';
                        
                        if (image) {
                            image.style.transform = 'scale(1.1)';
                            image.style.filter = 'brightness(1.1) contrast(1.4) saturate(1.4)';
                        }
                        
                        if (title) {
                            title.style.transform = 'translateY(-5px) scale(1.05)';
                        }
                        
                        isAnimating = false;
                    });
                };
                
                const handleMouseLeave = () => {
                    if (isAnimating) return;
                    isAnimating = true;
                    
                    requestAnimationFrame(() => {
                        item.style.transform = '';
                        imageWrapper.style.boxShadow = '';
                        imageWrapper.style.borderColor = '';
                        
                        if (image) {
                            image.style.transform = '';
                            image.style.filter = '';
                        }
                        
                        if (title) {
                            title.style.transform = '';
                        }
                        
                        isAnimating = false;
                    });
                };
                
                item.addEventListener('mouseenter', handleMouseEnter, { passive: true });
                item.addEventListener('mouseleave', handleMouseLeave, { passive: true });
                
                // Add accessibility support
                const link = item.querySelector('.gaming-gallery-link');
                if (link) {
                    link.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handleMouseEnter();
                            setTimeout(() => {
                                link.click();
                            }, 200);
                        }
                    });
                }
            }
            
            // Enhanced image loading
            if (image) {
                image.onload = function() {
                    this.style.opacity = '1';
                    this.classList.add('loaded');
                };
                
                image.onerror = function() {
                    console.warn('Failed to load gallery image:', this.src);
                    const placeholder = document.createElement('div');
                    placeholder.className = 'image-placeholder';
                    placeholder.innerHTML = '<i class="sicon-image" style="font-size: 3rem; color: #00ff88; opacity: 0.5;"></i>';
                    placeholder.style.cssText = 'display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; background: #1a1a1a;';
                    this.parentNode.replaceChild(placeholder, this);
                };
            }
        });
    });
});
