/* Special Title Component with Tracking-In-Expand Animation */

/* Tracking-In-Expand Animation Keyframes */
@-webkit-keyframes tracking-in-expand {
    0% {
        letter-spacing: -0.5em;
        opacity: 0;
    }
    40% {
        opacity: 0.6;
    }
    100% {
        opacity: 1;
        letter-spacing: normal;
    }
}

@keyframes tracking-in-expand {
    0% {
        letter-spacing: -0.5em;
        opacity: 0;
    }
    40% {
        opacity: 0.6;
    }
    100% {
        opacity: 1;
        letter-spacing: normal;
    }
}

/* Faster version of tracking-in-expand */
@keyframes tracking-in-expand-fast {
    0% {
        letter-spacing: -0.5em;
        opacity: 0;
        transform: scale(0.8);
    }
    50% {
        opacity: 0.8;
        letter-spacing: -0.1em;
        transform: scale(1.02);
    }
    100% {
        opacity: 1;
        letter-spacing: normal;
        transform: scale(1);
    }
}

/* Main Component with Lazy Loading Animation */
.special-title-component {
    padding: 60px 0;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: opacity 1.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;

    &.animate-in {
        opacity: 1;
        transform: translateY(0) scale(1);
        animation: sectionBounceIn 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    /* Full width variant */
    &.full-width {
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
        width: 100vw;
        max-width: 100vw;
    }

    /* Text alignment variants */
    &.text-align-left .special-title-wrapper {
        text-align: left;
    }

    &.text-align-center .special-title-wrapper {
        text-align: center;
    }

    &.text-align-right .special-title-wrapper {
        text-align: right;
    }

    /* Animation duration variants */
    &.duration-fast .special-title-text.animate-in {
        animation-duration: 0.5s;
    }

    &.duration-medium .special-title-text.animate-in {
        animation-duration: 1s;
    }

    &.duration-slow .special-title-text.animate-in {
        animation-duration: 1.5s;
    }

    &.duration-extra-slow .special-title-text.animate-in {
        animation-duration: 2s;
    }

    /* Animation delay variants */
    &.delay-none .special-title-text.animate-in {
        animation-delay: 0s;
    }

    &.delay-short .special-title-text.animate-in {
        animation-delay: 0.2s;
    }

    &.delay-medium .special-title-text.animate-in {
        animation-delay: 0.5s;
    }

    &.delay-long .special-title-text.animate-in {
        animation-delay: 1s;
    }
}

/* Section Bouncing Animation */
@keyframes sectionBounceIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-5px) scale(1.02);
    }
    85% {
        transform: translateY(2px) scale(0.99);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Title Wrapper */
.special-title-wrapper {
    position: relative;
    text-align: center;
    z-index: 2;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Main Title with Tracking-In-Expand Animation */
.special-title-text {
    font-family: var(--font-main), 'DINNextLTArabic', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 3.5rem;
    font-weight: 700;
    color: #1DE9B6;
    text-shadow: 0 0 20px rgba(29, 233, 182, 0.8), 0 0 40px rgba(29, 233, 182, 0.4);
    margin: 0 0 30px 0;
    position: relative;
    display: inline-block;
    opacity: 0;
    letter-spacing: -0.5em;
    transition: opacity 0.3s ease;

    &.animate-in {
        animation: tracking-in-expand-fast 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    /* Enhanced glow effect */
    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        color: rgba(29, 233, 182, 0.3);
        text-shadow: 0 0 30px rgba(29, 233, 182, 0.6);
        z-index: -1;
        opacity: 0;
        transition: opacity 0.5s ease;
    }

    &.animate-in::after {
        opacity: 1;
    }
}

/* Font size variants */
.special-title-component.font-size-small .special-title-text {
    font-size: 2rem;
}

.special-title-component.font-size-medium .special-title-text {
    font-size: 2.75rem;
}

.special-title-component.font-size-large .special-title-text {
    font-size: 3.5rem;
}

.special-title-component.font-size-extra-large .special-title-text {
    font-size: 4.5rem;
}

/* Font weight variants */
.special-title-component.font-weight-normal .special-title-text {
    font-weight: 400;
}

.special-title-component.font-weight-medium .special-title-text {
    font-weight: 500;
}

.special-title-component.font-weight-bold .special-title-text {
    font-weight: 700;
}

.special-title-component.font-weight-extra-bold .special-title-text {
    font-weight: 800;
}

/* Particles Animation */
.special-title-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
    opacity: 0;
    transition: opacity 2s ease;

    &.animate-in {
        opacity: 1;
    }

    .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: var(--particle-color, #1DE9B6);
        border-radius: 50%;
        box-shadow: 0 0 10px var(--particle-color, rgba(29, 233, 182, 0.8));
        opacity: 0;

        &.animate-in {
            animation: particleFloat 3s ease-in-out infinite;
        }
    }

    .particle-1 {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
    }

    .particle-2 {
        top: 30%;
        right: 15%;
        animation-delay: 0.5s;
    }

    .particle-3 {
        bottom: 25%;
        left: 20%;
        animation-delay: 1s;
    }

    .particle-4 {
        bottom: 35%;
        right: 25%;
        animation-delay: 1.5s;
    }

    .particle-5 {
        top: 50%;
        left: 5%;
        animation-delay: 2s;
    }

    .particle-6 {
        top: 60%;
        right: 8%;
        animation-delay: 2.5s;
    }
}

/* Particle Float Animation */
@keyframes particleFloat {
    0%, 100% {
        opacity: 0;
        transform: translateY(0) scale(1);
    }
    50% {
        opacity: 1;
        transform: translateY(-20px) scale(1.2);
    }
}

/* Decoration with Animation */
.special-title-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin: 20px 0;
    opacity: 0;
    transform: translateY(15px) scale(0.9);
    transition: opacity 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0) scale(1);
        animation: decorationBounce 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
}

.decoration-line {
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #1DE9B6, transparent);
    box-shadow: 0 0 10px rgba(29, 233, 182, 0.6);
    opacity: 0;
    transform: scaleX(0);
    transition: opacity 0.8s ease, transform 0.8s ease;

    &.animate-in {
        opacity: 1;
        transform: scaleX(1);
    }
}

.decoration-icon {
    color: #1DE9B6;
    font-size: 24px;
    opacity: 0;
    transform: rotate(0deg) scale(0.8);
    transition: opacity 0.8s ease, transform 0.8s ease;

    &.animate-in {
        opacity: 1;
        transform: rotate(360deg) scale(1);
        animation: iconPulse 2s ease-in-out infinite;
    }

    svg {
        filter: drop-shadow(0 0 10px rgba(29, 233, 182, 0.8));
    }
}

/* Decoration Bouncing Animation */
@keyframes decorationBounce {
    0% {
        opacity: 0;
        transform: translateY(15px) scale(0.9);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-2px) scale(1.05);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Icon Pulse Animation */
@keyframes iconPulse {
    0%, 100% {
        transform: rotate(360deg) scale(1);
        filter: drop-shadow(0 0 10px rgba(29, 233, 182, 0.8));
    }
    50% {
        transform: rotate(360deg) scale(1.1);
        filter: drop-shadow(0 0 20px rgba(29, 233, 182, 1));
    }
}

/* Circuit Decoration */
.circuit-decoration {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
    opacity: 0;
    transition: opacity 2.5s ease;

    &.animate-in {
        opacity: 1;
    }

    .circuit-line {
        position: absolute;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(29, 233, 182, 0.3), transparent);
        top: 50%;
        opacity: 0;

        &.animate-in {
            animation: circuitPulse 3s ease-in-out infinite;
        }

        &.circuit-left {
            left: 0;
            width: 30%;
            animation-delay: 0s;
        }

        &.circuit-right {
            right: 0;
            width: 30%;
            animation-delay: 1.5s;
        }
    }

    .circuit-dot {
        position: absolute;
        width: 6px;
        height: 6px;
        background: #1DE9B6;
        border-radius: 50%;
        box-shadow: 0 0 15px rgba(29, 233, 182, 0.8);
        top: 50%;
        transform: translateY(-50%);
        opacity: 0;

        &.animate-in {
            animation: dotPulse 2s ease-in-out infinite;
        }

        &.circuit-dot-1 {
            left: 25%;
            animation-delay: 0.5s;
        }

        &.circuit-dot-2 {
            right: 25%;
            animation-delay: 2s;
        }
    }
}

/* Circuit Animations */
@keyframes circuitPulse {
    0%, 100% {
        opacity: 0;
        transform: scaleX(0);
    }
    50% {
        opacity: 1;
        transform: scaleX(1);
    }
}

@keyframes dotPulse {
    0%, 100% {
        opacity: 0;
        transform: translateY(-50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translateY(-50%) scale(1.5);
    }
}

/* Animation type variants */
.special-title-component.animation-fadeIn .special-title-text.animate-in {
    animation: fadeIn 1s ease forwards;
}

.special-title-component.animation-slideUp .special-title-text.animate-in {
    animation: slideUp 1s ease forwards;
}

.special-title-component.animation-scaleIn .special-title-text.animate-in {
    animation: scaleIn 1s ease forwards;
}

.special-title-component.animation-bounceIn .special-title-text.animate-in {
    animation: bounceIn 1s ease forwards;
}

.special-title-component.animation-tracking-expand .special-title-text.animate-in {
    animation: tracking-in-expand-fast 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Additional animation keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .special-title-component {
        padding: 40px 0;
    }

    /* Responsive font sizes */
    .special-title-component.font-size-small .special-title-text {
        font-size: 1.5rem;
    }

    .special-title-component.font-size-medium .special-title-text {
        font-size: 2rem;
    }

    .special-title-component.font-size-large .special-title-text {
        font-size: 2.5rem;
    }

    .special-title-component.font-size-extra-large .special-title-text {
        font-size: 3rem;
    }

    .special-title-decoration {
        gap: 15px;

        .decoration-line {
            width: 40px;
        }
    }
}

@media (max-width: 480px) {
    /* Responsive font sizes for mobile */
    .special-title-component.font-size-small .special-title-text {
        font-size: 1.25rem;
    }

    .special-title-component.font-size-medium .special-title-text {
        font-size: 1.5rem;
    }

    .special-title-component.font-size-large .special-title-text {
        font-size: 2rem;
    }

    .special-title-component.font-size-extra-large .special-title-text {
        font-size: 2.5rem;
    }

    .special-title-decoration {
        gap: 10px;

        .decoration-line {
            width: 30px;
        }
    }
}

/* Accessibility - Disable animations for reduced motion */
@media (prefers-reduced-motion: reduce) {
    .special-title-component,
    .special-title-wrapper,
    .special-title-text,
    .special-title-particles,
    .special-title-decoration,
    .circuit-decoration {
        animation: none !important;
        transition: opacity 0.3s ease !important;
        transform: none !important;
        opacity: 1 !important;
        letter-spacing: normal !important;
    }

    .particle,
    .decoration-line,
    .decoration-icon,
    .circuit-line,
    .circuit-dot {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
    }
}




