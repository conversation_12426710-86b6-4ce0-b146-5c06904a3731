/**
 * Loading Screen Component - Theme Settings Integration
 * Handles customizable loading screen based on theme settings from twilight.json
 */
(function() {
    'use strict';

    let loadingScreen = null;
    let isInitialized = false;

    // Default settings
    const DEFAULT_SETTINGS = {
        loading_screen_enabled: true,
        loading_screen_duration: '2000',
        loading_screen_logo: '',
        loading_screen_background_color: '#ffffff',
        loading_screen_background_image: '',
        loading_screen_display_scope: 'all_pages',
        loading_screen_animation_type: 'spinner',
        loading_screen_text: 'جاري التحميل...',
        loading_screen_text_color: '#333333',
        loading_screen_spinner_color: '#5bd5c4'
    };

    /**
     * Get theme settings from various sources
     */
    function getThemeSettings() {
        let settings = { ...DEFAULT_SETTINGS };

        try {
            // Method 1: Check window variables (primary method)
            if (typeof window.loading_screen_settings !== 'undefined') {
                settings = { ...settings, ...window.loading_screen_settings };
                console.log('Loading Screen Settings loaded from window:', settings);
            }
            // Method 2: Check salla.config (fallback)
            else if (typeof salla !== 'undefined' && salla.config && salla.config.get) {
                Object.keys(DEFAULT_SETTINGS).forEach(key => {
                    const value = salla.config.get(key);
                    if (value !== null && value !== undefined) {
                        settings[key] = value;
                    }
                });
                console.log('Loading Screen Settings loaded from salla.config:', settings);
            }
            // Method 3: Use defaults
            else {
                console.log('Loading Screen Settings: Using defaults');
            }

            return settings;
        } catch (e) {
            console.warn('Failed to load Loading Screen settings, using defaults:', e.message);
            return settings;
        }
    }

    /**
     * Check if loading screen should be displayed on current page
     */
    function shouldDisplayOnCurrentPage(displayScope) {
        if (displayScope === 'all_pages') {
            return true;
        }
        
        if (displayScope === 'homepage_only') {
            return window.location.pathname === '/' || window.location.pathname === '/ar' || window.location.pathname === '/en';
        }
        
        if (displayScope === 'product_pages') {
            return window.location.pathname.includes('/product/') || window.location.pathname.includes('/products/');
        }
        
        return true; // Default to show on all pages
    }

    /**
     * Create loading screen HTML structure
     */
    function createLoadingScreenHTML(settings) {
        const logoHTML = settings.loading_screen_logo ? 
            `<div class="loading-logo">
                <img src="${settings.loading_screen_logo}" alt="Logo" class="loading-logo-img">
            </div>` : '';

        const textHTML = settings.loading_screen_text ? 
            `<div class="loading-text" style="color: ${settings.loading_screen_text_color};">
                ${settings.loading_screen_text}
            </div>` : '';

        const animationHTML = getAnimationHTML(settings.loading_screen_animation_type, settings.loading_screen_spinner_color);

        const backgroundStyle = getBackgroundStyle(settings);

        return `
            <div class="custom-loading-screen" style="${backgroundStyle}">
                <div class="loading-content">
                    ${logoHTML}
                    ${animationHTML}
                    ${textHTML}
                </div>
            </div>
        `;
    }

    /**
     * Get background style based on settings
     */
    function getBackgroundStyle(settings) {
        let style = `background-color: ${settings.loading_screen_background_color};`;
        
        if (settings.loading_screen_background_image) {
            style += ` background-image: url('${settings.loading_screen_background_image}');`;
            style += ` background-size: cover; background-position: center; background-repeat: no-repeat;`;
        }
        
        return style;
    }

    /**
     * Get animation HTML based on type
     */
    function getAnimationHTML(type, color) {
        switch (type) {
            case 'spinner':
                return `<div class="loading-spinner" style="border-top-color: ${color};"></div>`;
            
            case 'fade':
                return `<div class="loading-fade" style="background-color: ${color};"></div>`;
            
            case 'dots':
                return `
                    <div class="loading-dots">
                        <div class="dot" style="background-color: ${color};"></div>
                        <div class="dot" style="background-color: ${color};"></div>
                        <div class="dot" style="background-color: ${color};"></div>
                    </div>
                `;
            
            case 'progress':
                return `
                    <div class="loading-progress">
                        <div class="progress-bar" style="background-color: ${color};"></div>
                    </div>
                `;
            
            case 'pulse':
                return `<div class="loading-pulse" style="background-color: ${color};"></div>`;
            
            default:
                return `<div class="loading-spinner" style="border-top-color: ${color};"></div>`;
        }
    }

    /**
     * Create and inject CSS styles
     */
    function injectStyles() {
        if (document.getElementById('custom-loading-screen-styles')) {
            return; // Styles already injected
        }

        const styles = `
            <style id="custom-loading-screen-styles">
                .custom-loading-screen {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100vw;
                    height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 99999;
                    transition: opacity 0.5s ease, visibility 0.5s ease;
                }

                .custom-loading-screen.hidden {
                    opacity: 0;
                    visibility: hidden;
                }

                .loading-content {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 20px;
                }

                .loading-logo-img {
                    max-width: 150px;
                    max-height: 80px;
                    object-fit: contain;
                }

                .loading-text {
                    font-size: 16px;
                    font-weight: 500;
                    text-align: center;
                }

                /* Spinner Animation */
                .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid #f3f3f3;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                /* Fade Animation */
                .loading-fade {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    animation: fade 1.5s ease-in-out infinite;
                }

                @keyframes fade {
                    0%, 100% { opacity: 0.3; transform: scale(0.8); }
                    50% { opacity: 1; transform: scale(1); }
                }

                /* Dots Animation */
                .loading-dots {
                    display: flex;
                    gap: 8px;
                }

                .loading-dots .dot {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    animation: bounce 1.4s ease-in-out infinite both;
                }

                .loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
                .loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }

                @keyframes bounce {
                    0%, 80%, 100% { transform: scale(0); }
                    40% { transform: scale(1); }
                }

                /* Progress Bar Animation */
                .loading-progress {
                    width: 200px;
                    height: 4px;
                    background-color: #f3f3f3;
                    border-radius: 2px;
                    overflow: hidden;
                }

                .progress-bar {
                    height: 100%;
                    width: 0%;
                    border-radius: 2px;
                    animation: progress 2s ease-in-out infinite;
                }

                @keyframes progress {
                    0% { width: 0%; }
                    50% { width: 70%; }
                    100% { width: 100%; }
                }

                /* Pulse Animation */
                .loading-pulse {
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                    animation: pulse 2s ease-in-out infinite;
                }

                @keyframes pulse {
                    0% { transform: scale(0); opacity: 1; }
                    100% { transform: scale(1); opacity: 0; }
                }

                /* Responsive adjustments */
                @media (max-width: 768px) {
                    .loading-logo-img {
                        max-width: 120px;
                        max-height: 60px;
                    }
                    
                    .loading-text {
                        font-size: 14px;
                    }
                    
                    .loading-progress {
                        width: 150px;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    /**
     * Initialize loading screen
     */
    function initializeLoadingScreen() {
        if (isInitialized) {
            return;
        }

        const settings = getThemeSettings();

        // Check if loading screen is enabled
        if (!settings.loading_screen_enabled) {
            console.log('Loading screen is disabled');
            return;
        }

        // Check if should display on current page
        if (!shouldDisplayOnCurrentPage(settings.loading_screen_display_scope)) {
            console.log('Loading screen not configured for this page');
            return;
        }

        try {
            // Inject styles
            injectStyles();

            // Create loading screen HTML
            const loadingHTML = createLoadingScreenHTML(settings);
            
            // Remove existing loading screen if any
            const existingLoader = document.querySelector('.custom-loading-screen');
            if (existingLoader) {
                existingLoader.remove();
            }

            // Insert loading screen at the beginning of body
            document.body.insertAdjacentHTML('afterbegin', loadingHTML);
            loadingScreen = document.querySelector('.custom-loading-screen');

            // Handle duration and hiding
            handleLoadingDuration(settings);

            isInitialized = true;
            console.log('Custom loading screen initialized successfully');

        } catch (error) {
            console.error('Failed to initialize loading screen:', error);
        }
    }

    /**
     * Handle loading duration and hiding logic
     */
    function handleLoadingDuration(settings) {
        if (!loadingScreen) return;

        const duration = settings.loading_screen_duration;

        if (duration === 'auto') {
            // Hide when page is fully loaded
            if (document.readyState === 'complete') {
                hideLoadingScreen();
            } else {
                window.addEventListener('load', hideLoadingScreen, { once: true });
            }
        } else {
            // Hide after specified duration
            const durationMs = parseInt(duration) || 2000;
            
            // Also hide when page loads if it happens before the duration
            const hideOnLoad = () => {
                setTimeout(hideLoadingScreen, Math.min(durationMs, 500));
            };

            if (document.readyState === 'complete') {
                setTimeout(hideLoadingScreen, durationMs);
            } else {
                window.addEventListener('load', hideOnLoad, { once: true });
                // Fallback: hide after duration regardless
                setTimeout(hideLoadingScreen, durationMs + 1000);
            }
        }
    }

    /**
     * Hide loading screen
     */
    function hideLoadingScreen() {
        if (!loadingScreen) return;

        loadingScreen.classList.add('hidden');
        
        // Remove from DOM after transition
        setTimeout(() => {
            if (loadingScreen && loadingScreen.parentNode) {
                loadingScreen.parentNode.removeChild(loadingScreen);
            }
        }, 500);

        console.log('Loading screen hidden');
    }

    /**
     * Update loading screen settings
     */
    function updateLoadingScreen(newSettings = null) {
        const settings = newSettings || getThemeSettings();

        if (!settings.loading_screen_enabled) {
            if (loadingScreen) {
                hideLoadingScreen();
            }
            return;
        }

        if (!shouldDisplayOnCurrentPage(settings.loading_screen_display_scope)) {
            if (loadingScreen) {
                hideLoadingScreen();
            }
            return;
        }

        // Re-initialize with new settings
        isInitialized = false;
        initializeLoadingScreen();
    }

    /**
     * Handle settings updates from Salla
     */
    function handleSettingsUpdate(data) {
        const loadingSettings = [
            'loading_screen_enabled', 'loading_screen_duration', 'loading_screen_logo',
            'loading_screen_background_color', 'loading_screen_background_image',
            'loading_screen_display_scope', 'loading_screen_animation_type',
            'loading_screen_text', 'loading_screen_text_color', 'loading_screen_spinner_color'
        ];

        if (loadingSettings.includes(data.key)) {
            setTimeout(() => {
                updateLoadingScreen();
            }, 100);
        }
    }

    /**
     * Initialize when DOM is ready
     */
    function init() {
        console.log('Initializing Loading Screen Component...');

        // Initialize loading screen
        initializeLoadingScreen();

        // Listen for settings updates
        if (typeof salla !== 'undefined' && salla.event) {
            salla.event.on('settings.updated', handleSettingsUpdate);
        }

        console.log('Loading Screen Component initialized');
    }

    /**
     * Public API
     */
    window.LoadingScreen = {
        init: init,
        update: updateLoadingScreen,
        hide: hideLoadingScreen,
        getSettings: getThemeSettings
    };

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        // Use requestAnimationFrame for better performance
        requestAnimationFrame(init);
    }

    // Also initialize when Salla is ready
    if (typeof salla !== 'undefined' && salla.onReady) {
        salla.onReady(() => {
            setTimeout(init, 100);
        });
    }

})();
