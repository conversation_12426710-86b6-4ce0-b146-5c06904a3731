/**
 * Banner with Offer Animation Component
 * Adds gentle bouncing animations to banner elements when they come into view
 * Similar to lazy loading behavior
 */
(function() {
    'use strict';
    
    let isInitialized = false;
    const bannerObservers = new Map();
    
    class BannerWithOfferAnimator {
        constructor(banner) {
            this.banner = banner;
            this.observer = null;
            this.hasAnimated = false;
            this.elements = {
                section: null,
                title: null,
                wrapper: null,
                content: null,
                offerTitle: null,
                countdown: null,
                countdownBoxes: []
            };
        }
        
        init() {
            this.findElements();
            this.setupAnimation();
        }
        
        findElements() {
            this.elements.section = this.banner;
            this.elements.title = this.banner.querySelector('.s-block__title');
            this.elements.wrapper = this.banner.querySelector('[id^="offer-component-"], .banner-offer-wrapper');
            this.elements.content = this.banner.querySelector('.offer-content');
            this.elements.offerTitle = this.banner.querySelector('.offer-title');
            this.elements.countdown = this.banner.querySelector('.countdown-timer');
            this.elements.countdownBoxes = Array.from(this.banner.querySelectorAll('.countdown-box'));
        }
        
        setupAnimation() {
            if (!('IntersectionObserver' in window)) {
                this.fallbackAnimation();
                return;
            }
            
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.hasAnimated) {
                        // Add slight delay for smoother effect
                        setTimeout(() => {
                            this.animateBanner();
                        }, 150);
                        this.hasAnimated = true;
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
            
            this.observer.observe(this.banner);
        }
        
        animateBanner() {
            // Animate main section
            if (this.elements.section) {
                this.elements.section.classList.add('animate-in');
            }
            
            // Animate title with delay
            if (this.elements.title) {
                setTimeout(() => {
                    this.elements.title.classList.add('animate-in');
                }, 200);
            }
            
            // Animate wrapper with delay
            if (this.elements.wrapper) {
                setTimeout(() => {
                    this.elements.wrapper.classList.add('animate-in');
                }, 400);
            }
            
            // Animate content with delay
            if (this.elements.content) {
                setTimeout(() => {
                    this.elements.content.classList.add('animate-in');
                }, 600);
            }
            
            // Animate offer title with delay
            if (this.elements.offerTitle) {
                setTimeout(() => {
                    this.elements.offerTitle.classList.add('animate-in');
                }, 800);
            }
            
            // Animate countdown timer with delay
            if (this.elements.countdown) {
                setTimeout(() => {
                    this.elements.countdown.classList.add('animate-in');
                }, 1000);
            }
            
            // Animate countdown boxes with staggered delay
            this.elements.countdownBoxes.forEach((box, index) => {
                setTimeout(() => {
                    box.classList.add('animate-in');
                }, 1200 + (index * 100));
            });
        }
        
        fallbackAnimation() {
            // Immediate animation for browsers without IntersectionObserver
            if (!this.hasAnimated) {
                this.animateBanner();
                this.hasAnimated = true;
            }
        }
        
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            this.hasAnimated = false;
        }
    }
    
    function initBanner(banner) {
        const bannerId = banner.id || `banner-offer-${Date.now()}-${Math.random()}`;
        
        if (bannerObservers.has(bannerId)) return;
        
        const animator = new BannerWithOfferAnimator(banner);
        bannerObservers.set(bannerId, animator);
        
        animator.init();
    }
    
    function init() {
        if (isInitialized) return;
        
        const banners = document.querySelectorAll('.s-block--banner-with-offer');
        banners.forEach(initBanner);
        
        // Watch for dynamically added banners
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    const addedBanners = Array.from(mutation.addedNodes)
                        .filter(node => node.nodeType === 1 && node.classList?.contains('s-block--banner-with-offer'));
                    
                    addedBanners.forEach(initBanner);
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        isInitialized = true;
    }
    
    function cleanup() {
        bannerObservers.forEach(animator => animator.destroy());
        bannerObservers.clear();
        isInitialized = false;
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        requestAnimationFrame(init);
    }
    
    // Expose for manual control
    window.bannerWithOfferAnimator = {
        init,
        cleanup,
        initBanner: initBanner
    };
    
})();

// Enhanced countdown timer integration
document.addEventListener('DOMContentLoaded', function() {
    const banners = document.querySelectorAll('.s-block--banner-with-offer');
    
    banners.forEach(banner => {
        const countdownBoxes = banner.querySelectorAll('.countdown-box');
        
        // Enhanced countdown box interactions
        countdownBoxes.forEach((box, index) => {
            // Add enhanced hover effects
            let isAnimating = false;
            
            const handleMouseEnter = () => {
                if (isAnimating) return;
                isAnimating = true;
                
                requestAnimationFrame(() => {
                    box.style.transform = 'translateY(-8px) scale(1.05)';
                    box.style.boxShadow = '0 0 25px rgba(29, 233, 182, 0.8), 0 0 40px rgba(29, 233, 182, 0.4)';
                    
                    const value = box.querySelector('.countdown-value');
                    if (value) {
                        value.style.textShadow = '0 0 15px #1DE9B6, 0 0 25px #1DE9B6';
                        value.style.transform = 'scale(1.1)';
                    }
                    
                    isAnimating = false;
                });
            };
            
            const handleMouseLeave = () => {
                if (isAnimating) return;
                isAnimating = true;
                
                requestAnimationFrame(() => {
                    box.style.transform = '';
                    box.style.boxShadow = '';
                    
                    const value = box.querySelector('.countdown-value');
                    if (value) {
                        value.style.textShadow = '';
                        value.style.transform = '';
                    }
                    
                    isAnimating = false;
                });
            };
            
            box.addEventListener('mouseenter', handleMouseEnter, { passive: true });
            box.addEventListener('mouseleave', handleMouseLeave, { passive: true });
            
            // Add accessibility support
            box.setAttribute('role', 'timer');
            box.setAttribute('aria-live', 'polite');
            
            const value = box.querySelector('.countdown-value');
            const label = box.querySelector('.countdown-label');
            
            if (value && label) {
                box.setAttribute('aria-label', `${value.textContent} ${label.textContent} remaining`);
            }
        });
        
        // Enhanced banner image loading
        const bannerImage = banner.querySelector('.banner-image');
        if (bannerImage) {
            bannerImage.style.opacity = '1';
            bannerImage.style.visibility = 'visible';
            bannerImage.style.display = 'block';
            
            bannerImage.onload = function() {
                this.style.filter = 'brightness(0.8) contrast(1.1)';
                this.classList.add('loaded');
            };
            
            bannerImage.onerror = function() {
                console.warn('Failed to load banner image:', this.src);
                const placeholder = banner.querySelector('.placeholder-banner');
                if (placeholder) {
                    placeholder.style.display = 'flex';
                }
            };
        }
    });
});
