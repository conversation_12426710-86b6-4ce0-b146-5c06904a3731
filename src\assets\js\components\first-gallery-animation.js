/**
 * First Gallery Animation Component
 * Adds gentle bouncing animations to gallery elements when they come into view
 * Similar to lazy loading behavior
 */
(function() {
    'use strict';
    
    let isInitialized = false;
    const galleryObservers = new Map();
    
    class FirstGalleryAnimator {
        constructor(gallery) {
            this.gallery = gallery;
            this.observer = null;
            this.hasAnimated = false;
            this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
            this.elements = {
                banner: null,
                content: null,
                title: null,
                description: null,
                button: null,
                icons: null,
                cards: []
            };
        }
        
        init() {
            this.findElements();
            this.forceImagesVisible();
            this.setupAnimation();
        }

        forceImagesVisible() {
            // Force all gallery images to be visible immediately
            const allImages = this.gallery.querySelectorAll('.gallery-image, img');
            allImages.forEach(img => {
                img.style.opacity = '1';
                img.style.visibility = 'visible';
                img.style.display = 'block';
            });
        }
        
        findElements() {
            this.elements.banner = this.gallery.querySelector('.game-gallery-banner');
            this.elements.content = this.gallery.querySelector('.game-gallery-content');
            this.elements.title = this.gallery.querySelector('.game-gallery-title');
            this.elements.description = this.gallery.querySelector('.game-gallery-description');
            this.elements.button = this.gallery.querySelector('.game-gallery-button');
            this.elements.icons = this.gallery.querySelector('.game-gallery-icons');
            this.elements.cards = Array.from(this.gallery.querySelectorAll('.game-card'));
        }
        
        setupAnimation() {
            if (!('IntersectionObserver' in window)) {
                this.fallbackAnimation();
                return;
            }
            
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.hasAnimated) {
                        // Reduced delay for faster entrance
                        setTimeout(() => {
                            this.animateGallery();
                        }, 50);
                        this.hasAnimated = true;
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
            
            this.observer.observe(this.gallery);
        }
        
        animateGallery() {
            // Skip delays and complex animations if user prefers reduced motion
            if (this.prefersReducedMotion) {
                this.gallery.classList.add('animate-in');
                if (this.elements.banner) this.elements.banner.classList.add('animate-in');
                if (this.elements.content) this.elements.content.classList.add('animate-in');
                if (this.elements.title) this.elements.title.classList.add('animate-in');
                if (this.elements.description) this.elements.description.classList.add('animate-in');
                if (this.elements.button) this.elements.button.classList.add('animate-in');
                if (this.elements.icons) this.elements.icons.classList.add('animate-in');
                this.elements.cards.forEach(card => {
                    card.classList.add('animate-in');
                    const img = card.querySelector('.gallery-image');
                    if (img) {
                        img.style.opacity = '1';
                        img.style.visibility = 'visible';
                        img.style.display = 'block';
                    }
                });
                return;
            }

            // Detect mobile for faster animations
            const isMobile = window.innerWidth <= 768;

            // Use requestAnimationFrame for smooth animation batching
            requestAnimationFrame(() => {
                // Animate main gallery section
                this.gallery.classList.add('animate-in');

                // Faster delays for better user experience
                const baseDelay = isMobile ? 80 : 120;

                // Animate banner with reduced delay
                if (this.elements.banner) {
                    setTimeout(() => {
                        requestAnimationFrame(() => {
                            this.elements.banner.classList.add('animate-in');
                        });
                    }, baseDelay);
                }

                // Animate content with reduced delay
                if (this.elements.content) {
                    setTimeout(() => {
                        requestAnimationFrame(() => {
                            this.elements.content.classList.add('animate-in');
                        });
                    }, baseDelay * 2);
                }

                // Animate title with reduced delay
                if (this.elements.title) {
                    setTimeout(() => {
                        requestAnimationFrame(() => {
                            this.elements.title.classList.add('animate-in');
                        });
                    }, baseDelay * 3);
                }

                // Animate description with reduced delay
                if (this.elements.description) {
                    setTimeout(() => {
                        requestAnimationFrame(() => {
                            this.elements.description.classList.add('animate-in');
                        });
                    }, baseDelay * 4);
                }

                // Animate button with reduced delay
                if (this.elements.button) {
                    setTimeout(() => {
                        requestAnimationFrame(() => {
                            this.elements.button.classList.add('animate-in');
                        });
                    }, baseDelay * 5);
                }

                // Animate icons container with reduced delay
                if (this.elements.icons) {
                    setTimeout(() => {
                        requestAnimationFrame(() => {
                            this.elements.icons.classList.add('animate-in');
                        });
                    }, baseDelay * 6);
                }

                // Animate individual cards with optimized sliding up effect
                this.elements.cards.forEach((card, index) => {
                    // Calculate staggered delay for smooth sliding effect
                    const cardDelay = isMobile ?
                        (window.innerWidth <= 480 ? 50 : 60) : 80;

                    setTimeout(() => {
                        requestAnimationFrame(() => {
                            card.classList.add('animate-in');

                            // Ensure images in this card are visible
                            const img = card.querySelector('.gallery-image');
                            if (img) {
                                img.style.opacity = '1';
                                img.style.visibility = 'visible';
                                img.style.display = 'block';
                            }

                            // Add subtle enhancement for sliding effect
                            card.style.willChange = 'transform, opacity';

                            // Clean up will-change after animation completes
                            setTimeout(() => {
                                card.style.willChange = 'auto';
                            }, isMobile ? 700 : 900);
                        });
                    }, (baseDelay * 7) + (index * cardDelay));
                });

                // Clean up will-change after all animations complete (including card sliding)
                const totalAnimationTime = (baseDelay * 7) +
                    (this.elements.cards.length * (isMobile ?
                        (window.innerWidth <= 480 ? 50 : 60) : 80)) +
                    (isMobile ? 700 : 900);

                setTimeout(() => {
                    this.gallery.style.willChange = 'auto';
                    if (this.elements.content) this.elements.content.style.willChange = 'auto';
                    if (this.elements.title) this.elements.title.style.willChange = 'auto';
                    if (this.elements.description) this.elements.description.style.willChange = 'auto';
                    if (this.elements.button) this.elements.button.style.willChange = 'auto';
                    if (this.elements.icons) this.elements.icons.style.willChange = 'auto';
                    // Cards are cleaned up individually in their own timeouts above
                }, totalAnimationTime);
            });
        }
        
        fallbackAnimation() {
            // Immediate animation for browsers without IntersectionObserver
            if (!this.hasAnimated) {
                this.animateGallery();
                this.hasAnimated = true;
            }
        }
        
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            this.hasAnimated = false;
        }
    }
    
    function initGallery(gallery) {
        const galleryId = gallery.id || `gallery-${Date.now()}-${Math.random()}`;
        
        if (galleryObservers.has(galleryId)) return;
        
        const animator = new FirstGalleryAnimator(gallery);
        galleryObservers.set(galleryId, animator);
        
        animator.init();
    }
    
    function init() {
        if (isInitialized) return;
        
        const galleries = document.querySelectorAll('.gaming-gallery-showcase');
        galleries.forEach(initGallery);
        
        // Watch for dynamically added galleries
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    const addedGalleries = Array.from(mutation.addedNodes)
                        .filter(node => node.nodeType === 1 && node.classList?.contains('gaming-gallery-showcase'));
                    
                    addedGalleries.forEach(initGallery);
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        isInitialized = true;
    }
    
    function cleanup() {
        galleryObservers.forEach(animator => animator.destroy());
        galleryObservers.clear();
        isInitialized = false;
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        requestAnimationFrame(init);
    }
    
    // Expose for manual control
    window.firstGalleryAnimator = {
        init,
        cleanup,
        initGallery: initGallery
    };
    
})();

// Enhanced image loading and interaction effects
document.addEventListener('DOMContentLoaded', function() {
    const galleries = document.querySelectorAll('.gaming-gallery-showcase');
    
    galleries.forEach(gallery => {
        const galleryIcons = gallery.querySelectorAll('.game-gallery-icon');
        const galleryLinks = gallery.querySelectorAll('.game-gallery-link');

        // Force all images to be visible immediately
        const allImages = gallery.querySelectorAll('img, .gallery-image');
        allImages.forEach(img => {
            img.style.opacity = '1';
            img.style.visibility = 'visible';
            img.style.display = 'block';
            img.removeAttribute('loading'); // Remove lazy loading
        });

        // Optimize image loading with immediate visibility
        const optimizeImages = () => {
            galleryIcons.forEach((icon, index) => {
                const img = icon.querySelector('.gallery-image');

                if (img) {
                    // Make sure images are visible immediately
                    img.style.opacity = '1';
                    img.style.display = 'block';
                    img.style.visibility = 'visible';

                    // If image is already loaded, mark it as loaded
                    if (img.complete && img.naturalHeight !== 0) {
                        img.classList.add('loaded');
                    } else {
                        // Handle image load event
                        img.onload = function() {
                            this.style.opacity = '1';
                            this.classList.add('loaded');
                        };

                        // Handle image error
                        img.onerror = function() {
                            console.warn('Failed to load gallery image:', this.src);
                            const placeholder = document.createElement('div');
                            placeholder.className = 'icon-placeholder';
                            placeholder.innerHTML = '<i class="sicon-image" aria-hidden="true"></i>';
                            placeholder.setAttribute('aria-label', 'Image failed to load');
                            this.parentNode.replaceChild(placeholder, this);
                        };
                    }
                }
            });
        };
        
        // Enhanced hover effects with performance optimization
        const addInteractiveEffects = () => {
            galleryIcons.forEach((icon, index) => {
                let isAnimating = false;
                
                const handleMouseEnter = () => {
                    if (isAnimating) return;
                    isAnimating = true;
                    
                    requestAnimationFrame(() => {
                        icon.style.transform = 'translate3d(0, -8px, 20px)';
                        icon.style.boxShadow = '0 15px 30px rgba(29, 233, 182, 0.6), 0 0 15px rgba(29, 233, 182, 0.6)';
                        icon.style.borderColor = '#4CC9F0';
                        icon.style.willChange = 'transform';

                        const parentCard = icon.closest('.game-card');
                        if (parentCard) {
                            parentCard.style.transform = 'rotateY(10deg) rotateX(10deg)';
                            parentCard.style.willChange = 'transform';
                        }

                        const img = icon.querySelector('.gallery-image');
                        if (img) {
                            img.style.transform = 'scale3d(1.1, 1.1, 1)';
                            img.style.filter = 'drop-shadow(0 0 10px rgba(29, 233, 182, 0.8))';
                            img.style.willChange = 'transform, filter';
                        }

                        isAnimating = false;
                    });
                };
                
                const handleMouseLeave = () => {
                    if (isAnimating) return;
                    isAnimating = true;
                    
                    requestAnimationFrame(() => {
                        icon.style.transform = 'translate3d(0, 0, 0)';
                        icon.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2), 0 0 10px rgba(29, 233, 182, 0.3)';
                        icon.style.borderColor = '#1DE9B6';
                        icon.style.willChange = 'auto';

                        const parentCard = icon.closest('.game-card');
                        if (parentCard) {
                            parentCard.style.transform = '';
                            parentCard.style.willChange = 'auto';
                        }

                        const img = icon.querySelector('.gallery-image');
                        if (img) {
                            img.style.transform = '';
                            img.style.filter = 'drop-shadow(0 0 5px rgba(29, 233, 182, 0.5))';
                            img.style.willChange = 'auto';
                        }

                        isAnimating = false;
                    });
                };
                
                icon.addEventListener('mouseenter', handleMouseEnter, { passive: true });
                icon.addEventListener('mouseleave', handleMouseLeave, { passive: true });
                
                // Keyboard support for accessibility
                icon.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        const link = icon.closest('.game-gallery-link');
                        if (link) {
                            link.click();
                        }
                    }
                });
            });
        };
        
        // Initialize enhancements
        optimizeImages();
        addInteractiveEffects();
    });
});
