{# ############## Shared Variables in all views ################
| Variable                                           | Type     | Description                                                                                              |
|----------------------------------------------------|----------|----------------------------------------------------------------------------------------------------------|
| store                                              | Store    |                                                                                                          |
| store.id                                           | int      |                                                                                                          |
| store.name                                         | string   |                                                                                                          |
| store.username                                     | string   |                                                                                                          |
| store.description                                  | string   |                                                                                                          |
| store.slogan                                       | ?string  | * if feature in twilight.json                                                                            |
| store.logo                                         | string   |                                                                                                          |
| store.url                                          | string   |                                                                                                          |
| store.api                                          | string   | the api url for current store                                                                            |
| store.icon                                         | string   | *fav icon url                                                                                            |
| store.contacts                                     | object   | [mobile, phone, email, whatsapp, telegram], ex. store.contacts.email                                     |
| store.social                                       | object   | [instagram, snapchat, twitter, youtube, facebook, pinterest, maroof, whatsapp], ex. store.social.twitter |
| store.settings                                     | object   |                                                                                                          |
| store.settings.auth.email_allowed                  | bool     |                                                                                                          |
| store.settings.auth.mobile_allowed                 | bool     |                                                                                                          |
| store.settings.auth.is_email_required              | bool     |                                                                                                          |
| store.settings.cart.apply_coupon_enabled           | bool     | Does visitor allowed to applay coupon in cart page                                                       |
| store.settings.product.total_sold_enabled          | bool     |                                                                                                          |
| store.settings.product.fit_type                    | ?string  | null when equal product card size is off, or ['cover', 'contain']                                        |
| store.settings.category.testimonial_enabled        | bool     | Show random testimonials in category page                                                                |
| store.settings.tax.number                          | ?string  |                                                                                                          |
| store.settings.tax.certificate                     | ?string  | certificate image url                                                                                    |
| store.settings.tax.taxable_prices_enabled          | bool     | Does tax included in the prices ex product_price 100 & tax is 15, price will be 115                      |
| store.settings.rating_enabled                      | bool     | Is one of: store_enabled or shipping_enabled or products_enabled                                         |
| store.settings.arabic_numbers_enabled              | bool     | Does the merchant wants the numbers to be shown as Arabic format                                         |
| store.settings.is_multilingual                     | bool     |                                                                                                          |
| store.settings.currencies_enabled                  | bool     |     
| theme                                              | Theme    |                                                                                                          |
| theme.id                                           | int      |                                                                                                          |
| theme.name                                         | string   |                                                                                                          |
| theme.mode                                         | string   | `live`, `preview`                                                                                        |
| theme.is_rtl                                       | bool     |                                                                                                          |
| theme.translations_hash                            | int      | to be used in case merchant updated frontend store translations                                          |
| theme.color                                        | object   |                                                                                                          |
| theme.color.primary                                | string   |                                                                                                          |
| theme.color.text                                   | string   | suitable text color comparing to `theme.primary.color`, #000000, #FFFFFF                                 |
| theme.color.reverse_primary                        | string   |                                                                                                          |
| theme.color.reverse_text                           | string   | if `theme.text_color=#000000` means that `theme.reverse_text_color=#FFFFFF`                              |
| theme.color.is_dark                                | bool     | Does main store color `theme.primary.color` considered as dim color?                                     |
| theme.color.darker(float alpha, ?string hexColor)  | string   | get darker color from `theme.primary.color` or from passed color, `alpha` range from 0.0-1.0             |
| theme.color.lighter(float alpha, ?string hexColor) | string   | get lighter color from `theme.primary.color` or from passed color, `alpha` range from 0.0-1.0            |
| theme.font                                         | ?object  | Salla predefined font, null when the theme doesn't have `fonts` feature                                  |
| theme.font.name                                    | string   | DINNextLTArabic-Regular, Amazon-Ember, Apple, Dubai, Estedad                                             |
| theme.font.url                                     | string   | css full url ex: 'https://my_store.test/dist/fonts/default.css'                                          |
| theme.settings                                     | object   | dynamic object with values of the `settings` in twilight.json ex. `theme.settings.topnav_is_dark`        |
| theme.settings.set(settingName, value)             | string   | set global var, then get it {{theme.settings.get('my_var','fallback') }}                                 |
| theme.settings.get(settingName, default=null)      | mixed    | get theme setting ex `theme.settings.get('my_var', 'fallback')`                                          |
#}
<!DOCTYPE html>
<html lang="{{ user.language.code }}" dir="{{ user.language.dir }}">
<!-- بسم الله الرحمن الرحيم -->
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{ theme.settings.set('placeholder', 'images/placeholder.png') }}

    {% include 'pages.partials.loading-screen-assets' %}

    <script defer data-cfasync="false" src="{{ 'product-card.js'|asset }}"></script>
    <script defer src="{{ 'main-menu.js'|asset }}"></script>
    <script defer src="{{ 'categories-dropdown.js'|asset }}"></script>

    {% block head_scripts %}{% endblock %}


    <script data-cfasync="false">
        window.header_is_sticky = "{{theme.settings.get('header_is_sticky', 'Default Value')}}"
        window.imageZoom = "{{theme.settings.get('imageZoom')}}"
        window.can_access_wallet = {{ user.can_access_wallet | json_encode }}
        window.navbar_logo_size = "{{theme.settings.get('navbar_logo_size', 'medium')}}"



        // إعدادات الواتساب
        window.whatsapp_settings = {
            whatsapp_enabled: {{ theme.settings.get('whatsapp_enabled', false) | json_encode }},
            whatsapp_number: "{{ theme.settings.get('whatsapp_number', '') }}",
            whatsapp_position: "{{ theme.settings.get('whatsapp_position', 'right') }}",
            whatsapp_display_pages: "{{ theme.settings.get('whatsapp_display_pages', 'all_pages') }}"
        }

        // إعدادات شاشة التحميل
        window.loading_screen_settings = {
            loading_screen_enabled: {{ theme.settings.get('loading_screen_enabled', true) | json_encode }},
            loading_screen_duration: "{{ theme.settings.get('loading_screen_duration', '2000') }}",
            loading_screen_logo: "{{ theme.settings.get('loading_screen_logo', '') }}",
            loading_screen_background_color: "{{ theme.settings.get('loading_screen_background_color', '#ffffff') }}",
            loading_screen_background_image: "{{ theme.settings.get('loading_screen_background_image', '') }}",
            loading_screen_display_scope: "{{ theme.settings.get('loading_screen_display_scope', 'all_pages') }}",
            loading_screen_animation_type: "{{ theme.settings.get('loading_screen_animation_type', 'spinner') }}",
            loading_screen_text: "{{ theme.settings.get('loading_screen_text', 'جاري التحميل...') }}",
            loading_screen_text_color: "{{ theme.settings.get('loading_screen_text_color', '#333333') }}",
            loading_screen_spinner_color: "{{ theme.settings.get('loading_screen_spinner_color', '#5bd5c4') }}"
        }

        // Debug WhatsApp settings
        console.log('🔍 WhatsApp Settings from Twig:', window.whatsapp_settings);
        console.log('🔍 Loading Screen Settings from Twig:', window.loading_screen_settings);
        console.log('🔍 Raw theme settings check:');
        console.log('  - whatsapp_enabled:', "{{ theme.settings.get('whatsapp_enabled', 'NOT_FOUND') }}");
        console.log('  - whatsapp_number:', "{{ theme.settings.get('whatsapp_number', 'NOT_FOUND') }}");
        console.log('  - whatsapp_position:', "{{ theme.settings.get('whatsapp_position', 'NOT_FOUND') }}");
        console.log('  - whatsapp_display_pages:', "{{ theme.settings.get('whatsapp_display_pages', 'NOT_FOUND') }}");

        // سكريبت مباشر لتطبيق أحجام اللوجو
        (function() {
            'use strict';

            console.log('🚀 بدء تحميل سكريبت أحجام اللوجو المباشر');

            function applyLogoSize() {
                const logoSize = window.navbar_logo_size || 'medium';
                const logos = document.querySelectorAll('.navbar-brand img');

                console.log(`📏 تطبيق حجم اللوجو: ${logoSize} على ${logos.length} عنصر`);

                if (logos.length === 0) return;

                const sizeMap = {
                    'small': { maxWidth: '80px', maxHeight: '50px' },
                    'medium': { maxWidth: '120px', maxHeight: '70px' },
                    'large': { maxWidth: '160px', maxHeight: '90px' },
                    'extra-large': { maxWidth: '200px', maxHeight: '110px' }
                };

                const mobileSizeMap = {
                    'small': { maxWidth: '60px', maxHeight: '40px' },
                    'medium': { maxWidth: '90px', maxHeight: '55px' },
                    'large': { maxWidth: '120px', maxHeight: '70px' },
                    'extra-large': { maxWidth: '140px', maxHeight: '85px' }
                };

                const isMobile = window.innerWidth <= 768;
                const targetSize = isMobile ? mobileSizeMap[logoSize] : sizeMap[logoSize];

                if (!targetSize) return;

                logos.forEach((logo, index) => {
                    const styleString = [
                        `max-width: ${targetSize.maxWidth} !important`,
                        `max-height: ${targetSize.maxHeight} !important`,
                        `width: auto !important`,
                        `height: auto !important`,
                        `object-fit: contain !important`,
                        `transition: all 0.3s ease !important`
                    ].join('; ') + ';';

                    logo.setAttribute('style', styleString);
                    logo.style.setProperty('max-width', targetSize.maxWidth, 'important');
                    logo.style.setProperty('max-height', targetSize.maxHeight, 'important');

                    console.log(`✅ تم تطبيق الحجم على اللوجو ${index + 1}: ${targetSize.maxWidth} x ${targetSize.maxHeight}`);
                });
            }

            function initLogoSizing() {
                applyLogoSize();

                // إعادة تطبيق كل ثانية لمدة 5 ثوان
                let attempts = 0;
                const interval = setInterval(() => {
                    attempts++;
                    applyLogoSize();
                    if (attempts >= 5) clearInterval(interval);
                }, 1000);

                // مراقبة تغييرات حجم الشاشة
                window.addEventListener('resize', () => setTimeout(applyLogoSize, 100));
            }

            // تشغيل النظام
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initLogoSizing);
            } else {
                initLogoSizing();
            }

            // تصدير للاستخدام العام
            window.applyNavbarLogoSize = applyLogoSize;

        })();


    </script>
    
    {% hook 'head:start' %}
    {% hook head %}
    {% block styles %}{% endblock %}
    <link rel="stylesheet" href="{{ 'app.css' | asset }}">
    <link rel="stylesheet" href="{{ theme.font.path|cdn }}"/>
    <link rel="stylesheet" href="{{ 'fonts/sallaicons.css'|cdn }}"/>
    
    <style>
        :root {
            --font-main: '{{theme.font.name}}';
            --color-primary: {{ theme.color.primary }};
            --color-primary-dark: {{ theme.color.darker(0.15) }};
            --color-primary-light: {{ theme.color.lighter(0.15) }};
            --color-primary-reverse: {{ theme.color.reverse_text }};


        }

        /* إعدادات حجم اللوجو - CSS قوي جداً لضمان التطبيق */
        {% set logo_size = theme.settings.get('navbar_logo_size', 'medium') %}
        {% if logo_size == 'small' %}
        /* الحجم الصغير - 80px */
        body .store-header .main-nav-container .navbar-brand img,
        body .store-header .navbar-brand img,
        body .main-nav-container .navbar-brand img,
        body .navbar-brand img,
        .store-header .main-nav-container .navbar-brand img,
        .store-header .navbar-brand img,
        .main-nav-container .navbar-brand img,
        .navbar-brand img {
            max-width: 80px !important;
            max-height: 50px !important;
            width: auto !important;
            height: auto !important;
        }
        @media (max-width: 768px) {
            body .store-header .main-nav-container .navbar-brand img,
            body .store-header .navbar-brand img,
            body .main-nav-container .navbar-brand img,
            body .navbar-brand img {
                max-width: 60px !important;
                max-height: 40px !important;
            }
        }
        {% elseif logo_size == 'large' %}
        /* الحجم الكبير - 160px */
        body .store-header .main-nav-container .navbar-brand img,
        body .store-header .navbar-brand img,
        body .main-nav-container .navbar-brand img,
        body .navbar-brand img,
        .store-header .main-nav-container .navbar-brand img,
        .store-header .navbar-brand img,
        .main-nav-container .navbar-brand img,
        .navbar-brand img {
            max-width: 160px !important;
            max-height: 90px !important;
            width: auto !important;
            height: auto !important;
        }
        @media (max-width: 768px) {
            body .store-header .main-nav-container .navbar-brand img,
            body .store-header .navbar-brand img,
            body .main-nav-container .navbar-brand img,
            body .navbar-brand img {
                max-width: 120px !important;
                max-height: 70px !important;
            }
        }
        {% elseif logo_size == 'extra-large' %}
        /* الحجم الكبير جداً - 200px */
        body .store-header .main-nav-container .navbar-brand img,
        body .store-header .navbar-brand img,
        body .main-nav-container .navbar-brand img,
        body .navbar-brand img,
        .store-header .main-nav-container .navbar-brand img,
        .store-header .navbar-brand img,
        .main-nav-container .navbar-brand img,
        .navbar-brand img {
            max-width: 200px !important;
            max-height: 110px !important;
            width: auto !important;
            height: auto !important;
        }
        @media (max-width: 768px) {
            body .store-header .main-nav-container .navbar-brand img,
            body .store-header .navbar-brand img,
            body .main-nav-container .navbar-brand img,
            body .navbar-brand img {
                max-width: 140px !important;
                max-height: 85px !important;
            }
        }
        {% else %}
        /* الحجم المتوسط - 120px (افتراضي) */
        body .store-header .main-nav-container .navbar-brand img,
        body .store-header .navbar-brand img,
        body .main-nav-container .navbar-brand img,
        body .navbar-brand img,
        .store-header .main-nav-container .navbar-brand img,
        .store-header .navbar-brand img,
        .main-nav-container .navbar-brand img,
        .navbar-brand img {
            max-width: 120px !important;
            max-height: 70px !important;
            width: auto !important;
            height: auto !important;
        }
        @media (max-width: 768px) {
            body .store-header .main-nav-container .navbar-brand img,
            body .store-header .navbar-brand img,
            body .main-nav-container .navbar-brand img,
            body .navbar-brand img {
                max-width: 90px !important;
                max-height: 55px !important;
            }
        }
        {% endif %}




    </style>
    {# tracking services, custom css feature. #}
    {% hook 'head:end' %}
</head>
<body id="app" class="overflow-x-hidden {% hook 'body:classes' %}
  {{ theme.settings.get('footer_is_dark') ? ' footer-is-dark' : ' footer-is-light' }}
  {{ theme.settings.get('topnav_is_dark') ? ' topnav-is-dark' : '' }}
  {{ theme.settings.get('sticky_add_to_cart') ? ' is-sticky-product-bar' : '' }}
  navbar-logo-{{ theme.settings.get('navbar_logo_size', 'medium') }}
  ">
  <div class="loader-init"></div>
<noscript>
    To get full functionality of this site you need to enable JavaScript. Here is how
    <a href="https://www.enable-javascript.com/" rel="noreferrer"
       target="_blank">To enable JavaScript on webpage</a>.
</noscript>
<div class="app-inner flex flex-col min-h-full">
    {% hook 'body:start' %}
    {% component 'header.header' %}
    {% block content %}{% endblock %}
    {% component 'footer.footer' %}
</div>
{% hook 'body:end' %}
<script defer src="{{ 'app.js' | asset }}"></script>

{# Twilight Components #}
<salla-offer-modal></salla-offer-modal>
<salla-search></salla-search>

{% if user.type=='guest' %}
    <salla-login-modal></salla-login-modal>
{% endif %}
{% block scripts %}{% endblock %}
</body>
</html>
