/**
 * Universal Product Cards Sliding Animation System
 * Provides consistent sliding up entrance animations for all product card types
 * with lazy loading, performance optimization, and accessibility support
 */

/* Universal Product Card Selectors */
custom-salla-product-card,
.s-product-card,
.product-card,
salla-products-list custom-salla-product-card,
.products-grid .product-card,
.product-item,
.product-wrapper {
    /* Initial hidden state for lazy loading */
    opacity: 0;
    transform: translate3d(0, 30px, 0);
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;
    contain: layout style paint;
    
    /* Ensure proper display and layout */
    display: block;
    position: relative;
    
    /* Animation state management */
    &.animate-in {
        opacity: 1;
        transform: translate3d(0, 0, 0);
        animation: productCardSlideUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
    
    /* Cleanup after animation */
    &.animation-complete {
        will-change: auto;
        animation: none;
    }
}

/* Staggered Animation Delays for Wave Effect */
custom-salla-product-card:nth-child(1),
.s-product-card:nth-child(1),
.product-card:nth-child(1) {
    transition-delay: 0s;
    animation-delay: 0s;
}

custom-salla-product-card:nth-child(2),
.s-product-card:nth-child(2),
.product-card:nth-child(2) {
    transition-delay: 0.08s;
    animation-delay: 0.08s;
}

custom-salla-product-card:nth-child(3),
.s-product-card:nth-child(3),
.product-card:nth-child(3) {
    transition-delay: 0.16s;
    animation-delay: 0.16s;
}

custom-salla-product-card:nth-child(4),
.s-product-card:nth-child(4),
.product-card:nth-child(4) {
    transition-delay: 0.24s;
    animation-delay: 0.24s;
}

custom-salla-product-card:nth-child(5),
.s-product-card:nth-child(5),
.product-card:nth-child(5) {
    transition-delay: 0.32s;
    animation-delay: 0.32s;
}

custom-salla-product-card:nth-child(6),
.s-product-card:nth-child(6),
.product-card:nth-child(6) {
    transition-delay: 0.40s;
    animation-delay: 0.40s;
}

custom-salla-product-card:nth-child(7),
.s-product-card:nth-child(7),
.product-card:nth-child(7) {
    transition-delay: 0.48s;
    animation-delay: 0.48s;
}

custom-salla-product-card:nth-child(8),
.s-product-card:nth-child(8),
.product-card:nth-child(8) {
    transition-delay: 0.56s;
    animation-delay: 0.56s;
}

/* Extended support for more cards */
custom-salla-product-card:nth-child(n+9),
.s-product-card:nth-child(n+9),
.product-card:nth-child(n+9) {
    transition-delay: 0.64s;
    animation-delay: 0.64s;
}

/* Product Card Slide Up Animation - Performance Optimized */
@keyframes productCardSlideUp {
    0% {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }
    100% {
        opacity: 1;
        transform: translate3d(0, 0, 0);
        will-change: auto;
    }
}

/* Mobile Optimizations (≤768px) */
@media (max-width: 768px) {
    custom-salla-product-card,
    .s-product-card,
    .product-card,
    salla-products-list custom-salla-product-card {
        transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        
        &.animate-in {
            animation: productCardSlideUpMobile 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
    }
    
    /* Faster staggered delays for mobile */
    custom-salla-product-card:nth-child(1),
    .s-product-card:nth-child(1),
    .product-card:nth-child(1) { animation-delay: 0s; }
    
    custom-salla-product-card:nth-child(2),
    .s-product-card:nth-child(2),
    .product-card:nth-child(2) { animation-delay: 0.06s; }
    
    custom-salla-product-card:nth-child(3),
    .s-product-card:nth-child(3),
    .product-card:nth-child(3) { animation-delay: 0.12s; }
    
    custom-salla-product-card:nth-child(4),
    .s-product-card:nth-child(4),
    .product-card:nth-child(4) { animation-delay: 0.18s; }
    
    custom-salla-product-card:nth-child(5),
    .s-product-card:nth-child(5),
    .product-card:nth-child(5) { animation-delay: 0.24s; }
    
    custom-salla-product-card:nth-child(6),
    .s-product-card:nth-child(6),
    .product-card:nth-child(6) { animation-delay: 0.30s; }
    
    custom-salla-product-card:nth-child(n+7),
    .s-product-card:nth-child(n+7),
    .product-card:nth-child(n+7) { animation-delay: 0.36s; }
    
    /* Mobile-optimized slide up animation */
    @keyframes productCardSlideUpMobile {
        0% {
            opacity: 0;
            transform: translate3d(0, 25px, 0);
        }
        100% {
            opacity: 1;
            transform: translate3d(0, 0, 0);
            will-change: auto;
        }
    }
}

/* Small Mobile Optimizations (≤480px) */
@media (max-width: 480px) {
    custom-salla-product-card,
    .s-product-card,
    .product-card,
    salla-products-list custom-salla-product-card {
        transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        
        &.animate-in {
            animation: productCardSlideUpSmall 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
    }
    
    /* Even faster staggered delays for small mobile */
    custom-salla-product-card:nth-child(1),
    .s-product-card:nth-child(1),
    .product-card:nth-child(1) { animation-delay: 0s; }
    
    custom-salla-product-card:nth-child(2),
    .s-product-card:nth-child(2),
    .product-card:nth-child(2) { animation-delay: 0.05s; }
    
    custom-salla-product-card:nth-child(3),
    .s-product-card:nth-child(3),
    .product-card:nth-child(3) { animation-delay: 0.10s; }
    
    custom-salla-product-card:nth-child(4),
    .s-product-card:nth-child(4),
    .product-card:nth-child(4) { animation-delay: 0.15s; }
    
    custom-salla-product-card:nth-child(n+5),
    .s-product-card:nth-child(n+5),
    .product-card:nth-child(n+5) { animation-delay: 0.20s; }
    
    /* Small mobile-optimized slide up animation */
    @keyframes productCardSlideUpSmall {
        0% {
            opacity: 0;
            transform: translate3d(0, 20px, 0);
        }
        100% {
            opacity: 1;
            transform: translate3d(0, 0, 0);
            will-change: auto;
        }
    }
}

/* Accessibility - Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    custom-salla-product-card,
    .s-product-card,
    .product-card,
    salla-products-list custom-salla-product-card {
        transition: opacity 0.2s ease !important;
        animation: none !important;
        transform: none !important;
    }
    
    custom-salla-product-card.animate-in,
    .s-product-card.animate-in,
    .product-card.animate-in,
    salla-products-list custom-salla-product-card.animate-in {
        opacity: 1 !important;
        transform: none !important;
    }
    
    /* Disable all product card animations for reduced motion */
    @keyframes productCardSlideUp {
        0%, 100% {
            opacity: 1;
            transform: none;
        }
    }
    
    @keyframes productCardSlideUpMobile {
        0%, 100% {
            opacity: 1;
            transform: none;
        }
    }
    
    @keyframes productCardSlideUpSmall {
        0%, 100% {
            opacity: 1;
            transform: none;
        }
    }
}

/* Special handling for Salla product lists */
salla-products-list {
    contain: layout style;
}

/* Ensure proper loading states */
.product-cards-loading {
    custom-salla-product-card,
    .s-product-card,
    .product-card {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }
}

.product-cards-loaded {
    custom-salla-product-card,
    .s-product-card,
    .product-card {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}
