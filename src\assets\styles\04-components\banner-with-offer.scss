/* Banner with Offer Component - Gaming Theme */

// Variables
:root {
    --banner-gaming-primary: #1DE9B6;
    --banner-gaming-primary-rgb: 29, 233, 182;
    --banner-gaming-bg: #121212;
    --banner-gaming-white: #ffffff;
    --banner-gaming-dark: #333333;
    --banner-gaming-overlay: rgba(18, 18, 18, 0.7);
    --banner-gaming-primary-alpha-10: rgba(29, 233, 182, 0.1);
    --banner-gaming-primary-alpha-20: rgba(29, 233, 182, 0.2);
    --banner-gaming-primary-alpha-30: rgba(29, 233, 182, 0.3);
    --banner-gaming-primary-alpha-40: rgba(29, 233, 182, 0.4);
    --banner-gaming-primary-alpha-60: rgba(29, 233, 182, 0.6);
    --banner-gaming-primary-alpha-80: rgba(29, 233, 182, 0.8);
    --banner-gaming-primary-alpha-90: rgba(29, 233, 182, 0.9);
}

// Main component styles with gentle animation
.s-block--banner-with-offer {
    @apply mx-auto my-10 px-5;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    transition: opacity 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;

    &.animate-in {
        opacity: 1;
        transform: translateY(0) scale(1);
        animation: bannerSectionBounceIn 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    .s-block__title {
        opacity: 0;
        transform: translateY(15px);
        transition: opacity 1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                    transform 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        &.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

// Banner Section Bouncing Animation
@keyframes bannerSectionBounceIn {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-3px) scale(1.02);
    }
    85% {
        transform: translateY(1px) scale(0.99);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

// Banner wrapper with animation
[id^="offer-component-"],
.banner-offer-wrapper {
    @apply block w-full visible relative z-[1];
    opacity: 0;
    transform: translateY(15px) scale(0.97);
    transition: opacity 1.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;

    &.animate-in {
        opacity: 1;
        transform: translateY(0) scale(1);
        animation: bannerWrapperBounce 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
}

// Banner Wrapper Bouncing Animation
@keyframes bannerWrapperBounce {
    0% {
        opacity: 0;
        transform: translateY(15px) scale(0.97);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-2px) scale(1.01);
    }
    85% {
        transform: translateY(1px) scale(0.995);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

// Offer container
[id^="offer-component-"] .offer-container,
.banner-offer-wrapper .offer-container {
    @apply relative block w-full overflow-hidden;
    border-radius: 12px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 20px var(--banner-gaming-primary-alpha-40);
    background-color: var(--banner-gaming-bg);
    border: 1px solid var(--banner-gaming-primary);
    min-height: 400px;
    height: 400px;
    width: 100%;
    will-change: transform;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6), 0 0 30px var(--banner-gaming-primary-alpha-60);
    }
}

// Banner image
[id^="offer-component-"] .banner-image,
.banner-offer-wrapper .banner-image {
    @apply w-full block relative;
    width: 100% !important;
    height: 400px;
    object-fit: cover;
    object-position: center;
    filter: brightness(0.8) contrast(1.1);
    transition: filter 0.3s ease;
    background: var(--banner-gaming-bg);

    // Alternative: if you want the image to contain (show full image)
    &.contain-mode {
        object-fit: contain;
        object-position: center;
    }

    // Alternative: if you want the image to fill completely
    &.fill-mode {
        object-fit: fill;
    }

    // Alternative: if you want the image to scale down only
    &.scale-down-mode {
        object-fit: scale-down;
        object-position: center;
    }
}

// Placeholder banner
[id^="offer-component-"] .placeholder-banner,
.banner-offer-wrapper .placeholder-banner {
    @apply w-full flex items-center justify-center relative overflow-hidden;
    height: 400px;
    background: linear-gradient(135deg, var(--banner-gaming-bg) 0%, var(--banner-gaming-dark) 100%);
    border-radius: 8px;

    &::before {
        content: '';
        @apply absolute inset-0;
        background: repeating-linear-gradient(
            45deg,
            var(--banner-gaming-primary-alpha-10),
            var(--banner-gaming-primary-alpha-10) 20px,
            transparent 20px,
            transparent 40px
        );
        opacity: 0.3;
    }
}

// Placeholder content
[id^="offer-component-"] .placeholder-content,
.banner-offer-wrapper .placeholder-content {
    @apply text-center relative z-[1];
}

[id^="offer-component-"] .placeholder-icon,
.banner-offer-wrapper .placeholder-icon {
    @apply block mb-4;
    color: var(--banner-gaming-primary);
    text-shadow: 0 0 20px var(--banner-gaming-primary);
    animation: bannerPulse 2s infinite;
    font-size: 4rem;
}

[id^="offer-component-"] .placeholder-content h3,
.banner-offer-wrapper .placeholder-content h3 {
    @apply text-white font-bold my-4;
    font-size: 2rem;
}

[id^="offer-component-"] .placeholder-content p,
.banner-offer-wrapper .placeholder-content p {
    @apply text-gray-300;
    font-size: 1rem;
}

// Overlay
[id^="offer-component-"] .overlay,
.banner-offer-wrapper .overlay {
    @apply absolute inset-0 z-[1];
    background: linear-gradient(
        rgba(18, 18, 18, 0.7),
        rgba(18, 18, 18, 0.9)
    );

    &::before {
        content: '';
        @apply absolute inset-0;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50' width='50' height='50'%3E%3Ccircle cx='10' cy='10' r='2' fill='%231de9b610'/%3E%3Ccircle cx='40' cy='20' r='1.5' fill='%231de9b608'/%3E%3Ccircle cx='25' cy='35' r='1' fill='%231de9b605'/%3E%3C/svg%3E");
        background-size: 50px 50px;
        opacity: 0.3;
    }
}

// Offer content with animation
[id^="offer-component-"] .offer-content,
.banner-offer-wrapper .offer-content {
    @apply absolute inset-0 z-[2] flex flex-col justify-center items-center text-center text-white;
    padding: 20px;
    opacity: 0;
    transform: translateY(25px) scale(0.95);
    transition: opacity 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0) scale(1);
        animation: offerContentBounce 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
}

// Offer Content Bouncing Animation
@keyframes offerContentBounce {
    0% {
        opacity: 0;
        transform: translateY(25px) scale(0.95);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-4px) scale(1.02);
    }
    85% {
        transform: translateY(1px) scale(0.99);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

// Offer title with animation
[id^="offer-component-"] .offer-title,
.banner-offer-wrapper .offer-title {
    @apply text-white font-bold mb-5;
    font-size: 28px;
    text-shadow: 0 0 10px var(--banner-gaming-primary), 0 0 20px var(--banner-gaming-primary);
    letter-spacing: 1px;
    will-change: text-shadow;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
        animation: offerTitleBounce 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
}

// Offer Title Bouncing Animation
@keyframes offerTitleBounce {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-3px) scale(1.03);
    }
    85% {
        transform: translateY(1px) scale(0.98);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

// Countdown timer with animation
[id^="offer-component-"] .countdown-timer {
    @apply flex justify-center flex-wrap mb-5;
    gap: 10px;
    opacity: 0;
    transform: translateY(15px);
    transition: opacity 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
}

// Countdown box with animation
[id^="offer-component-"] .countdown-box {
    @apply text-center relative overflow-hidden;
    background: var(--banner-gaming-overlay);
    color: var(--banner-gaming-white);
    border-radius: 12px;
    padding: 15px;
    min-width: 70px;
    box-shadow:
        0 0 10px var(--banner-gaming-primary-alpha-60),
        0 0 20px var(--banner-gaming-primary-alpha-20),
        inset 0 0 15px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.2, 0, 0.2, 1);
    will-change: transform, box-shadow;
    transform: translateZ(0);
    border: 1px solid var(--banner-gaming-primary);
    animation: bannerPulse 2s ease-in-out infinite, bannerGlow 3s ease-in-out infinite;
    opacity: 0;
    transform: translateY(20px) scale(0.9);
    transition: opacity 1.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.animate-in {
        opacity: 1;
        transform: translateY(0) scale(1);
        animation: countdownBoxBounce 1.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards,
                   bannerPulse 2s ease-in-out 2s infinite,
                   bannerGlow 3s ease-in-out 2s infinite;
    }

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 0 15px var(--banner-gaming-primary-alpha-90), 0 0 30px var(--banner-gaming-primary-alpha-30);
    }

    /* Staggered animation delays */
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.1s; }
    &:nth-child(3) { animation-delay: 0.2s; }
    &:nth-child(4) { animation-delay: 0.3s; }
}

// Countdown Box Bouncing Animation
@keyframes countdownBoxBounce {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    70% {
        opacity: 0.9;
        transform: translateY(-3px) scale(1.05);
    }
    85% {
        transform: translateY(1px) scale(0.98);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

// Countdown value
[id^="offer-component-"] .countdown-value {
    @apply block font-bold;
    font-size: 28px;
    text-shadow: 0 0 10px var(--banner-gaming-primary), 0 0 5px var(--banner-gaming-primary);
    color: var(--banner-gaming-primary);
    will-change: transform, text-shadow;
}

// Countdown label
[id^="offer-component-"] .countdown-label {
    @apply block mt-1 font-medium uppercase;
    font-size: 14px;
    letter-spacing: 1px;
}

// Offer ended badge
[id^="offer-component-"] .offer-ended-badge {
    @apply absolute justify-center items-center rounded-full hidden z-[3];
    top: -25px;
    right: -25px;
    background: linear-gradient(135deg, var(--banner-gaming-bg), var(--banner-gaming-dark));
    color: var(--banner-gaming-primary);
    width: 120px;
    height: 120px;
    transform: rotate(15deg);
    box-shadow: 0 0 15px var(--banner-gaming-primary-alpha-60), 0 0 30px var(--banner-gaming-primary-alpha-30);
    border: 2px dashed var(--banner-gaming-primary);
    overflow: hidden;
    will-change: transform, opacity;
}

[id^="offer-component-"] .badge-pattern {
    @apply absolute inset-0;
    background: repeating-linear-gradient(
        45deg, 
        var(--banner-gaming-primary-alpha-10), 
        var(--banner-gaming-primary-alpha-10) 10px, 
        transparent 10px, 
        transparent 20px
    );
}

[id^="offer-component-"] .badge-text {
    @apply font-bold text-center relative;
    font-size: 18px;
    text-shadow: 0 0 10px var(--banner-gaming-primary), 0 0 20px var(--banner-gaming-primary);
}

// CTA Button
[id^="offer-component-"] .offer-cta-button {
    @apply inline-block no-underline font-bold relative overflow-hidden mt-5 uppercase;
    background: var(--banner-gaming-overlay);
    color: var(--banner-gaming-primary);
    padding: 12px 30px;
    border-radius: 50px;
    box-shadow: 0 0 10px var(--banner-gaming-primary-alpha-60), 0 0 20px var(--banner-gaming-primary-alpha-20);
    transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.2, 0, 0.2, 1),
              background 0.3s ease;
    will-change: transform, box-shadow;
    transform: translateY(0);
    border: 1px solid var(--banner-gaming-primary);
    letter-spacing: 1px;

    &:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 0 15px var(--banner-gaming-primary-alpha-90), 0 0 30px var(--banner-gaming-primary-alpha-40);
        background: var(--banner-gaming-primary-alpha-20);
        color: var(--banner-gaming-white);
    }

    &:active {
        transform: translateY(1px);
        box-shadow: 0 0 5px var(--banner-gaming-primary-alpha-60);
    }
}

[id^="offer-component-"] .button-text {
    @apply relative z-[2];
}

// Particles
[id^="offer-component-"] .particles {
    @apply absolute inset-0 overflow-hidden z-[1] pointer-events-none;
}

[id^="offer-component-"] .particle {
    @apply absolute rounded-full;
    width: 4px;
    height: 4px;
    background-color: var(--banner-gaming-primary);
    box-shadow: 0 0 8px var(--banner-gaming-primary);
    opacity: 0;
    will-change: transform, opacity;
    transform: translateZ(0);
}

[id^="offer-component-"] .particle-1 {
    top: 20%;
    left: 10%;
    animation: bannerFloat1 12s infinite ease-in-out;
}

[id^="offer-component-"] .particle-2 {
    top: 60%;
    right: 15%;
    animation: bannerFloat2 15s infinite ease-in-out 2s;
}

[id^="offer-component-"] .particle-3 {
    bottom: 30%;
    left: 70%;
    animation: bannerFloat3 18s infinite ease-in-out 4s;
}

// Animations
@keyframes bannerPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        text-shadow: 0 0 20px var(--banner-gaming-primary);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
        text-shadow: 0 0 30px var(--banner-gaming-primary), 0 0 40px var(--banner-gaming-primary);
    }
}

@keyframes bannerGlow {
    0%, 100% { 
        box-shadow: 0 0 20px var(--banner-gaming-primary-alpha-30); 
    }
    50% { 
        box-shadow: 0 0 30px var(--banner-gaming-primary-alpha-60); 
    }
}

@keyframes bannerFloat1 {
    0% { opacity: 0; transform: translate(0, 0); }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { opacity: 0; transform: translate(-30px, -80px); }
}

@keyframes bannerFloat2 {
    0% { opacity: 0; transform: translate(0, 0); }
    15% { opacity: 1; }
    85% { opacity: 1; }
    100% { opacity: 0; transform: translate(40px, -100px); }
}

@keyframes bannerFloat3 {
    0% { opacity: 0; transform: translate(0, 0); }
    20% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; transform: translate(-20px, -120px); }
}

// Accessibility - Disable animations for reduced motion
@media (prefers-reduced-motion: reduce) {
    .s-block--banner-with-offer,
    .s-block--banner-with-offer .s-block__title,
    [id^="offer-component-"],
    .banner-offer-wrapper,
    .offer-content,
    .offer-title,
    .countdown-timer,
    .countdown-box {
        animation: none !important;
        transition: opacity 0.3s ease !important;
        transform: none !important;
        opacity: 1 !important;
    }

    .banner-image {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }
}

// Enhanced Responsive Design
@media (max-width: 1200px) {
    [id^="offer-component-"] .countdown-timer {
        gap: 15px;
    }

    [id^="offer-component-"] .countdown-box {
        min-width: 80px;
        padding: 12px;
    }
}

@media (max-width: 992px) {
    .s-block--banner-with-offer {
        @apply px-4;
    }

    [id^="offer-component-"] .offer-content {
        padding: 30px 15px;
    }

    [id^="offer-component-"] .offer-title {
        font-size: 24px;
        margin-bottom: 25px;
    }

    [id^="offer-component-"] .countdown-timer {
        gap: 12px;
        margin: 25px 0;
    }
}

@media (max-width: 768px) {
    [id^="offer-component-"] .banner-image,
    [id^="offer-component-"] .placeholder-banner {
        height: 300px;
        max-height: 300px;
        width: 100% !important;
    }

    [id^="offer-component-"] .banner-image {
        object-fit: cover;
        object-position: center;
    }

    [id^="offer-component-"] .offer-container {
        min-height: 300px;
        height: 300px;
        margin: 15px 0;
        width: 100%;
    }

    [id^="offer-component-"] .offer-title {
        font-size: 20px;
        margin-bottom: 20px;
    }

    [id^="offer-component-"] .countdown-timer {
        gap: 8px;
        margin: 20px 0;
    }

    [id^="offer-component-"] .countdown-box {
        min-width: 65px;
        padding: 10px 8px;
    }

    [id^="offer-component-"] .countdown-value {
        font-size: 20px;
    }

    [id^="offer-component-"] .countdown-label {
        font-size: 12px;
        margin-top: 4px;
    }

    [id^="offer-component-"] .offer-cta-button {
        padding: 10px 25px;
        font-size: 14px;
        margin-top: 20px;
    }
}

@media (max-width: 576px) {
    [id^="offer-component-"] .banner-image,
    [id^="offer-component-"] .placeholder-banner {
        height: 250px;
        max-height: 250px;
        width: 100% !important;
    }

    [id^="offer-component-"] .banner-image {
        object-fit: cover;
        object-position: center;
        max-height: 250px;
        width: 100% !important;
    }

    [id^="offer-component-"] .offer-container {
        min-height: 250px;
        height: 250px;
        margin: 10px 0;
        width: 100%;
    }

    [id^="offer-component-"] .offer-content {
        padding: 20px 10px;
    }

    [id^="offer-component-"] .offer-title {
        font-size: 18px;
        margin-bottom: 15px;
    }

    [id^="offer-component-"] .countdown-timer {
        gap: 6px;
        margin: 15px 0;
    }

    [id^="offer-component-"] .countdown-box {
        min-width: 55px;
        padding: 8px 6px;
        border-radius: 8px;
    }

    [id^="offer-component-"] .countdown-value {
        font-size: 18px;
    }

    [id^="offer-component-"] .countdown-label {
        font-size: 10px;
        margin-top: 3px;
    }

    [id^="offer-component-"] .offer-cta-button {
        padding: 8px 20px;
        font-size: 13px;
        margin-top: 15px;
    }

    [id^="offer-component-"] .placeholder-content h3 {
        font-size: 1.5rem;
    }

    [id^="offer-component-"] .placeholder-content p {
        font-size: 0.9rem;
    }

    [id^="offer-component-"] .placeholder-icon {
        font-size: 3rem !important;
    }
}

@media (max-width: 400px) {
    [id^="offer-component-"] .countdown-timer {
        gap: 4px;
    }

    [id^="offer-component-"] .countdown-box {
        min-width: 50px;
        padding: 6px 4px;
    }

    [id^="offer-component-"] .countdown-value {
        font-size: 16px;
    }

    [id^="offer-component-"] .countdown-label {
        font-size: 9px;
    }
}

// Accessibility - Reduced motion
@media (prefers-reduced-motion: reduce) {
    [id^="offer-component-"] * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
