/**
 * Gaming Theme Moving Text Component
 * Enhanced functionality and animations for the moving text banner with speed control
 */

(function() {
    'use strict';

    class GamingMovingText {
        constructor() {
            this.sections = [];
            this.isVisible = false;
            this.animationFrameId = null;
            this.intersectionObserver = null;
            this.resizeTimeout = null;
            
            this.init();
        }

        init() {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.setup());
            } else {
                this.setup();
            }
        }

        setup() {
            this.findMovingTextSections();
            this.setupIntersectionObserver();
            this.setupEventListeners();
            this.enhanceAccessibility();
        }

        findMovingTextSections() {
            const sections = document.querySelectorAll('.gaming-moving-text-section');

            sections.forEach((section, index) => {
                const textElement = section.querySelector('.gaming-moving-text');
                if (!textElement) return;

                const originalText = textElement.textContent.trim();

                // Get speed and color from data attributes (from Twig template)
                const dataSpeed = section.getAttribute('data-speed');
                const dataColor = section.getAttribute('data-color');
                const position = section.getAttribute('data-position') || index;

                // Use data speed if available, otherwise calculate based on text length
                const animationSpeed = dataSpeed ? parseFloat(dataSpeed) : this.calculateAnimationSpeed(originalText.length);

                const sectionData = {
                    id: `gaming-moving-text-${position}`,
                    section,
                    textElement,
                    originalText,
                    animationSpeed,
                    backgroundColor: dataColor || '#000000',
                    position,
                    isVisible: false
                };

                this.sections.push(sectionData);
                this.setupSection(sectionData);
            });
        }

        getCustomSpeed(section) {
            // Get speed from CSS custom property
            const speedValue = section.style.getPropertyValue('--animation-speed');
            if (speedValue) {
                const numericValue = parseFloat(speedValue.replace('s', ''));
                return isNaN(numericValue) ? null : numericValue;
            }
            return null;
        }

        setupSection(sectionData) {
            const { section, textElement, animationSpeed, backgroundColor, position } = sectionData;

            // Set unique ID based on position
            const uniqueId = `moving-text-${position}`;
            if (!section.id || section.id === uniqueId) {
                section.id = uniqueId;
            }

            // Use the animation speed from data attribute (already parsed)
            const finalSpeed = animationSpeed;

            console.log(`Setting up section ${position} with speed: ${finalSpeed}s and color: ${backgroundColor}`);

            // Force apply animation speed and color for this specific component
            this.forceApplySettings(section, textElement, finalSpeed, backgroundColor);

            // Add data attributes for debugging and CSS customization
            textElement.setAttribute('data-length', sectionData.originalText.length);
            textElement.setAttribute('data-speed', finalSpeed);
            textElement.setAttribute('data-position', position);
            section.setAttribute('data-color', backgroundColor);
            section.setAttribute('data-speed', finalSpeed);

            // Setup hover effects
            this.setupHoverEffects(sectionData);
        }

        forceApplySettings(section, textElement, speed, color) {
            const isDesktop = window.innerWidth >= 1024;
            const animationName = isDesktop ? 'moveTextDesktop' : 'moveTextMobile';

            // Force set animation with correct speed
            textElement.style.animation = `${animationName} ${speed}s linear infinite`;

            // Force set background color
            section.style.setProperty('--section-color', color);
            section.style.background = color;
            section.style.background = `linear-gradient(135deg, ${color} 0%, color-mix(in srgb, ${color} 80%, #1a1a2e) 50%, ${color} 100%)`;

            console.log(`Applied speed ${speed}s and color ${color} to moving text section ${section.getAttribute('data-position')}`);
        }

        applyBackgroundColor(section, color) {
            // Apply solid color first
            section.style.backgroundColor = color;

            // Try to apply gradient with color-mix (modern browsers)
            try {
                section.style.background = `linear-gradient(135deg, ${color} 0%, color-mix(in srgb, ${color} 80%, #1a1a2e) 50%, ${color} 100%)`;
            } catch (e) {
                // Fallback for older browsers
                section.style.background = `linear-gradient(135deg, ${color} 0%, ${color} 50%, ${color} 100%)`;
            }
        }

        calculateAnimationSpeed(textLength) {
            // Base speed calculation: longer text = slower animation
            const baseSpeed = 25; // seconds
            const lengthFactor = Math.max(0.5, textLength / 20);
            return Math.round(baseSpeed * lengthFactor);
        }

        setupHoverEffects(sectionData) {
            const { section, textElement } = sectionData;
            
            section.addEventListener('mouseenter', () => {
                textElement.style.animationPlayState = 'paused';
            });
            
            section.addEventListener('mouseleave', () => {
                textElement.style.animationPlayState = 'running';
            });
        }

        setupIntersectionObserver() {
            if (!window.IntersectionObserver) return;

            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    const sectionData = this.sections.find(s => s.section === entry.target);
                    if (sectionData) {
                        sectionData.isVisible = entry.isIntersecting;
                        this.toggleAnimation(sectionData, entry.isIntersecting);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            this.sections.forEach(sectionData => {
                this.intersectionObserver.observe(sectionData.section);
            });
        }

        toggleAnimation(sectionData, shouldPlay) {
            const { textElement } = sectionData;
            
            if (shouldPlay) {
                textElement.style.animationPlayState = 'running';
            } else {
                textElement.style.animationPlayState = 'paused';
            }
        }

        setupEventListeners() {
            // Handle window resize with passive listener
            window.addEventListener('resize', () => {
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => this.handleResize(), 250);
            }, { passive: true });

            // Handle visibility change
            document.addEventListener('visibilitychange', () => {
                this.sections.forEach(sectionData => {
                    this.toggleAnimation(sectionData, !document.hidden && sectionData.isVisible);
                });
            }, { passive: true });

            // Pause animations when page is not visible to save resources
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.pauseAll();
                } else {
                    this.resumeAll();
                }
            }, { passive: true });
        }

        handleResize() {
            // Use requestAnimationFrame to batch DOM updates
            requestAnimationFrame(() => {
                this.sections.forEach(sectionData => {
                    const { section, textElement, animationSpeed, backgroundColor, position } = sectionData;

                    // Keep the original speed from data attribute
                    const finalSpeed = animationSpeed;

                    // Force apply settings with new screen size considerations
                    this.forceApplySettings(section, textElement, finalSpeed, backgroundColor);

                    // Update spacing for pseudo-elements on mobile
                    const isDesktop = window.innerWidth >= 1024;
                    if (!isDesktop) {
                        const spacing = window.innerWidth <= 480 ? '30px' :
                                      window.innerWidth <= 768 ? '50px' : '80px';
                        textElement.style.setProperty('--spacing', spacing);
                    }
                });
            });
        }

        enhanceAccessibility() {
            // Add ARIA labels and respect reduced motion preferences
            this.sections.forEach(sectionData => {
                const { section, textElement, originalText } = sectionData;
                
                section.setAttribute('aria-label', `إعلان متحرك: ${originalText}`);
                textElement.setAttribute('aria-hidden', 'true');
                
                // Respect prefers-reduced-motion
                if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                    textElement.style.animation = 'none';
                    textElement.style.position = 'static';
                    textElement.style.transform = 'none';
                }
            });
        }

        // Public API methods
        pauseAll() {
            this.sections.forEach(sectionData => {
                sectionData.textElement.style.animationPlayState = 'paused';
            });
        }

        resumeAll() {
            this.sections.forEach(sectionData => {
                if (sectionData.isVisible) {
                    sectionData.textElement.style.animationPlayState = 'running';
                }
            });
        }

        updateSpeed(sectionId, newSpeed) {
            const sectionData = this.sections.find(s => s.id === sectionId);
            if (sectionData) {
                console.log(`Updating speed for section ${sectionId} to ${newSpeed}s`);

                sectionData.animationSpeed = newSpeed;
                const { textElement, section } = sectionData;

                // Force restart animation with new speed
                textElement.style.animation = 'none';
                textElement.offsetHeight; // Trigger reflow

                const isDesktop = window.innerWidth >= 1024;
                const isRTL = document.documentElement.dir === 'rtl';
                let animationName = isDesktop ? 'moveTextDesktop' : 'moveTextMobile';
                if (isRTL) {
                    animationName = 'moveTextRTL';
                }

                textElement.style.animation = `${animationName} ${newSpeed}s linear infinite`;
                section.style.setProperty('--animation-speed', `${newSpeed}s`);
                textElement.setAttribute('data-speed', newSpeed);
            }
        }

        updateColor(sectionId, newColor) {
            const sectionData = this.sections.find(s => s.id === sectionId);
            if (sectionData) {
                console.log(`Updating color for section ${sectionId} to ${newColor}`);

                sectionData.backgroundColor = newColor;
                const { section } = sectionData;

                // Apply new background color
                this.applyBackgroundColor(section, newColor);
                section.style.setProperty('--section-color', newColor);
                section.setAttribute('data-color', newColor);
            }
        }

        // Method to refresh all sections (useful for debugging)
        refreshAllSections() {
            console.log('Refreshing all moving text sections...');
            this.sections.forEach(sectionData => {
                this.setupSection(sectionData);
            });
        }

        destroy() {
            if (this.intersectionObserver) {
                this.intersectionObserver.disconnect();
            }
            
            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
            }
            
            clearTimeout(this.resizeTimeout);
            this.sections = [];
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.gamingMovingText = new GamingMovingText();
        });
    } else {
        window.gamingMovingText = new GamingMovingText();
    }

    // Cleanup on page unload to prevent memory leaks
    window.addEventListener('beforeunload', () => {
        if (window.gamingMovingText) {
            window.gamingMovingText.destroy();
        }
    });

    // Export for module systems
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = GamingMovingText;
    }

})();
