/**
 * Enhanced magnifier glass for images zooming with improved performance and UX.
 *
 * @param imgID the id of the image to be zoomed
 * @param zoom the zoom strength (default: 2.5)
 * @returns void
 */
export function zoom(imgID, zoom = 2.5) {
	// Validation
	if (!imgID) return;

	const img = document.getElementById(imgID);
	if (!img || !img.complete) return;

	// Check if magnifier already exists for this image
	const existingGlass = img.parentElement.querySelector('.img-magnifier-glass');
	if (existingGlass) return;

	// Create magnifier glass with enhanced styling and performance optimization
	const glass = document.createElement('DIV');
	glass.setAttribute('class', 'img-magnifier-glass');
	glass.setAttribute('data-img-id', imgID);
	glass.style.willChange = 'transform, opacity';
	glass.style.transform = 'translate3d(0, 0, 0)';
	glass.style.contain = 'layout style paint';

	// Insert magnifier glass
	img.parentElement.insertBefore(glass, img);

	// Wait for image to be fully loaded
	if (!img.complete) {
		img.addEventListener('load', () => initializeMagnifier());
	} else {
		initializeMagnifier();
	}

	function initializeMagnifier() {
		// Get actual image dimensions
		const imgRect = img.getBoundingClientRect();
		const naturalWidth = img.naturalWidth || img.width;
		const naturalHeight = img.naturalHeight || img.height;

		// Set background properties for the magnifier glass
		glass.style.backgroundImage = `url('${img.src}')`;
		glass.style.backgroundRepeat = 'no-repeat';
		glass.style.backgroundSize = `${naturalWidth * zoom}px ${naturalHeight * zoom}px`;

		const borderWidth = 3;
		const glassWidth = glass.offsetWidth / 2;
		const glassHeight = glass.offsetHeight / 2;

		// Enhanced event listeners with better performance
		const events = ['mousemove', 'touchmove'];
		events.forEach(event => {
			glass.addEventListener(event, moveMagnifier, { passive: false });
			img.addEventListener(event, moveMagnifier, { passive: false });
		});

		// Mouse leave events to hide magnifier
		img.addEventListener('mouseleave', () => {
			glass.style.opacity = '0';
			glass.style.transform = 'scale(0.8)';
		});

		img.addEventListener('mouseenter', () => {
			glass.style.opacity = '1';
			glass.style.transform = 'scale(1)';
		});

		function moveMagnifier(e) {
			e.preventDefault();

			const pos = getCursorPos(e);
			let x = pos.x;
			let y = pos.y;

			// Boundary constraints with smoother calculations
			const maxX = imgRect.width - glassWidth / zoom;
			const maxY = imgRect.height - glassHeight / zoom;
			const minX = glassWidth / zoom;
			const minY = glassHeight / zoom;

			x = Math.max(minX, Math.min(x, maxX));
			y = Math.max(minY, Math.min(y, maxY));

			// Smooth positioning with requestAnimationFrame for better performance
			requestAnimationFrame(() => {
				glass.style.left = `${x - glassWidth}px`;
				glass.style.top = `${y - glassHeight}px`;

				// Calculate background position with higher precision
				const bgX = -(x * zoom - glassWidth + borderWidth);
				const bgY = -(y * zoom - glassHeight + borderWidth);
				glass.style.backgroundPosition = `${bgX}px ${bgY}px`;
			});
		}

		function getCursorPos(e) {
			const rect = img.getBoundingClientRect();
			const clientX = e.clientX || (e.touches && e.touches[0] && e.touches[0].clientX);
			const clientY = e.clientY || (e.touches && e.touches[0] && e.touches[0].clientY);

			return {
				x: clientX - rect.left,
				y: clientY - rect.top
			};
		}
	}
}

/**
 * Clean up all magnifier glasses
 */
export function cleanupZoom() {
	const glasses = document.querySelectorAll('.img-magnifier-glass');
	glasses.forEach(glass => {
		// Remove event listeners before removing element
		glass.remove();
	});
}
