# WahgBanner Component - دليل الاستخدام

## 🎮 نظرة عامة
كومبوننت بنر عصري مصمم خصيصاً للجيمنج مع تأثيرات light sweep بألوان الأزرق والأحمر والبنفسجي.

## ✨ المميزات
- **تأثير Light Sweep:** ألوان جيمنج متدرجة (أزرق → أحمر → بنفسجي)
- **تأثير Glow:** إضاءة خفيفة عند الhover
- **Border Glow:** حدود مضيئة باللون الأزرق
- **نظام الروابط المتقدم:** دعم variable-list format
- **تصميم Responsive:** يتكيف مع جميع الشاشات
- **دعم Accessibility:** ARIA labels وkeyboard navigation

## 🔧 الإعدادات المتاحة

### 1. صورة البنر
- **النوع:** Image
- **المقاس المناسب:** 1200×400 بكسل
- **مطلوب:** نعم

### 2. رابط البنر
- **النوع:** Variable List
- **الخيارات المتاحة:**
  - منتج
  - تصنيف
  - ماركة تجارية
  - صفحة تعريفية
  - مقالة
  - تصنيف ضمن المدونة
  - التخفيضات
  - الماركات التجارية
  - المدونة
  - رابط خارجي
- **مطلوب:** لا (اختياري)

### 3. نص بديل للصورة
- **النوع:** Text
- **الغرض:** تحسين SEO وAccessibility
- **مطلوب:** لا (اختياري)

## 🎨 التأثيرات البصرية

### ألوان الجيمنج
```css
--wahg-banner-gaming-blue: #00d4ff;   /* أزرق مشرق */
--wahg-banner-gaming-red: #ff0080;    /* وردي مشرق */
--wahg-banner-gaming-purple: #8b5cf6; /* بنفسجي عصري */
```

### تأثير Light Sweep
- **المدة:** 0.8 ثانية
- **الاتجاه:** من اليسار إلى اليمين
- **الألوان:** تدرج من الأزرق إلى الأحمر إلى البنفسجي
- **الشفافية:** 0.4 للوضوح المثالي

### تأثير Glow
- **النوع:** Box-shadow متعدد الطبقات
- **الألوان:** أزرق وأحمر
- **التفعيل:** عند الhover فقط

## 📱 التصميم المتجاوب

### Desktop (أكبر من 768px)
- **الارتفاع الأدنى:** 200px
- **التأثيرات:** كاملة (sweep + glow + transform)
- **Border radius:** 12px

### Tablet (768px وأقل)
- **الارتفاع الأدنى:** 150px
- **التأثيرات:** مبسطة (بدون transform)
- **Border radius:** 8px

### Mobile (480px وأقل)
- **الارتفاع الأدنى:** 120px
- **التأثيرات:** أساسية فقط
- **Padding:** مقلل

## 🌙 دعم الأوضاع المختلفة

### Dark Mode
- **تأثيرات أقوى:** ألوان أكثر إشراقاً
- **ظلال محسنة:** للظهور في الخلفية المظلمة

### High Contrast Mode
- **حدود واضحة:** للمستخدمين ذوي الاحتياجات الخاصة
- **ألوان متباينة:** للوضوح الأمثل

### Reduced Motion
- **بدون حركة:** للمستخدمين الذين يفضلون تقليل الحركة
- **تأثيرات ثابتة:** بدون animations

## 🔗 كيفية عمل نظام الروابط

### في Twig Template
```twig
{# استخراج الرابط من variable-list format #}
{% set banner_url = null %}
{% if component.banner_url is defined and component.banner_url %}
    {% if component.banner_url.url is defined %}
        {% set banner_url = component.banner_url.url %}
    {% elseif component.banner_url is iterable and component.banner_url|length > 0 %}
        {% set banner_url = component.banner_url[0].url|default(null) %}
    {% endif %}
{% endif %}
```

### أمثلة على البيانات
```json
{
  "banner_url": {
    "type": "products",
    "url": "/products/gaming-laptop",
    "title": "لابتوب جيمنج"
  }
}
```

## 🚀 الأداء والتحسينات

### Lazy Loading
- **تحميل تدريجي:** للصور
- **Intersection Observer:** لتحسين الأداء

### Error Handling
- **Placeholder:** في حالة فشل تحميل الصورة
- **Fallback:** عرض رسالة خطأ مناسبة

### Analytics
- **تتبع النقرات:** مع Google Analytics
- **تتبع Salla:** إذا كان متاحاً

## 📋 نصائح للاستخدام

1. **استخدم صور عالية الجودة:** للحصول على أفضل تأثير
2. **اختبر على أجهزة مختلفة:** للتأكد من التجاوب
3. **استخدم نص بديل وصفي:** لتحسين SEO
4. **اختر روابط مناسبة:** لتحسين تجربة المستخدم

## 🐛 استكشاف الأخطاء

### المشاكل الشائعة
1. **عدم ظهور القائمة:** تأكد من صحة بنية sources في twilight.json
2. **عدم عمل التأثيرات:** تأكد من تحميل ملف CSS
3. **مشاكل الروابط:** تحقق من بنية variable-list

### الحلول
1. **تحقق من Console:** للأخطاء في JavaScript
2. **تحقق من Network:** لتحميل الملفات
3. **تحقق من HTML:** للبنية الصحيحة
