/**
 * Enhanced Wahg Banner Component Styles
 * Comprehensive customizable banner with full-width support and advanced effects
 */

/* Base banner component */
.s-block--wahg-banner {
    position: relative;
    contain: layout style paint;
    isolation: isolate;
    
    /* Default margins */
    margin: 20px auto;
    
    /* Performance optimizations */
    will-change: auto;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Full-width container - Simple and effective approach */
.s-block--wahg-banner.s-block--full-width {
    width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    max-width: none !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Alternative full-width approach using transform */
.s-block--wahg-banner[data-full-width="true"] {
    width: 100vw !important;
    transform: translateX(calc(-50vw + 50%)) !important;
    max-width: none !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Force full-width for any banner with full-width class */
.wahg-banner-wrapper .s-block--full-width,
.s-block--wahg-banner.s-block--full-width,
[data-full-width="true"] {
    width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    max-width: none !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
}

/* Banner wrapper */
.wahg-banner-wrapper {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: var(--border-radius, 12px);
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Base banner element */
.wahg-banner {
    position: relative;
    display: block;
    width: 100%;
    height: var(--banner-height, 400px);
    overflow: hidden;
    border-radius: var(--border-radius, 12px);
    transition: all 0.3s ease;
    will-change: transform, filter;
    transform: translateZ(0);
    backface-visibility: hidden;
    
    /* Default shadow */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Banner image */
.wahg-banner__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Shadow settings - Enhanced */
.wahg-banner {
    /* Default shadow */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Custom shadow when enabled */
[data-enable-shadow="true"] .wahg-banner {
    box-shadow: 0 4px var(--shadow-intensity, 10px) var(--shadow-color, rgba(0, 0, 0, 0.15)) !important;
}

/* Enhanced shadow with color support */
.wahg-banner[data-shadow-enabled="true"] {
    box-shadow: 0 4px var(--shadow-intensity, 10px) var(--shadow-color, rgba(0, 0, 0, 0.15)) !important;
}

/* No shadow when disabled */
[data-enable-shadow="false"] .wahg-banner,
.wahg-banner[data-shadow-enabled="false"] {
    box-shadow: none !important;
}

/* Force shadow application with inline styles */
.wahg-banner.shadow-enabled {
    box-shadow: 0 4px var(--shadow-intensity, 10px) var(--shadow-color, rgba(0, 0, 0, 0.15)) !important;
}

.wahg-banner.shadow-disabled {
    box-shadow: none !important;
}

/* Border settings */
[data-enable-border="true"] .wahg-banner {
    border: 2px solid var(--border-color, #e5e7eb) !important;
}

[data-enable-border="false"] .wahg-banner {
    border: none !important;
}

/* Hover effects overlay */
.wahg-banner--hover-enabled::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--hover-overlay-color, #000000);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
    pointer-events: none;
    will-change: opacity;
    transform: translateZ(0);
}

.wahg-banner--hover-enabled:hover::before {
    opacity: var(--hover-overlay-opacity, 0.2);
}

/* Animation Types */

/* Fade animation */
.animation-type-fade.wahg-banner--hover-enabled:hover {
    filter: brightness(0.8);
}

/* Zoom animation */
.animation-type-zoom.wahg-banner--hover-enabled:hover .wahg-banner__image {
    transform: scale(1.05);
}

/* Slide animation */
.animation-type-slide.wahg-banner--hover-enabled:hover .wahg-banner__image {
    transform: translateX(5px);
}

/* No animation */
.animation-type-none.wahg-banner--hover-enabled:hover .wahg-banner__image {
    transform: none;
}

/* Animation Durations */

/* Slow duration */
.animation-duration-slow .wahg-banner,
.animation-duration-slow .wahg-banner__image,
.animation-duration-slow .wahg-banner--hover-enabled::before {
    transition-duration: 0.6s;
}

/* Normal duration (default) */
.animation-duration-normal .wahg-banner,
.animation-duration-normal .wahg-banner__image,
.animation-duration-normal .wahg-banner--hover-enabled::before {
    transition-duration: 0.3s;
}

/* Fast duration */
.animation-duration-fast .wahg-banner,
.animation-duration-fast .wahg-banner__image,
.animation-duration-fast .wahg-banner--hover-enabled::before {
    transition-duration: 0.15s;
}

/* No link styling */
.wahg-banner--no-link {
    cursor: default;
}

/* Mobile responsive settings */
@media (max-width: 768px) {
    .wahg-banner {
        height: var(--mobile-height, 250px);
    }
    
    /* Hide on mobile when enabled */
    .wahg-banner--hide-mobile {
        display: none !important;
    }
    
    /* Adjust full-width margins for mobile */
    .s-block--wahg-banner.s-block--full-width {
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
    }
    
    /* Reduce border radius on mobile */
    .wahg-banner-wrapper,
    .wahg-banner {
        border-radius: calc(var(--border-radius, 12px) * 0.75);
    }
    
    /* Reduce shadow intensity on mobile */
    [data-enable-shadow="true"] .wahg-banner,
    .wahg-banner.shadow-enabled {
        box-shadow: 0 2px calc(var(--shadow-intensity, 10px) * 0.5) var(--shadow-color, rgba(0, 0, 0, 0.1)) !important;
    }
}

@media (max-width: 480px) {
    .wahg-banner {
        height: calc(var(--mobile-height, 250px) * 0.9);
    }
    
    /* Further reduce border radius on small mobile */
    .wahg-banner-wrapper,
    .wahg-banner {
        border-radius: calc(var(--border-radius, 12px) * 0.5);
    }
    
    /* Minimal shadow on small screens */
    [data-enable-shadow="true"] .wahg-banner,
    .wahg-banner.shadow-enabled {
        box-shadow: 0 1px calc(var(--shadow-intensity, 10px) * 0.3) var(--shadow-color, rgba(0, 0, 0, 0.1)) !important;
    }
}

/* Tablet responsive */
@media (min-width: 769px) and (max-width: 1024px) {
    .wahg-banner {
        height: calc(var(--banner-height, 400px) * 0.85);
    }
}

/* Large screens */
@media (min-width: 1200px) {
    .s-block--wahg-banner.s-block--full-width {
        /* Ensure proper full-width on large screens */
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
    }
}

/* RTL support */
[dir="rtl"] .animation-type-slide.wahg-banner--hover-enabled:hover .wahg-banner__image {
    transform: translateX(-5px);
}

/* Accessibility and performance */
@media (prefers-reduced-motion: reduce) {
    .wahg-banner,
    .wahg-banner__image,
    .wahg-banner--hover-enabled::before {
        transition: none !important;
        animation: none !important;
    }
    
    .animation-type-zoom.wahg-banner--hover-enabled:hover .wahg-banner__image,
    .animation-type-slide.wahg-banner--hover-enabled:hover .wahg-banner__image {
        transform: none !important;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .wahg-banner {
        transform: translateZ(0) scale(1);
    }
    
    .wahg-banner__image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .wahg-banner {
        filter: brightness(0.95);
    }
    
    [data-enable-shadow="true"] .wahg-banner {
        box-shadow: 0 4px var(--shadow-intensity, 10px) rgba(255, 255, 255, 0.1) !important;
    }
}

/* Battery saving mode */
@media (prefers-reduced-data: reduce) {
    .wahg-banner--hover-enabled::before {
        display: none;
    }
    
    .wahg-banner,
    .wahg-banner__image {
        transition-duration: 0.1s;
    }
}

/* Focus states for accessibility */
.wahg-banner:focus {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

.wahg-banner:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* Loading states */
.wahg-banner__image[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wahg-banner__image[loading="lazy"].loaded {
    opacity: 1;
}

/* Error states */
.wahg-banner__image:not([src]),
.wahg-banner__image[src=""] {
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wahg-banner__image:not([src])::before,
.wahg-banner__image[src=""]::before {
    content: "صورة غير متوفرة";
    color: #6b7280;
    font-size: 14px;
}

/* Print styles */
@media print {
    .wahg-banner--hover-enabled::before {
        display: none !important;
    }
    
    .wahg-banner {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}
