/**
 * Enhanced First Gallery Component - Theme Settings Integration
 * Handles customizable gallery with grid, masonry, and carousel layouts
 */
(function() {
    'use strict';

    // Default settings
    const DEFAULT_SETTINGS = {
        gallery_layout: 'grid',
        columns_count: 4,
        image_aspect_ratio: 'square',
        image_spacing: 15,
        enable_hover_effects: true,
        hover_overlay_color: '#000000',
        hover_overlay_opacity: 30,
        image_border_radius: 8,
        enable_image_shadows: true,
        enable_entrance_animations: true,
        animation_type: 'fadeIn',
        animation_delay: 'short',
        stagger_animations: true,
        mobile_columns: 2,
        tablet_columns: 3,
        hide_on_mobile: false,
        lazy_loading: true,
        image_optimization: true
    };

    let isInitialized = false;

    /**
     * Initialize first gallery components
     */
    function initFirstGalleryComponents() {
        if (isInitialized) return;

        const galleries = document.querySelectorAll('[data-component="first-gallery"]');
        if (galleries.length === 0) return;

        console.log('🎯 Initializing First Gallery Enhanced:', galleries.length, 'gallery(s) found');

        // Check browser support for enhanced features
        const supportsWillChange = CSS.supports('will-change', 'transform');
        const supportsBackfaceVisibility = CSS.supports('backface-visibility', 'hidden');
        const supportsMasonry = CSS.supports('grid-template-rows', 'masonry');

        // Setup intersection observer for animations and lazy loading
        const observerOptions = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };

        const galleryObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const gallery = entry.target;
                const galleryItems = gallery.querySelectorAll('.gallery-item');

                if (entry.isIntersecting) {
                    // Trigger entrance animations
                    if (gallery.dataset.enableEntranceAnimations === 'true') {
                        triggerEntranceAnimations(gallery, galleryItems);
                    }

                    // Handle lazy loading
                    const lazyImages = gallery.querySelectorAll('.enhanced-gallery-image[loading="lazy"]');
                    lazyImages.forEach(img => {
                        img.addEventListener('load', () => {
                            img.classList.add('loaded');
                        });
                    });

                    gallery.classList.add('gallery-visible');
                } else {
                    gallery.classList.remove('gallery-visible');
                }
            });
        }, observerOptions);

        // Process each gallery
        galleries.forEach(gallery => {
            applyThemeSettings(gallery);
            galleryObserver.observe(gallery);

            // Performance optimizations
            if (supportsWillChange) {
                const galleryItems = gallery.querySelectorAll('.gallery-item');
                galleryItems.forEach(item => {
                    item.style.willChange = 'transform, opacity';
                });
            }

            if (supportsBackfaceVisibility) {
                const allElements = gallery.querySelectorAll('*');
                allElements.forEach(el => {
                    el.style.backfaceVisibility = 'hidden';
                });
            }

            // Handle masonry layout fallback
            if (!supportsMasonry && gallery.dataset.galleryLayout === 'masonry') {
                initMasonryFallback(gallery);
            }

            // Handle carousel layout
            if (gallery.dataset.galleryLayout === 'carousel') {
                initCarouselFeatures(gallery);
            }
        });

        // Cleanup observer on page unload
        window.addEventListener('beforeunload', () => {
            galleryObserver.disconnect();
        });

        isInitialized = true;

        // Log initialization (development only)
        console.log('✅ Enhanced First Gallery initialized:', galleries.length, 'gallery(s) processed');
    }

    /**
     * Apply theme settings to a gallery element
     */
    function applyThemeSettings(gallery) {
        console.log('🔧 Applying theme settings to gallery element');
        
        const settings = getThemeSettings(gallery);
        
        console.log('📋 Final settings to apply:', settings);
        
        // Apply CSS custom properties for dynamic styling
        gallery.style.setProperty('--columns-count', settings.columns_count);
        gallery.style.setProperty('--image-spacing', `${settings.image_spacing}px`);
        gallery.style.setProperty('--hover-overlay-color', settings.hover_overlay_color);
        gallery.style.setProperty('--hover-overlay-opacity', settings.hover_overlay_opacity / 100);
        gallery.style.setProperty('--image-border-radius', `${settings.image_border_radius}px`);
        gallery.style.setProperty('--mobile-columns', settings.mobile_columns);
        gallery.style.setProperty('--tablet-columns', settings.tablet_columns);

        const galleryGrid = gallery.querySelector('.gallery-grid');
        const galleryItems = gallery.querySelectorAll('.gallery-item');

        if (galleryGrid) {
            // Clear existing layout classes
            galleryGrid.className = galleryGrid.className.replace(/aspect-ratio-\w+/g, '');
            
            // Apply aspect ratio class
            galleryGrid.classList.add(`aspect-ratio-${settings.image_aspect_ratio}`);
            
            console.log('🎨 Applied layout:', settings.gallery_layout, 'aspect ratio:', settings.image_aspect_ratio);
        }

        // Handle stagger animations
        if (settings.stagger_animations && settings.enable_entrance_animations) {
            galleryItems.forEach((item, index) => {
                const delay = getAnimationDelay(settings.animation_delay, index);
                item.style.setProperty('--animation-delay', `${delay}ms`);
                item.dataset.animationDelay = `${delay}ms`;
            });
        }

        // Handle mobile visibility
        if (settings.hide_on_mobile) {
            gallery.classList.add('hide-mobile');
        } else {
            gallery.classList.remove('hide-mobile');
        }

        console.log('✅ Settings applied successfully to gallery:', {
            layout: settings.gallery_layout,
            columns: settings.columns_count,
            aspect_ratio: settings.image_aspect_ratio,
            hover_effects: settings.enable_hover_effects,
            animations: settings.enable_entrance_animations,
            animation_type: settings.animation_type
        });
    }

    /**
     * Trigger entrance animations for gallery items
     */
    function triggerEntranceAnimations(gallery, galleryItems) {
        const settings = getThemeSettings(gallery);
        
        if (!settings.enable_entrance_animations) return;

        galleryItems.forEach((item, index) => {
            const delay = settings.stagger_animations ? 
                getAnimationDelay(settings.animation_delay, index) : 0;
            
            setTimeout(() => {
                item.classList.add('animate-in');
            }, delay);
        });

        console.log('🎬 Triggered entrance animations:', settings.animation_type);
    }

    /**
     * Get animation delay based on settings and index
     */
    function getAnimationDelay(delayType, index) {
        const baseDelays = {
            none: 0,
            short: 100,
            medium: 200,
            long: 300
        };

        return (baseDelays[delayType] || 100) * index;
    }

    /**
     * Initialize masonry layout fallback for browsers without CSS masonry support
     */
    function initMasonryFallback(gallery) {
        const galleryGrid = gallery.querySelector('.gallery-grid');
        if (!galleryGrid) return;

        // Simple masonry fallback using flexbox
        galleryGrid.style.display = 'flex';
        galleryGrid.style.flexWrap = 'wrap';
        galleryGrid.style.alignItems = 'flex-start';

        console.log('🧱 Initialized masonry fallback for gallery');
    }

    /**
     * Initialize carousel features
     */
    function initCarouselFeatures(gallery) {
        const galleryGrid = gallery.querySelector('.gallery-grid');
        if (!galleryGrid) return;

        // Add smooth scrolling
        galleryGrid.style.scrollBehavior = 'smooth';

        // Add touch/swipe support for mobile
        let isDown = false;
        let startX;
        let scrollLeft;

        galleryGrid.addEventListener('mousedown', (e) => {
            isDown = true;
            startX = e.pageX - galleryGrid.offsetLeft;
            scrollLeft = galleryGrid.scrollLeft;
        });

        galleryGrid.addEventListener('mouseleave', () => {
            isDown = false;
        });

        galleryGrid.addEventListener('mouseup', () => {
            isDown = false;
        });

        galleryGrid.addEventListener('mousemove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - galleryGrid.offsetLeft;
            const walk = (x - startX) * 2;
            galleryGrid.scrollLeft = scrollLeft - walk;
        });

        console.log('🎠 Initialized carousel features for gallery');
    }

    /**
     * Get theme settings from data attributes or defaults
     */
    function getThemeSettings(gallery) {
        const settings = { ...DEFAULT_SETTINGS };

        try {
            // Get settings from data attributes (primary method)
            if (gallery && gallery.dataset) {
                const dataset = gallery.dataset;
                
                // Read each setting from data attributes
                if (dataset.galleryLayout) settings.gallery_layout = dataset.galleryLayout;
                if (dataset.columnsCount) settings.columns_count = parseInt(dataset.columnsCount);
                if (dataset.imageAspectRatio) settings.image_aspect_ratio = dataset.imageAspectRatio;
                if (dataset.imageSpacing) settings.image_spacing = parseInt(dataset.imageSpacing);
                if (dataset.enableHoverEffects) settings.enable_hover_effects = dataset.enableHoverEffects === 'true';
                if (dataset.hoverOverlayColor) settings.hover_overlay_color = dataset.hoverOverlayColor;
                if (dataset.hoverOverlayOpacity) settings.hover_overlay_opacity = parseInt(dataset.hoverOverlayOpacity);
                if (dataset.imageBorderRadius) settings.image_border_radius = parseInt(dataset.imageBorderRadius);
                if (dataset.enableImageShadows) settings.enable_image_shadows = dataset.enableImageShadows === 'true';
                if (dataset.enableEntranceAnimations) settings.enable_entrance_animations = dataset.enableEntranceAnimations === 'true';
                if (dataset.animationType) settings.animation_type = dataset.animationType;
                if (dataset.animationDelay) settings.animation_delay = dataset.animationDelay;
                if (dataset.staggerAnimations) settings.stagger_animations = dataset.staggerAnimations === 'true';
                if (dataset.mobileColumns) settings.mobile_columns = parseInt(dataset.mobileColumns);
                if (dataset.tabletColumns) settings.tablet_columns = parseInt(dataset.tabletColumns);
                if (dataset.hideOnMobile) settings.hide_on_mobile = dataset.hideOnMobile === 'true';
                if (dataset.lazyLoading) settings.lazy_loading = dataset.lazyLoading === 'true';
                if (dataset.imageOptimization) settings.image_optimization = dataset.imageOptimization === 'true';
                
                console.log('📊 First Gallery settings loaded from data attributes:', settings);
            }
            else {
                console.log('📊 First Gallery settings: Using defaults');
            }
        } catch (error) {
            console.warn('⚠️ Could not load First Gallery theme settings:', error);
        }

        return settings;
    }

    /**
     * Refresh all first gallery components with new settings
     */
    function refreshFirstGalleryComponents() {
        const galleries = document.querySelectorAll('[data-component="first-gallery"]');
        galleries.forEach(gallery => {
            applyThemeSettings(gallery);
        });
        console.log('🔄 First Gallery components refreshed');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initFirstGalleryComponents);
    } else {
        initFirstGalleryComponents();
    }

    // Listen for theme changes
    document.addEventListener('salla:theme:updated', refreshFirstGalleryComponents);

    // Expose refresh function globally for theme customizer
    window.refreshFirstGalleryComponents = refreshFirstGalleryComponents;

    // Add body class to indicate component is loaded
    document.body.classList.add('first-gallery-enhanced');
    
    // Dispatch event to indicate component is loaded
    document.dispatchEvent(new CustomEvent('first-gallery-enhanced-loaded'));

})();
