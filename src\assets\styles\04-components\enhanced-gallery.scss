// Enhanced Product Gallery Styles
// Advanced styling for the improved product image gallery

.product-gallery-container {
  // Gallery loading states
  &.gallery-loading {
    .details-slider {
      opacity: 0.7;
      pointer-events: none;
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40px;
      height: 40px;
      margin: -20px 0 0 -20px;
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-top-color: var(--color-primary);
      border-radius: 50%;
      animation: gallery-spin 1s linear infinite;
      z-index: 100;
    }
  }

  // Enhanced focus styles for accessibility
  &:focus-within {
    .details-slider {
      box-shadow: 0 0 0 3px rgba(var(--color-primary), 0.3);
      border-radius: 12px;
    }
  }

  // Gaming theme enhancements
  .details-slider.enhanced-gallery {
    // Glow effect for gaming theme
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(45deg, 
        var(--gaming-accent-blue), 
        var(--gaming-accent-purple), 
        var(--gaming-accent-green),
        var(--gaming-accent-blue)
      );
      background-size: 400% 400%;
      border-radius: 14px;
      z-index: -1;
      opacity: 0;
      animation: gaming-gradient 3s ease infinite;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 0.3;
    }

    // Enhanced swiper navigation
    .swiper-button-next,
    .swiper-button-prev {
      @apply w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg;
      @apply text-gray-700 hover:text-primary hover:bg-white;
      @apply transition-all duration-300;
      
      &:hover {
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      &::after {
        @apply text-lg font-bold;
      }

      // Gaming theme colors
      @media (prefers-color-scheme: dark) {
        @apply bg-gray-800/90 text-white hover:bg-gray-700;
      }
    }

    // Enhanced pagination
    .swiper-pagination {
      @apply bottom-4;

      .swiper-pagination-bullet {
        @apply w-3 h-3 bg-white/70 hover:bg-white;
        @apply transition-all duration-300;

        &-active {
          @apply bg-primary scale-125;
          box-shadow: 0 0 10px rgba(var(--color-primary), 0.5);
        }
      }
    }
  }

  // Enhanced thumbnails
  .enhanced-thumbs {
    // Custom scrollbar for webkit browsers
    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      @apply bg-gray-100 rounded-full;
    }

    &::-webkit-scrollbar-thumb {
      @apply bg-gray-300 rounded-full;
      
      &:hover {
        @apply bg-gray-400;
      }
    }

    // Smooth scroll behavior
    scroll-behavior: smooth;

    .thumb-slide {
      // Enhanced hover effects
      .thumb-wrapper {
        @apply relative overflow-hidden;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transform: translateX(-100%);
          transition: transform 0.6s ease;
          z-index: 2;
        }

        &:hover::before {
          transform: translateX(100%);
        }

        &.active {
          @apply ring-2 ring-primary ring-offset-2;
          
          .thumb-active-indicator {
            @apply opacity-100;
          }
        }

        // Focus styles for keyboard navigation
        &:focus {
          @apply outline-none ring-2 ring-primary ring-offset-2;
        }
      }

      .thumb-image {
        @apply transition-all duration-300;
        
        &.lazy {
          @apply opacity-0 blur-sm;
        }

        &.loaded {
          @apply opacity-100 blur-0;
        }
      }
    }
  }

  // Enhanced indicators
  .model-indicator,
  .video-indicator {
    @apply font-medium tracking-wide;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: inherit;
      border-radius: inherit;
      opacity: 0.8;
      z-index: -1;
    }
  }

  .model-indicator {
    @apply bg-gradient-to-r from-blue-600 to-purple-600;
  }

  .video-indicator {
    @apply bg-gradient-to-r from-red-600 to-pink-600;
  }

  // Enhanced image counter
  .image-counter {
    @apply tracking-wider;
    font-family: 'Courier New', monospace;
    backdrop-filter: blur(10px);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: inherit;
      border-radius: inherit;
      opacity: 0.9;
      z-index: -1;
    }
  }
}

// Enhanced magnifier glass
.img-magnifier-glass {
  // Improved visual design
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(1px);
  
  // Enhanced border with gradient
  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, 
      rgba(255, 255, 255, 0.8), 
      rgba(255, 255, 255, 0.4), 
      rgba(255, 255, 255, 0.8)
    );
    border-radius: 50%;
    z-index: -1;
  }

  // Smooth entrance animation
  &.entering {
    animation: magnifier-enter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// Animations
@keyframes gallery-spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes gaming-gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes magnifier-enter {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .product-gallery-container {
    .thumb-wrapper.active {
      @apply ring-4 ring-black;
    }

    .model-indicator,
    .video-indicator,
    .image-counter {
      @apply bg-black text-white border-2 border-white;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .product-gallery-container {
    .details-slider.enhanced-gallery::before {
      animation: none;
    }

    .thumb-wrapper::before {
      transition: none;
    }

    .img-magnifier-glass {
      transition: opacity 0.1s ease;
    }
  }
}

// Print styles
@media print {
  .product-gallery-container {
    .enhanced-thumbs,
    .image-counter,
    .model-indicator,
    .video-indicator {
      display: none;
    }

    .details-slider {
      .swiper-slide:not(.swiper-slide-active) {
        display: none;
      }
    }
  }
}
