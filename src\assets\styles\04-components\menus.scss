
// Main Menu
.main-menu > li > a[href*="offer"]{
  @apply text-red-800
}

@media only screen and (min-width: 1024px) {
  .main-menu {
    @apply hidden lg:flex flex-wrap items-center justify-center mx-6 pt-0 pb-4;

    .fixed-pinned & {
      padding-top: 0;
      padding-bottom: 0;
    }

    li{
      > a{
        @apply flex justify-between items-center transition duration-300 p-3 text-sm hover:no-underline;
        color: var(--gaming-text-primary, #ffffff);
        position: relative;

        &:hover {
          color: var(--gaming-accent-blue, #00d4ff);
          text-shadow: 0 0 5px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3));
          transform: translateY(-1px);
        }

        // Gaming hover effect
        &::before {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 0;
          height: 2px;
          background: linear-gradient(90deg,
            var(--gaming-accent-blue, #00d4ff),
            var(--gaming-accent-purple, #8b5cf6));
          transition: all 0.3s ease;
          transform: translateX(-50%);
        }

        &:hover::before {
          width: 100%;
          box-shadow: 0 0 10px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3));
        }
      }

      &.root-level{
        @apply inline-block;

        > a{
          @apply font-bold pt-0 pb-8;
        }
      }
    }

    > .has-children:hover > a{
      color: var(--color-primary);
    }

    .has-children{
      li a:hover,
      .has-children:hover > a{
        color: var(--gaming-accent-blue, #00d4ff);
        background: rgba(0, 212, 255, 0.1);
        text-shadow: 0 0 5px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3));
      }

      // Sub-menu items styling
      .sub-menu {
        li a {
          color: var(--gaming-text-primary, #ffffff);
          padding: 12px 16px;
          transition: all 0.3s ease;
          border-bottom: 1px solid rgba(0, 212, 255, 0.1);

          &:hover {
            background: linear-gradient(90deg,
              rgba(0, 212, 255, 0.1) 0%,
              rgba(139, 92, 246, 0.1) 100%);
            color: var(--gaming-accent-blue, #00d4ff);
            text-shadow: 0 0 5px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3));
            transform: translate3d(5px, 0, 0);
            will-change: transform;
          }
        }
      }
    }

      > a:after{
        font-family: 'sallaicons';
        content: "\e970";
        @apply inline-block transition-transform duration-300 self-end mx-0.5 text-lg opacity-50 leading-4;
      }

      &.root-level {
        > a:after{
          content: "\e96e";
        }

        &:hover > a:after{
          opacity: 1;
          transform: scaleY(-1);
        }
      }
    }

    .sub-menu {
      @apply transition opacity-0 invisible absolute -translate-y-3 rounded-b-md;
      z-index: var(--z-submenu);
      background: linear-gradient(135deg,
        var(--gaming-bg-card, #16213e) 0%,
        var(--gaming-bg-secondary, #1a1a2e) 100%);
      border: 2px solid var(--gaming-accent-blue, #00d4ff);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5),
                  0 0 20px var(--gaming-glow-blue, rgba(0, 212, 255, 0.3));
      backdrop-filter: blur(10px);

      .sub-menu {
        top: -1px;
        right: 100%;

        [dir="ltr"] & {
          left: 100%;
          right: auto;
        }
      }

      .s-product-card-entry{
        @apply border border-gray-100;
      }

      .btn {
        padding: 8px 10px 10px;
      }

      li.mega-menu .container {
        ul{
          @apply p-0 m-0 border-none;
        }
  
        > div{
          @apply hidden;
        }
      }
    }

    .change-menu-dir .sub-menu  .sub-menu {
      @apply rtl:left-full rtl:right-auto ltr:right-full ltr:left-auto;
    }

    li:hover {
      > .sub-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }
    }
  }

  .main-menu .sub-menu ul > li:not(:first-child) > .sub-menu{
    border-top-right-radius: 0.375rem;
    border-top-left-radius: 0.375rem;
    border: none;
  }

// Mobile Slide Menu
@media only screen and (max-width: 1024px) {
  .filters-opened{
    .close-filters{
      display: block !important;
    }
  } 
  .menu-opened{
    .btn--close-sm.close-mobile-menu{
      display: block !important;
    }
  }
  .mobile-menu {
      display: none;
      @apply lg:hidden overflow-hidden;
  }
  .mm-ocd__content{
    overflow-y: auto;
  }
  .mm-ocd-opened {
    .mobile-menu {
      display: block;
    }

    @media (max-width: 480px) {
      .btn--close-sm {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
        z-index: var(--z-critical);
      }
    }
  }

  .mm-spn.mm-spn--light {
    color: #000;
    background: #ffffff;
  }

  .mm-spn {
    ul.main-menu li:before{
      @apply w-2 h-2; 
    }

    &.mm-spn--navbar{
      &:after {
        @apply transition-all duration-300 rtl:pr-12 ltr:pl-12 text-start opacity-90 font-bold;
      }

      &.mm-spn--main{
        &:after{
          @apply rtl:pr-3 ltr:pl-3;
        }
      }
    }

    &.mm-spn--navbar:before {
      [dir="rtl"] & {
        transform: rotate(135deg) translateY(77%);
        right: var(--mm-spn-item-indent);
        left: auto;
      }
    }

    li {
      a,
      > span {
		padding: 18px;
		@apply flex items-center gap-4;
	  }

	  img{
		@apply w-12 h-12 object-cover bg-[#f5f7f9] pointer-events-none;
	  }

      a span{
        padding: 0;
      }

      &:before {
        [dir="rtl"] & {
          width: 6px;
          height: 6px;
          top: 50%;
          left: 25px;
          left: calc(var(--mm-spn-item-height) / 2);
          right: auto;
          border-bottom: 1px solid;
          border-left: 1px solid;
          border-right: none;
          border-top: none;
        }
      }

      &:after {
        width: 100%;
        border-color: var(--infinte-color);
      }
    }
  }
}