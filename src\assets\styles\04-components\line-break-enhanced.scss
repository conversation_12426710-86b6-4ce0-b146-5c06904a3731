/**
 * Enhanced Line Break Component Styles
 * Customizable divider with gaming theme support
 */

/* Base line break component */
.gaming-advanced-divider {
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
    contain: layout style paint;
    isolation: isolate;
    
    /* Default margins - will be overridden by theme settings */
    margin: 30px 0;
    
    /* Performance optimizations */
    will-change: auto;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Base divider line */
.divider-line {
    width: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 2px;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    
    /* Default height - will be overridden by theme settings */
    height: 2px;
}

/* Gaming style (default) */
.divider-line.style-gaming {
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(29, 233, 182, 0.3) 20%,
        rgba(29, 233, 182, 0.8) 50%,
        rgba(29, 233, 182, 0.3) 80%,
        transparent 100%) !important;
    border: none !important;
}

/* Solid style */
.divider-line.style-solid {
    background: var(--line-color, #1de9b6) !important;
    border: none !important;
    height: var(--line-thickness, 2px) !important;
}

/* Dashed style */
.divider-line.style-dashed {
    background: transparent !important;
    border-top: var(--line-thickness, 2px) dashed var(--line-color, #1de9b6) !important;
    border-bottom: none !important;
    height: 0 !important;
}

/* Dotted style */
.divider-line.style-dotted {
    background: transparent !important;
    border-top: var(--line-thickness, 2px) dotted var(--line-color, #1de9b6) !important;
    border-bottom: none !important;
    height: 0 !important;
}

/* Double style */
.divider-line.style-double {
    background: transparent !important;
    border-top: calc(var(--line-thickness, 2px) / 2) solid var(--line-color, #1de9b6) !important;
    border-bottom: calc(var(--line-thickness, 2px) / 2) solid var(--line-color, #1de9b6) !important;
    height: calc(var(--line-thickness, 2px) * 2) !important;
}

/* Glow effect (only for gaming style) */
.divider-line.style-gaming::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(29, 233, 182, 0.1) 30%,
        rgba(29, 233, 182, 0.5) 50%,
        rgba(29, 233, 182, 0.1) 70%,
        transparent 100%);
    box-shadow: 0 0 15px rgba(29, 233, 182, 0.6);
    filter: blur(1px);
    will-change: filter;
    transform: translateZ(0);
}

/* Shine effect */
.divider-line.enable-shine::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.8),
        transparent);
    will-change: transform;
    transform: translateZ(0);
}

/* Glow element */
.divider-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: clamp(60px, 15vw, 120px);
    height: 100%;
    background: radial-gradient(ellipse,
        rgba(29, 233, 182, 0.8) 0%,
        rgba(29, 233, 182, 0.4) 40%,
        transparent 70%);
    filter: blur(6px);
    will-change: transform, opacity, filter;
    transform: translate(-50%, -50%) translateZ(0);
}

/* Particles element */
.divider-particles {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 1px;
    background-image:
        radial-gradient(circle at 20%, rgba(29, 233, 182, 0.3) 1px, transparent 1px),
        radial-gradient(circle at 40%, rgba(76, 201, 240, 0.2) 1px, transparent 1px),
        radial-gradient(circle at 60%, rgba(29, 233, 182, 0.3) 1px, transparent 1px),
        radial-gradient(circle at 80%, rgba(76, 201, 240, 0.2) 1px, transparent 1px);
    background-size: 50px 1px, 75px 1px, 60px 1px, 40px 1px;
    opacity: 0.6;
    will-change: background-position;
}

/* Animation classes for different speeds */
.animation-very-slow .divider-line.enable-shine::after {
    animation: shine 6s infinite ease-in-out;
}

.animation-slow .divider-line.enable-shine::after {
    animation: shine 4s infinite ease-in-out;
}

.animation-normal .divider-line.enable-shine::after {
    animation: shine 3s infinite ease-in-out;
}

.animation-fast .divider-line.enable-shine::after {
    animation: shine 2s infinite ease-in-out;
}

.animation-very-fast .divider-line.enable-shine::after {
    animation: shine 1s infinite ease-in-out;
}

/* Glow animations */
.animation-very-slow .divider-glow {
    animation: glow-pulse 8s ease-in-out infinite;
}

.animation-slow .divider-glow {
    animation: glow-pulse 6s ease-in-out infinite;
}

.animation-normal .divider-glow {
    animation: glow-pulse 4s ease-in-out infinite;
}

.animation-fast .divider-glow {
    animation: glow-pulse 3s ease-in-out infinite;
}

.animation-very-fast .divider-glow {
    animation: glow-pulse 2s ease-in-out infinite;
}

/* Particles animations */
.animation-very-slow .divider-particles {
    animation: particles-flow 12s linear infinite;
}

.animation-slow .divider-particles {
    animation: particles-flow 8s linear infinite;
}

.animation-normal .divider-particles {
    animation: particles-flow 6s linear infinite;
}

.animation-fast .divider-particles {
    animation: particles-flow 4s linear infinite;
}

.animation-very-fast .divider-particles {
    animation: particles-flow 2s linear infinite;
}

/* Keyframe animations */
@keyframes shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(300%);
    }
}

@keyframes glow-pulse {
    0%, 100% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

@keyframes particles-flow {
    0% {
        background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px;
    }
    100% {
        background-position: 200px 0px, 300px 0px, 240px 0px, 160px 0px;
    }
}

/* RTL support */
[dir="rtl"] .divider-line.style-gaming {
    background: linear-gradient(270deg,
        transparent 0%,
        rgba(29, 233, 182, 0.3) 20%,
        rgba(29, 233, 182, 0.8) 50%,
        rgba(29, 233, 182, 0.3) 80%,
        transparent 100%);
}

[dir="rtl"] .divider-line.style-gaming::before {
    background: linear-gradient(270deg,
        transparent 0%,
        rgba(29, 233, 182, 0.1) 30%,
        rgba(29, 233, 182, 0.5) 50%,
        rgba(29, 233, 182, 0.1) 70%,
        transparent 100%);
}

[dir="rtl"] .divider-line.enable-shine::after {
    animation: shine-rtl 3s infinite ease-in-out;
}

@keyframes shine-rtl {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-300%);
    }
}

/* Responsive design */
@media (max-width: 1024px) {
    .gaming-advanced-divider {
        margin: clamp(15px, 4vw, 30px) 0;
    }

    .divider-particles {
        background-size: 40px 1px, 60px 1px, 50px 1px, 35px 1px;
    }
}

@media (max-width: 768px) {
    .gaming-advanced-divider {
        margin: clamp(12px, 3vw, 25px) 0;
    }

    .divider-line {
        height: 1.8px;
    }

    .divider-glow {
        height: 1.8px;
        filter: blur(4px);
    }

    .divider-particles {
        background-size: 35px 1px, 50px 1px, 40px 1px, 30px 1px;
        opacity: 0.5;
    }
}

@media (max-width: 480px) {
    .gaming-advanced-divider {
        margin: clamp(10px, 2.5vw, 20px) 0;
    }

    .divider-line {
        height: 1.5px;
    }

    .divider-glow {
        height: 1.5px;
        filter: blur(3px);
        box-shadow:
            0 0 10px rgba(29, 233, 182, 0.6),
            0 0 20px rgba(29, 233, 182, 0.4);
    }

    .divider-particles {
        background-size: 25px 1px, 35px 1px, 30px 1px, 20px 1px;
        opacity: 0.4;
    }
}

@media (max-width: 320px) {
    .gaming-advanced-divider {
        margin: 15px 0;
    }

    .divider-line {
        height: 1px;
    }

    .divider-glow {
        height: 1px;
        filter: blur(2px);
    }

    .divider-particles {
        display: none;
    }
}

/* Accessibility and performance */
@media (prefers-reduced-motion: reduce) {
    .divider-line.enable-shine::after,
    .divider-glow,
    .divider-particles {
        animation: none;
    }

    .divider-glow {
        opacity: 0.6;
        width: clamp(40px, 10vw, 80px);
    }

    .divider-particles {
        display: none;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .divider-line {
        transform: translateZ(0) scale(1);
    }

    .divider-glow {
        filter: blur(4px);
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .gaming-advanced-divider {
        filter: brightness(1.1);
    }
}

/* Battery saving mode */
@media (prefers-reduced-data: reduce) {
    .divider-particles {
        display: none;
    }

    .divider-line.enable-shine::after {
        animation-duration: 6s;
    }

    .divider-glow {
        animation-duration: 8s;
    }
}
