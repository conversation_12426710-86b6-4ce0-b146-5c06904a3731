/* WhatsApp Gaming Gallery Component - Optimized Performance */
.whatsapp-special-gallery-section {
    position: relative;
    background: linear-gradient(135deg, rgba(10, 10, 15, 0.95), rgba(18, 140, 126, 0.1));
    padding: 4rem 0;
    overflow: hidden;
    contain: layout style;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 20% 50%, rgba(37, 211, 102, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(18, 140, 126, 0.1) 0%, transparent 50%);
        z-index: 1;
        pointer-events: none;
    }

    @media (max-width: 768px) {
        padding: 2rem 0;
    }
}

/* Header Styles */
.whatsapp-gallery-header {
    position: relative;
    z-index: 2;
    text-align: center;
    margin-bottom: 3rem;
    padding: 0 1rem;

    @media (max-width: 768px) {
        margin-bottom: 2rem;
    }
}

.whatsapp-gallery-title {
    text-shadow: 0 0 15px rgba(37, 211, 102, 0.5);
    font-weight: 700;
    letter-spacing: 1px;
    position: relative;
    font-size: 2.25rem;
    margin-bottom: 1.5rem;

    @media (min-width: 768px) {
        font-size: 3rem;
        margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
        font-size: 1.875rem;
        margin-bottom: 1rem;
    }
}

.whatsapp-gallery-description {
    color: #128C7E;
    font-weight: 500;
    text-shadow: 0 0 8px rgba(18, 140, 126, 0.3);
    font-size: 1.125rem;
    max-width: 32rem;
    margin: 0 auto;

    @media (max-width: 768px) {
        font-size: 1rem;
    }
}

/* Gallery Container */
.whatsapp-gallery-container {
    position: relative;
    z-index: 2;
}

/* Gallery Item Styles */
.whatsapp-gallery-item {
    position: relative;
    width: 100%;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    margin-bottom: 2rem;
    z-index: 2;
    min-height: 400px;
    will-change: transform;
    backface-visibility: hidden;
    opacity: 0;
    transform: translateY(30px);

    &.animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    &:hover {
        transform: translateY(-5px);
    }

    // Gaming-style glow effect
    &::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(45deg, #25D366, #128C7E, #25D366);
        border-radius: 23px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover::before {
        opacity: 0.4;
        animation: whatsapp-gallery-glow 2s infinite alternate;
    }

    .flex {
        height: 100%;
        min-height: 400px;

        @media (max-width: 768px) {
            flex-direction: column !important;
        }
    }
}

/* Image Wrapper Styles */
.whatsapp-gallery-image-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 20px 20px 0 0;
    width: 50%;

    @media (max-width: 768px) {
        width: 100% !important;
        height: 250px;
        border-radius: 20px 20px 0 0;
    }

    @media (min-width: 769px) {
        height: auto;
    }
}

.whatsapp-gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
    backface-visibility: hidden;

    &.lazy {
        opacity: 0;
        transition: opacity 0.5s ease;
    }

    &.loaded {
        opacity: 1;
    }
}

.whatsapp-gallery-item:hover .whatsapp-gallery-image {
    transform: scale(1.05);
}

/* Overlay Effects */
.whatsapp-gallery-overlay {
    position: absolute;
    inset: 0;
    background: rgba(37, 211, 102, 0.1);
    transition: all 0.3s ease;
    pointer-events: none;
}

.whatsapp-gallery-item:hover .whatsapp-gallery-overlay {
    background: rgba(37, 211, 102, 0.2);
}

.whatsapp-gallery-gradient {
    position: absolute;
    inset: 0;
    pointer-events: none;

    &.gradient-left {
        background: linear-gradient(to left, rgba(37, 211, 102, 0.3) 0%, transparent 70%);
    }

    &.gradient-right {
        background: linear-gradient(to right, rgba(37, 211, 102, 0.3) 0%, transparent 70%);
    }
}

/* Content Wrapper Styles */
.whatsapp-gallery-content-wrapper {
    border-radius: 0 0 25px 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(37, 211, 102, 0.15);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    width: 50%;

    @media (max-width: 768px) {
        width: 100% !important;
        border-radius: 0 0 20px 20px;
    }
}

.whatsapp-gallery-item:hover .whatsapp-gallery-content-wrapper {
    box-shadow: 0 12px 40px rgba(37, 211, 102, 0.25);
    transform: translateY(-2px);
}

.whatsapp-gallery-bubble-bg {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(37, 211, 102, 0.08) 50%,
        rgba(18, 140, 126, 0.05) 100%);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(37, 211, 102, 0.3);
    border-top: 1px solid rgba(37, 211, 102, 0.2);
    border-radius: 0 0 25px 25px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(37, 211, 102, 0.4) 50%,
            transparent 100%);
    }

    &::after {
        content: '';
        position: absolute;
        bottom: 10px;
        right: 15px;
        width: 8px;
        height: 8px;
        background: #25D366;
        border-radius: 50%;
        box-shadow: 0 0 10px rgba(37, 211, 102, 0.6);
        animation: whatsapp-pulse 2s infinite;
    }

    @media (max-width: 768px) {
        border-radius: 0 0 20px 20px;
    }
}

/* Content Styles */
.whatsapp-gallery-content {
    position: relative;
    z-index: 3;
    padding: 2rem 2.5rem;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.98) 100%);
    border-radius: 0 0 23px 23px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 1.5rem;
        right: 1.5rem;
        height: 1px;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(37, 211, 102, 0.3) 50%,
            transparent 100%);
    }

    @media (max-width: 768px) {
        padding: 1.5rem 2rem !important;
        text-align: center !important;
    }
}

/* Title Styles */
.whatsapp-gallery-item-title {
    font-weight: 700;
    line-height: 1.2;
    text-shadow: 0 0 12px rgba(37, 211, 102, 0.4);
    letter-spacing: 0.5px;
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
    font-size: 2rem;

    @media (min-width: 768px) {
        font-size: 2.5rem;
    }

    @media (max-width: 768px) {
        font-size: 1.875rem !important;
    }

    &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 60%;
        height: 2px;
        background: linear-gradient(90deg, #25D366, transparent);
        border-radius: 1px;

        @media (max-width: 768px) {
            width: 80%;
        }
    }
}

/* Description Styles */
.whatsapp-gallery-item-description {
    line-height: 1.7;
    font-weight: 500;
    text-shadow: 0 0 8px rgba(18, 140, 126, 0.3);
    margin-bottom: 2rem;
    padding: 1rem 0;
    border-left: 3px solid rgba(37, 211, 102, 0.3);
    padding-left: 1.5rem;
    background: rgba(37, 211, 102, 0.02);
    border-radius: 0 8px 8px 0;
    font-size: 1.125rem;

    @media (min-width: 768px) {
        font-size: 1.25rem;
    }

    @media (max-width: 768px) {
        font-size: 1.125rem !important;
        padding-left: 1rem;
        margin-bottom: 1.5rem;
    }
}

/* Button Styles */
.whatsapp-gallery-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    border-radius: 30px;
    font-weight: 700;
    padding: 1rem 2.5rem;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.3);
    border: 2px solid rgba(37, 211, 102, 0.6);
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    min-width: 180px;
    font-size: 1.125rem;
    will-change: transform, box-shadow;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent);
        transition: left 0.5s ease;
    }

    &::after {
        content: '💬';
        position: absolute;
        right: 1rem;
        font-size: 1.2em;
        opacity: 0;
        transform: translateX(10px);
        transition: all 0.3s ease;
    }

    &:hover {
        transform: translateY(-4px) scale(1.08);
        box-shadow: 0 12px 35px rgba(37, 211, 102, 0.6),
                    0 0 30px rgba(37, 211, 102, 0.4);
        border-color: rgba(37, 211, 102, 1);
        background: linear-gradient(135deg, #2EE86C, #25D366);

        &::before {
            left: 100%;
        }

        &::after {
            opacity: 1;
            transform: translateX(0);
        }
    }

    &:focus {
        outline: 3px solid #25D366;
        outline-offset: 3px;
        box-shadow: 0 0 0 6px rgba(37, 211, 102, 0.3);
    }

    @media (max-width: 768px) {
        min-width: 160px;
        padding: 0.875rem 2rem;
        font-size: 0.95rem;
    }
}

/* Animations */
@keyframes whatsapp-gallery-glow {
    0% {
        filter: blur(5px);
    }
    100% {
        filter: blur(10px);
    }
}

@keyframes whatsapp-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* RTL Support */
[dir="rtl"] {
    .whatsapp-gallery-gradient.gradient-left {
        background: linear-gradient(to right, rgba(37, 211, 102, 0.3) 0%, transparent 70%);
    }

    .whatsapp-gallery-gradient.gradient-right {
        background: linear-gradient(to left, rgba(37, 211, 102, 0.3) 0%, transparent 70%);
    }

    .whatsapp-gallery-item-description {
        border-left: none;
        border-right: 3px solid rgba(37, 211, 102, 0.3);
        padding-left: 0;
        padding-right: 1.5rem;
        border-radius: 8px 0 0 8px;
    }

    .whatsapp-gallery-item-title::after {
        left: auto;
        right: 0;
    }

    .whatsapp-gallery-button::after {
        right: auto;
        left: 1rem;
    }

    .whatsapp-gallery-bubble-bg::after {
        right: auto;
        left: 15px;
    }
}

/* Accessibility and Performance */
@media (prefers-reduced-motion: reduce) {
    .whatsapp-gallery-item,
    .whatsapp-gallery-image,
    .whatsapp-gallery-button,
    .whatsapp-gallery-item::before,
    .whatsapp-gallery-bubble-bg::after {
        animation: none !important;
        transition: none !important;
    }

    .whatsapp-gallery-button::before {
        display: none;
    }

    .whatsapp-gallery-item:hover {
        transform: none !important;
    }
}

/* Loading State */
.whatsapp-gallery-item.loading {
    opacity: 0.7;
    pointer-events: none;

    &::before {
        animation: whatsapp-gallery-glow 1s infinite;
    }
}

/* Focus States */
.whatsapp-gallery-item:focus-within {
    outline: 2px solid rgba(37, 211, 102, 0.6);
    outline-offset: 2px;
}
