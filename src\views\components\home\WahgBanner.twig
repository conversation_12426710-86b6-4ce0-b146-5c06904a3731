{#
| Variable                          | Type    | Description                                    |
|-----------------------------------|---------|------------------------------------------------|
| component                         | object  | Main component data                            |
| component.banner_image            | string  | Banner image URL                               |
| component.banner_url              | object  | Banner link (variable-list format)            |
| component.banner_alt              | string  | Alternative text for image (optional)         |
| component.full_width              | boolean | Full-width display toggle                      |
| component.banner_height           | number  | Banner height in pixels                        |
| component.border_radius           | number  | Border radius in pixels                        |
| component.enable_hover_effects    | boolean | Enable hover effects                           |
| component.hover_overlay_color     | string  | Hover overlay color                            |
| component.hover_overlay_opacity   | number  | Hover overlay opacity (0-100)                 |
| component.animation_type          | array   | Animation type selection                       |
| component.animation_duration      | array   | Animation duration selection                   |
| component.enable_shadow           | boolean | Enable shadow                                  |
| component.shadow_color            | string  | Shadow color                                   |
| component.shadow_intensity        | number  | Shadow intensity (0-20px)                     |
| component.enable_border           | boolean | Enable border                                  |
| component.border_color            | string  | Border color                                   |
| component.mobile_height           | number  | Mobile height in pixels                        |
| component.hide_on_mobile          | boolean | Hide on mobile devices                         |
| component.lazy_loading            | boolean | Enable lazy loading                            |
| component.image_optimization      | boolean | Enable image optimization                      |
#}

{# Extract component data with fallbacks #}
{% set banner_image = component.banner_image|default('') %}
{% set banner_alt = component.banner_alt|default('بنر عصري') %}
{% set unique_id = component.id|default('wahg-banner-' ~ random()) %}

{# Get enhanced settings with defaults - handle both array and direct values #}
{% set full_width = component.full_width|default(false) %}
{% set banner_height = component.banner_height|default(400) %}
{% set border_radius = component.border_radius|default(12) %}
{% set enable_hover_effects = component.enable_hover_effects|default(true) %}
{% set hover_overlay_color = component.hover_overlay_color|default('#000000') %}
{% set hover_overlay_opacity = component.hover_overlay_opacity|default(20) %}
{% set animation_type = component.animation_type is iterable and component.animation_type[0] is defined ? component.animation_type[0].value : (component.animation_type|default('zoom')) %}
{% set animation_duration = component.animation_duration is iterable and component.animation_duration[0] is defined ? component.animation_duration[0].value : (component.animation_duration|default('normal')) %}
{% set enable_shadow = component.enable_shadow|default(true) %}
{% set shadow_color = component.shadow_color|default('#000000') %}
{% set shadow_intensity = component.shadow_intensity|default(10) %}
{% set enable_border = component.enable_border|default(false) %}
{% set border_color = component.border_color|default('#e5e7eb') %}
{% set mobile_height = component.mobile_height|default(250) %}
{% set hide_on_mobile = component.hide_on_mobile|default(false) %}
{% set lazy_loading = component.lazy_loading|default(true) %}
{% set image_optimization = component.image_optimization|default(true) %}

{# Build CSS classes based on settings #}
{% set container_classes = 's-block s-block--wahg-banner' %}
{% if full_width %}
    {% set container_classes = container_classes ~ ' s-block--full-width' %}
{% else %}
    {% set container_classes = container_classes ~ ' container' %}
{% endif %}
{% set banner_classes = 'wahg-banner' %}
{% if enable_hover_effects %}
    {% set banner_classes = banner_classes ~ ' wahg-banner--hover-enabled' %}
{% endif %}
{% set banner_classes = banner_classes ~ ' animation-type-' ~ animation_type ~ ' animation-duration-' ~ animation_duration %}
{% if hide_on_mobile %}
    {% set banner_classes = banner_classes ~ ' wahg-banner--hide-mobile' %}
{% endif %}

{# Debug output - remove in production #}
<!-- DEBUG: full_width = {{ full_width }}, animation_type = {{ animation_type }}, animation_duration = {{ animation_duration }} -->

{# Extract URL from variable-list format #}
{% set banner_url = null %}
{% if component.banner_url is defined and component.banner_url %}
    {% if component.banner_url.url is defined %}
        {% set banner_url = component.banner_url.url %}
    {% elseif component.banner_url is iterable and component.banner_url|length > 0 %}
        {% set banner_url = component.banner_url[0].url|default(null) %}
    {% endif %}
{% endif %}

{# Only render if image is provided #}
{% if banner_image and banner_image != '' %}
    <section class="{{ container_classes }}"
             id="wahg-banner-{{ unique_id }}"
             data-component="wahg-banner"
             data-full-width="{{ full_width ? 'true' : 'false' }}"
             data-banner-height="{{ banner_height }}"
             data-border-radius="{{ border_radius }}"
             data-enable-hover-effects="{{ enable_hover_effects ? 'true' : 'false' }}"
             data-hover-overlay-color="{{ hover_overlay_color }}"
             data-hover-overlay-opacity="{{ hover_overlay_opacity }}"
             data-animation-type="{{ animation_type }}"
             data-animation-duration="{{ animation_duration }}"
             data-enable-shadow="{{ enable_shadow ? 'true' : 'false' }}"
             data-shadow-color="{{ shadow_color }}"
             data-shadow-intensity="{{ shadow_intensity }}"
             data-enable-border="{{ enable_border ? 'true' : 'false' }}"
             data-border-color="{{ border_color }}"
             data-mobile-height="{{ mobile_height }}"
             data-hide-on-mobile="{{ hide_on_mobile ? 'true' : 'false' }}"
             data-lazy-loading="{{ lazy_loading ? 'true' : 'false' }}"
             data-image-optimization="{{ image_optimization ? 'true' : 'false' }}"
             style="--banner-height: {{ banner_height }}px; --border-radius: {{ border_radius }}px; --hover-overlay-color: {{ hover_overlay_color }}; --hover-overlay-opacity: {{ hover_overlay_opacity / 100 }}; --shadow-color: {{ shadow_color }}; --shadow-intensity: {{ shadow_intensity }}px; --border-color: {{ border_color }}; --mobile-height: {{ mobile_height }}px;">

        {% if component.title %}
            <div class="s-block__title">
                <div class="right-side">
                    <h2>{{ component.title }}</h2>
                </div>
            </div>
        {% endif %}

        <div class="wahg-banner-wrapper">
            {% if banner_url and banner_url != '' and banner_url != '#' %}
                <a href="{{ banner_url }}"
                   class="{{ banner_classes }}"
                   aria-label="{{ banner_alt }}"
                   rel="noopener noreferrer">
                    <img src="{{ banner_image | default('') }}"
                         alt="{{ banner_alt }}"
                         class="wahg-banner__image"
                         {% if not lazy_loading %}loading="eager"{% else %}loading="lazy"{% endif %}
                         decoding="async"
                         onerror="this.style.display='none'; this.parentElement.classList.add('wahg-banner--image-error');"
                         onload="this.style.opacity='1'; this.classList.add('wahg-banner__image--loaded');">
                </a>
            {% else %}
                <div class="{{ banner_classes }} wahg-banner--no-link">
                    <img src="{{ banner_image | default('') }}"
                         alt="{{ banner_alt }}"
                         class="wahg-banner__image"
                         {% if not lazy_loading %}loading="eager"{% else %}loading="lazy"{% endif %}
                         decoding="async"
                         onerror="this.style.display='none'; this.parentElement.classList.add('wahg-banner--image-error');"
                         onload="this.style.opacity='1'; this.classList.add('wahg-banner__image--loaded');">
                </div>
            {% endif %}
        </div>
    </section>
{% else %}
    {# Placeholder for admin/preview mode #}
    <section class="s-block s-block--wahg-banner container" id="wahg-banner-{{ unique_id }}">
        {% if component.title %}
            <div class="s-block__title">
                <div class="right-side">
                    <h2>{{ component.title }}</h2>
                </div>
            </div>
        {% endif %}

        <div class="wahg-banner-wrapper">
            <div class="wahg-banner wahg-banner--placeholder">
                <div class="wahg-banner__placeholder">
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 200px;
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                        color: #666;
                        font-size: 16px;
                        text-align: center;
                        border-radius: 12px;
                        border: 2px dashed #ddd;
                    ">
                        <div>
                            <div style="font-size: 48px; margin-bottom: 10px;">🖼️</div>
                            <div style="font-weight: bold; margin-bottom: 5px;">بنر عصري</div>
                            <div style="font-size: 14px; opacity: 0.7;">قم بإضافة صورة البنر من لوحة التحكم</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endif %}