/**
 * Google Maps Component Styling
 * CSS classes for color and background customization
 */

/* Base component styling */
.google-maps-component {
    position: relative;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

/* Background variations */
.google-maps-component.background-enabled {
    background-color: var(--component-bg-color, #f8f9fa);
}

.google-maps-component.background-disabled {
    background-color: transparent;
}

/* Border variations */
.google-maps-component.border-enabled {
    border: 1px solid var(--component-border-color, #dee2e6);
}

.google-maps-component.border-disabled {
    border: none;
}

/* Padding size variations */
.google-maps-component.padding-small {
    padding: 15px;
}

.google-maps-component.padding-medium {
    padding: 30px;
}

.google-maps-component.padding-large {
    padding: 45px;
}

.google-maps-component.padding-xlarge {
    padding: 60px;
}

.google-maps-component.padding-disabled {
    padding: 0;
}

/* Text color classes */
.google-maps-component .component-title,
.google-maps-component .maps-title,
.google-maps-component h2,
.google-maps-component h3,
.google-maps-component h4 {
    color: var(--title-color, #212529);
    transition: color 0.3s ease;
}

.google-maps-component .component-description,
.google-maps-component .maps-description,
.google-maps-component p {
    color: var(--description-color, #6c757d);
    transition: color 0.3s ease;
}

.google-maps-component .branch-name,
.google-maps-component .store-name {
    color: var(--branch-name-color, #495057);
    transition: color 0.3s ease;
    font-weight: 600;
}

.google-maps-component .branch-address,
.google-maps-component .store-address {
    color: var(--branch-address-color, #6c757d);
    transition: color 0.3s ease;
}

/* Map container styling */
.google-maps-component .map-container {
    position: relative;
    overflow: hidden;
    border-radius: inherit;
}

.google-maps-component .map-iframe {
    width: 100%;
    height: 400px;
    border: none;
    border-radius: inherit;
    transition: all 0.3s ease;
}

/* Branch info styling */
.google-maps-component .branch-info {
    margin-top: 20px;
    padding: 15px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.google-maps-component .branch-info.hidden {
    display: none;
}

/* Responsive design */
@media (max-width: 768px) {
    .google-maps-component.padding-small {
        padding: 10px;
    }
    
    .google-maps-component.padding-medium {
        padding: 20px;
    }
    
    .google-maps-component.padding-large {
        padding: 30px;
    }
    
    .google-maps-component.padding-xlarge {
        padding: 40px;
    }
    
    .google-maps-component .map-iframe {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .google-maps-component.padding-small {
        padding: 8px;
    }
    
    .google-maps-component.padding-medium {
        padding: 15px;
    }
    
    .google-maps-component.padding-large {
        padding: 20px;
    }
    
    .google-maps-component.padding-xlarge {
        padding: 25px;
    }
    
    .google-maps-component .map-iframe {
        height: 250px;
    }
    
    .google-maps-component .branch-info {
        margin-top: 15px;
        padding: 12px;
    }
}

/* Animation classes */
.google-maps-component.animate-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover effects */
.google-maps-component:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.google-maps-component .map-iframe:hover {
    transform: scale(1.02);
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .google-maps-component {
        --title-color: #ffffff;
        --description-color: #b0b0b0;
        --branch-name-color: #e0e0e0;
        --branch-address-color: #b0b0b0;
        --component-bg-color: #2d3748;
        --component-border-color: #4a5568;
    }
    
    .google-maps-component .branch-info {
        background-color: rgba(45, 55, 72, 0.9);
        color: #ffffff;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .google-maps-component {
        --title-color: #000000;
        --description-color: #333333;
        --branch-name-color: #000000;
        --branch-address-color: #333333;
        --component-border-color: #000000;
    }
    
    .google-maps-component.border-enabled {
        border-width: 2px;
    }
}

/* Print styles */
@media print {
    .google-maps-component {
        background-color: transparent !important;
        border: 1px solid #000000 !important;
        box-shadow: none !important;
        transform: none !important;
    }
    
    .google-maps-component .map-iframe {
        display: none;
    }
    
    .google-maps-component .branch-info {
        background-color: transparent !important;
        border: 1px solid #000000;
    }
}
