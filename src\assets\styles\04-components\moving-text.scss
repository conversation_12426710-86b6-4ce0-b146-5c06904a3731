/* Gaming Theme Moving Text Component - SCSS */

/* Critical Gaming Theme Moving Text Styles */
.gaming-moving-text-section {
    position: relative;
    overflow: hidden;
    width: 100%;
    background: #000000; /* Default fallback */
    padding: 6px 0;
    border-top: 2px solid rgba(0, 212, 255, 0.3);
    border-bottom: 2px solid rgba(0, 212, 255, 0.3);
    box-shadow:
        0 0 20px rgba(0, 212, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    contain: layout style paint;

    // Performance optimizations
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000px;
    -webkit-perspective: 1000px;
}

/* Specific styling for component instances with dynamic colors */
.gaming-moving-text-section {
    --section-color: var(--component-bg-color, #000000);
    background: var(--section-color);
    background: linear-gradient(135deg,
        var(--section-color) 0%,
        color-mix(in srgb, var(--section-color) 80%, #1a1a2e) 50%,
        var(--section-color) 100%);
}

/* Enhanced container with better performance */
.gaming-moving-text-container {
    position: relative;
    height: 30px;
    display: flex;
    align-items: center;
    overflow: hidden;
    contain: layout style paint;
}

/* Enhanced moving text with better animations and text repetition */
.gaming-moving-text {
    position: absolute;
    white-space: nowrap;
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    text-shadow:
        0 0 15px rgba(0, 212, 255, 0.9),
        0 0 25px rgba(0, 212, 255, 0.5),
        2px 2px 6px rgba(0, 0, 0, 0.9);
    animation: moveTextDesktop var(--animation-speed, 25s) linear infinite;
    will-change: transform;
    letter-spacing: 2px;
    display: flex;
    align-items: center;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    // Text repetition using pseudo-elements
    &::after {
        content: attr(data-text);
        margin-left: 100px;
        margin-right: 100px;
    }

    &::before {
        content: attr(data-text);
        margin-left: 100px;
        margin-right: 100px;
    }
}

/* Enhanced keyframe animation with better performance for desktop */
@keyframes moveTextDesktop {
    0% {
        transform: translate3d(100%, 0, 0);
    }
    100% {
        transform: translate3d(-100%, 0, 0);
    }
}

/* Mobile animation with faster speed */
@keyframes moveTextMobile {
    0% {
        transform: translate3d(100%, 0, 0);
    }
    100% {
        transform: translate3d(-100%, 0, 0);
    }
}

/* RTL support */
[dir="rtl"] .gaming-moving-text {
    animation-name: moveTextRTL;
}

@keyframes moveTextRTL {
    0% {
        transform: translate3d(-100%, 0, 0);
    }
    100% {
        transform: translate3d(100%, 0, 0);
    }
}

/* Gaming glow effect */
.gaming-moving-text-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 212, 255, 0.1) 50%,
        transparent 100%);
    animation: scanLine 3s ease-in-out infinite;
}

@keyframes scanLine {
    0%, 100% {
        left: -100%;
    }
    50% {
        left: 100%;
    }
}

/* Force animation duration for each section individually */
.gaming-moving-text-section[data-speed="10"] .gaming-moving-text {
    animation-duration: 10s !important;
}

.gaming-moving-text-section[data-speed="15"] .gaming-moving-text {
    animation-duration: 15s !important;
}

.gaming-moving-text-section[data-speed="25"] .gaming-moving-text {
    animation-duration: 25s !important;
}

.gaming-moving-text-section[data-speed="35"] .gaming-moving-text {
    animation-duration: 35s !important;
}

.gaming-moving-text-section[data-speed="50"] .gaming-moving-text {
    animation-duration: 50s !important;
}

/* Circuit pattern overlay for enhanced gaming effect - Performance Optimized */
.gaming-moving-text-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(90deg, transparent 24%, var(--gaming-glow-blue) 25%, var(--gaming-glow-blue) 26%, transparent 27%, transparent 74%, var(--gaming-glow-blue) 75%, var(--gaming-glow-blue) 76%, transparent 77%, transparent),
        linear-gradient(0deg, transparent 24%, var(--gaming-glow-blue) 25%, var(--gaming-glow-blue) 26%, transparent 27%, transparent 74%, var(--gaming-glow-blue) 75%, var(--gaming-glow-blue) 76%, transparent 77%, transparent);
    background-size: 50px 50px;
    opacity: 0.3;
    animation: circuitMove 20s linear infinite;
    pointer-events: none;
    z-index: 0;
    will-change: background-position;
    transform: translate3d(0, 0, 0);
    contain: layout style paint;
}

@keyframes circuitMove {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* Hover effects for enhanced interactivity - Performance Optimized */
.gaming-moving-text-section:hover {
    .gaming-moving-text {
        animation-play-state: paused;
        text-shadow:
            0 0 15px var(--gaming-accent-blue),
            0 0 25px rgba(0, 212, 255, 0.6),
            2px 2px 4px rgba(0, 0, 0, 0.8),
            0 0 35px rgba(0, 212, 255, 0.4);
        transform: scale3d(1.05, 1.05, 1);
        transition: transform 0.3s ease, text-shadow 0.3s ease;
    }

    &::before {
        animation-play-state: paused;
    }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
    .gaming-moving-text {
        font-size: 18px;
        letter-spacing: 3px;
    }

    .gaming-moving-text-container {
        height: 35px;
    }

    .gaming-moving-text-section {
        padding: 8px 0;
    }

    /* Desktop specific speed overrides */
    .gaming-moving-text-section[data-speed="10"] .gaming-moving-text {
        animation-duration: 10s !important;
    }

    .gaming-moving-text-section[data-speed="15"] .gaming-moving-text {
        animation-duration: 15s !important;
    }

    .gaming-moving-text-section[data-speed="25"] .gaming-moving-text {
        animation-duration: 25s !important;
    }

    .gaming-moving-text-section[data-speed="35"] .gaming-moving-text {
        animation-duration: 35s !important;
    }

    .gaming-moving-text-section[data-speed="50"] .gaming-moving-text {
        animation-duration: 50s !important;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .gaming-moving-text {
        font-size: 13px;
        animation: moveTextMobile 15s linear infinite;
        letter-spacing: 1px;

        &::before,
        &::after {
            margin-left: 50px;
            margin-right: 50px;
        }
    }

    .gaming-moving-text-container {
        height: 28px;
    }

    .gaming-moving-text-section {
        padding: 6px 0;
    }
}

@media (max-width: 480px) {
    .gaming-moving-text {
        font-size: 12px;
        animation: moveTextMobile 12s linear infinite;
        letter-spacing: 0.5px;

        &::before,
        &::after {
            margin-left: 30px;
            margin-right: 30px;
        }
    }

    .gaming-moving-text-container {
        height: 25px;
    }

    .gaming-moving-text-section {
        padding: 5px 0;
    }
}

/* Mobile and tablet speed overrides */
@media (max-width: 768px) {
    .gaming-moving-text-section[data-speed="10"] .gaming-moving-text {
        animation: moveTextMobile 10s linear infinite !important;
    }

    .gaming-moving-text-section[data-speed="15"] .gaming-moving-text {
        animation: moveTextMobile 15s linear infinite !important;
    }

    .gaming-moving-text-section[data-speed="25"] .gaming-moving-text {
        animation: moveTextMobile 25s linear infinite !important;
    }

    .gaming-moving-text-section[data-speed="35"] .gaming-moving-text {
        animation: moveTextMobile 35s linear infinite !important;
    }

    .gaming-moving-text-section[data-speed="50"] .gaming-moving-text {
        animation: moveTextMobile 50s linear infinite !important;
    }
}

@media (max-width: 480px) {
    .gaming-moving-text {
        font-size: 12px;
        letter-spacing: 0.5px;

        &::before,
        &::after {
            margin-left: 30px;
            margin-right: 30px;
        }
    }

    .gaming-moving-text-container {
        height: 25px;
    }

    .gaming-moving-text-section {
        padding: 5px 0;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .gaming-moving-text {
        animation: none;
        position: static;
        transform: none;
        text-align: center;
    }
    
    .gaming-moving-text-section {
        &::before,
        &::after {
            animation: none;
        }
    }
    
    .gaming-moving-text-container {
        justify-content: center;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gaming-moving-text {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
        color: #ffffff;
    }
    
    .gaming-moving-text-section {
        border-top: 3px solid #ffffff;
        border-bottom: 3px solid #ffffff;
    }
}

/* Print styles */
@media print {
    .gaming-moving-text-section {
        background: transparent !important;
        border: 1px solid #000000;
        box-shadow: none;
        
        &::before,
        &::after {
            display: none;
        }
    }
    
    .gaming-moving-text {
        animation: none;
        position: static;
        color: #000000;
        text-shadow: none;
        text-align: center;
    }
}
