/* Enhanced Special Vertical Gallery Component Styles */
/* Gaming-themed design with comprehensive configuration support */

/* CSS Custom Properties for Dynamic Theming */
.special-vertical-gallery-section {
    --main-title-color: #ffffff;
    --item-title-color: #ffffff;
    --item-description-color: #e0e0e0;
    --button-bg-color: #00ff88;
    --button-text-color: #000000;
    --hover-overlay-color: #00ff88;
    --glow-border-color: #00d4ff;
    --item-background-color: #1a1a1a;
    --item-spacing: 20px;
    --image-border-radius: 10px;
    --top-margin: 0px;
    --bottom-margin: 40px;
}

/* Main Gallery Section */
.special-vertical-gallery-section {
    position: relative;
    padding: var(--top-margin) 0 var(--bottom-margin);
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.special-vertical-gallery-section.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Simple Test Version - Override for immediate visibility */
.special-vertical-gallery-test {
    opacity: 1 !important;
    transform: none !important;
    padding: 40px 0;
}

/* Background Styling */
.special-vertical-gallery-section.has-background {
    background: var(--background-color);
    background-image: 
        radial-gradient(circle at 20% 50%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(255, 0, 136, 0.1) 0%, transparent 50%);
}

/* Full Width Layout */
.special-vertical-gallery-section.full-width-gallery {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
}

.special-vertical-gallery-section.full-width-gallery .gallery-container {
    max-width: none;
    padding: 0 40px;
}

/* Particles Background */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: var(--glow-border-color);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    opacity: 0.6;
}

/* Gallery Header */
.gallery-header {
    position: relative;
    z-index: 2;
    margin-bottom: 60px;
    text-align: center;
}

.gallery-main-title {
    color: var(--main-title-color);
    margin: 0 0 20px;
    font-family: inherit;
    line-height: 1.2;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease-out;
}

.gallery-main-title.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.gallery-main-description {
    color: var(--item-description-color);
    margin: 0;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease-out 0.2s;
}

.gallery-main-description.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Gallery Container */
.gallery-container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
    width: 100%;
}

/* Ensure container is responsive */
@media (max-width: 1240px) {
    .gallery-container {
        max-width: 95%;
        padding: 0 20px;
    }
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    gap: var(--item-spacing);
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    align-items: start;
    width: 100%;
    box-sizing: border-box;
}

/* Ensure grid items don't overflow */
.gallery-grid > * {
    min-width: 0;
    box-sizing: border-box;
}

.gallery-grid.items-per-row-1 {
    grid-template-columns: 1fr;
    max-width: 600px;
    margin: 0 auto;
}

.gallery-grid.items-per-row-2 {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.gallery-grid.items-per-row-3 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.gallery-grid.items-per-row-4 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.gallery-grid.items-per-row-5 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Mobile Responsive Grid Classes */
@media (max-width: 640px) {
    .gallery-grid.mobile-items-1 {
        grid-template-columns: 1fr !important;
    }

    .gallery-grid.mobile-items-2 {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* Tablet Responsive Grid Classes */
@media (min-width: 641px) and (max-width: 1024px) {
    .gallery-grid.tablet-items-1 {
        grid-template-columns: 1fr !important;
    }

    .gallery-grid.tablet-items-2 {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .gallery-grid.tablet-items-3 {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

/* Mobile Full Width */
@media (max-width: 640px) {
    .special-vertical-gallery-section.mobile-full-width .gallery-container {
        max-width: none;
        padding: 0 10px;
    }

    .special-vertical-gallery-section.mobile-full-width .gallery-grid {
        gap: 15px;
    }
}

/* Gallery Items */
.gallery-item {
    position: relative;
    background: var(--item-background-color);
    border-radius: var(--image-border-radius);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0;
    transform: translateY(30px);
}

.gallery-item.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.gallery-item.has-glow {
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.2);
}

.gallery-item.has-shadows {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.gallery-item.has-glow.has-shadows {
    box-shadow: 
        0 10px 30px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 212, 255, 0.2);
}

/* Image Container */
.gallery-item-image-wrapper {
    position: relative;
    overflow: hidden;
}

.image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.gallery-item-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease-out;
}

/* Ensure images with data-src also display */
.gallery-item-image[data-src] {
    background: #2a2a2a;
    display: block;
}

/* When image loads, remove background */
.gallery-item-image[src]:not([data-src]) {
    background: none;
}

/* Hover Effects */
.gallery-item.hover-enabled:hover .gallery-item-image {
    transform: scale(1.05);
}

.hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, var(--hover-overlay-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.gallery-item.hover-enabled:hover .hover-overlay {
    opacity: 0.2;
}

.glow-border {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--glow-border-color), transparent, var(--glow-border-color));
    border-radius: var(--image-border-radius);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: -1;
}

.gallery-item.hover-enabled:hover .glow-border {
    opacity: 0.6;
}

/* Image Effects */
.image-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shine-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.gallery-item.image-effects-enabled:hover .shine-effect {
    left: 100%;
}

.pulse-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: var(--glow-border-color);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
}

.gallery-item.image-effects-enabled:hover .pulse-effect {
    animation: pulse 1s ease-out;
}

/* Gallery Item Content */
.gallery-item-content {
    padding: 25px;
    position: relative;
    z-index: 2;
}

.gallery-item-title {
    color: var(--item-title-color);
    margin: 0 0 15px;
    line-height: 1.3;
    opacity: 0;
    transform: translateY(15px);
    transition: all 0.6s ease-out;
}

.gallery-item-title.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.gallery-item-description {
    color: var(--item-description-color);
    margin: 0 0 20px;
    line-height: 1.6;
    opacity: 0;
    transform: translateY(15px);
    transition: all 0.6s ease-out 0.1s;
}

.gallery-item-description.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Gallery Item Button */
.gallery-item-button-wrapper {
    margin-top: 20px;
}

.gallery-item-button {
    display: inline-block;
    padding: 12px 30px;
    background: var(--button-bg-color);
    color: var(--button-text-color);
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(15px);
}

.gallery-item-button.animate-in {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.2s;
}

.gallery-item-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
}

.button-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.gallery-item-button:hover .button-glow {
    left: 100%;
}

/* Font Size Variants */
.font-size-extra-small { font-size: 12px; }
.font-size-small { font-size: 14px; }
.font-size-medium { font-size: 18px; }
.font-size-large { font-size: 24px; }
.font-size-extra-large { font-size: 32px; }

/* Font Weight Variants */
.font-weight-normal { font-weight: 400; }
.font-weight-medium { font-weight: 500; }
.font-weight-bold { font-weight: 700; }
.font-weight-extra-bold { font-weight: 800; }

/* Text Alignment */
.text-align-left { text-align: left; }
.text-align-center { text-align: center; }
.text-align-right { text-align: right; }

/* Animation Keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes trackingExpand {
    0% {
        opacity: 0;
        letter-spacing: -0.5em;
    }
    40% {
        opacity: 0.6;
    }
    100% {
        opacity: 1;
        letter-spacing: normal;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* Animation Duration Classes */
.animation-duration-fast { --animation-duration: 0.5s; }
.animation-duration-medium { --animation-duration: 1s; }
.animation-duration-slow { --animation-duration: 1.5s; }
.animation-duration-extra-slow { --animation-duration: 2s; }

/* Gallery Animation Types */
.gallery-animation-fadeIn.animate-in {
    animation: fadeIn var(--animation-duration, 1s) ease-out;
}

.gallery-animation-slideUp.animate-in {
    animation: slideUp var(--animation-duration, 1s) ease-out;
}

.gallery-animation-scaleIn.animate-in {
    animation: scaleIn var(--animation-duration, 1s) ease-out;
}

.gallery-animation-bounceIn.animate-in {
    animation: bounceIn var(--animation-duration, 1s) ease-out;
}

/* Item Animation Types */
.item-animation-fadeIn.animate-in {
    animation: fadeIn var(--animation-duration, 1s) ease-out;
}

.item-animation-slideUp.animate-in {
    animation: slideUp var(--animation-duration, 1s) ease-out;
}

.item-animation-scaleIn.animate-in {
    animation: scaleIn var(--animation-duration, 1s) ease-out;
}

.item-animation-slideLeft.animate-in {
    animation: slideLeft var(--animation-duration, 1s) ease-out;
}

.item-animation-slideRight.animate-in {
    animation: slideRight var(--animation-duration, 1s) ease-out;
}

/* Text Animation Types */
.text-animation-fadeIn.animate-in {
    animation: fadeIn var(--animation-duration, 1s) ease-out;
}

.text-animation-slideUp.animate-in {
    animation: slideUp var(--animation-duration, 1s) ease-out;
}

.text-animation-scaleIn.animate-in {
    animation: scaleIn var(--animation-duration, 1s) ease-out;
}

.text-animation-trackingExpand.animate-in {
    animation: trackingExpand var(--animation-duration, 1s) ease-out;
}

/* Hover Animation Types */
.hover-animation-scale:hover {
    transform: scale(1.05);
}

.hover-animation-slide:hover {
    transform: translateY(-10px);
}

.hover-animation-pulse:hover {
    animation: pulse 1s ease-in-out;
}

.hover-animation-glow:hover {
    box-shadow: 0 0 30px var(--hover-overlay-color);
}

.hover-animation-lift:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */

/* Tablet Portrait */
@media (max-width: 1024px) {
    .gallery-grid.items-per-row-4,
    .gallery-grid.items-per-row-5 {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .gallery-container {
        padding: 0 30px;
    }
}

/* Tablet */
@media (max-width: 768px) {
    .special-vertical-gallery-section {
        padding: 20px 0 30px;
    }

    .gallery-container {
        padding: 0 20px;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
        gap: 20px;
    }

    .gallery-header {
        margin-bottom: 30px;
    }

    .gallery-main-title {
        margin-bottom: 15px;
    }

    .gallery-item-content {
        padding: 20px 15px;
    }

    .image-container {
        height: 220px;
    }

    /* Font Size Adjustments for Tablet */
    .font-size-extra-large { font-size: 28px; }
    .font-size-large { font-size: 22px; }
    .font-size-medium { font-size: 18px; }
    .font-size-small { font-size: 14px; }
    .font-size-extra-small { font-size: 12px; }

    .gallery-item-button {
        padding: 12px 25px;
        font-size: 14px;
    }
}

/* Mobile Large */
@media (max-width: 640px) {
    .gallery-grid {
        grid-template-columns: 1fr !important;
        gap: 25px;
    }

    .gallery-container {
        padding: 0 20px;
    }

    .image-container {
        height: 250px;
    }

    .gallery-item {
        max-width: 400px;
        margin: 0 auto;
    }
}

/* Mobile */
@media (max-width: 480px) {
    .special-vertical-gallery-section {
        padding: 15px 0 25px;
    }

    .gallery-container {
        padding: 0 15px;
    }

    .gallery-grid {
        gap: 20px;
    }

    .gallery-header {
        margin-bottom: 25px;
    }

    .gallery-main-title {
        margin-bottom: 12px;
    }

    .gallery-item-content {
        padding: 18px 15px;
    }

    .image-container {
        height: 200px;
    }

    .gallery-item {
        max-width: none;
    }

    /* Font Size Adjustments for Mobile */
    .font-size-extra-large { font-size: 24px; }
    .font-size-large { font-size: 20px; }
    .font-size-medium { font-size: 16px; }
    .font-size-small { font-size: 14px; }
    .font-size-extra-small { font-size: 12px; }

    .gallery-item-button {
        padding: 10px 20px;
        font-size: 13px;
        width: 100%;
        text-align: center;
        box-sizing: border-box;
    }

    .gallery-item-button-wrapper {
        margin-top: 15px;
    }
}

/* Mobile Small */
@media (max-width: 360px) {
    .gallery-container {
        padding: 0 12px;
    }

    .gallery-grid {
        gap: 15px;
    }

    .gallery-item-content {
        padding: 15px 12px;
    }

    .image-container {
        height: 180px;
    }

    /* Font Size Adjustments for Small Mobile */
    .font-size-extra-large { font-size: 22px; }
    .font-size-large { font-size: 18px; }
    .font-size-medium { font-size: 15px; }
    .font-size-small { font-size: 13px; }
    .font-size-extra-small { font-size: 11px; }

    .gallery-item-button {
        padding: 8px 16px;
        font-size: 12px;
    }
}

/* Full Width Gallery Responsive */
@media (max-width: 768px) {
    .special-vertical-gallery-section.full-width-gallery .gallery-container {
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .special-vertical-gallery-section.full-width-gallery .gallery-container {
        padding: 0 15px;
    }
}

/* Particles Responsive */
@media (max-width: 768px) {
    .particle {
        width: 2px;
        height: 2px;
    }

    .particles-container {
        opacity: 0.5;
    }
}

@media (max-width: 480px) {
    .particles-container {
        display: none; /* Hide particles on small mobile for performance */
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Mobile touch devices */
    .gallery-item {
        /* Remove hover effects on touch devices */
        transition: transform 0.2s ease;
    }

    .gallery-item:active {
        transform: scale(0.98);
    }

    .gallery-item-button {
        /* Larger touch targets */
        min-height: 44px;
        padding: 12px 24px;
        touch-action: manipulation;
    }

    .gallery-item-button:active {
        transform: scale(0.95);
        background: var(--button-bg-color);
        filter: brightness(0.9);
    }

    /* Disable complex hover effects on touch */
    .hover-overlay,
    .glow-border,
    .shine-effect {
        display: none;
    }

    /* Simplify animations for better performance */
    .gallery-item.image-effects-enabled .pulse-effect {
        display: none;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .gallery-item-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .special-vertical-gallery-section {
        padding: 10px 0 20px;
    }

    .gallery-header {
        margin-bottom: 20px;
    }

    .image-container {
        height: 160px;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
        gap: 15px;
    }
}

/* Very Small Screens */
@media (max-width: 320px) {
    .gallery-container {
        padding: 0 10px;
    }

    .gallery-grid {
        gap: 12px;
    }

    .gallery-item-content {
        padding: 12px 10px;
    }

    .image-container {
        height: 160px;
    }

    .font-size-extra-large { font-size: 20px; }
    .font-size-large { font-size: 16px; }
    .font-size-medium { font-size: 14px; }
    .font-size-small { font-size: 12px; }
    .font-size-extra-small { font-size: 10px; }
}
