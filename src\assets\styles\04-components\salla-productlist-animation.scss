/* Salla ProductList Animation Styles - Updated for Universal System */

/* Note: Product card animations are now handled by the Universal Product Cards Animation system
 * This file is kept for compatibility and any Salla-specific styling that doesn't conflict
 */

/* Enhanced hover effects for products - Hardware Accelerated */
salla-products-list custom-salla-product-card:hover {
    transform: translate3d(0, -5px, 0) scale(1.02);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
}

/* Note: Staggered animations are now handled by the Universal Product Cards Animation system
 * This section is kept for reference but animations are managed globally
 */

/* Loading state animation */
salla-products-list[loading] custom-salla-product-card {
    opacity: 0.3;
    transform: translateY(10px) scale(0.98);
    animation: productLoadingPulse 1.5s ease-in-out infinite;
}

@keyframes productLoadingPulse {
    0%, 100% {
        opacity: 0.3;
    }
    50% {
        opacity: 0.6;
    }
}

/* Accessibility - Disable animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
    salla-products-list custom-salla-product-card,
    salla-products-list custom-salla-product-card.product-bounce-in {
        animation: none !important;
        transition: opacity 0.3s ease !important;
        transform: none !important;
    }
    
    salla-products-list custom-salla-product-card {
        opacity: 1;
    }
    
    salla-products-list custom-salla-product-card:hover {
        transform: none;
    }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    salla-products-list custom-salla-product-card {
        transition-duration: 1s;
    }
    
    @keyframes productGentleBounceIn {
        0% {
            opacity: 0;
            transform: translateY(15px) scale(0.97);
        }
        70% {
            opacity: 0.9;
            transform: translateY(-2px) scale(1.01);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }
    
    salla-products-list custom-salla-product-card:hover {
        transform: translateY(-3px) scale(1.01);
    }
}

/* Performance optimizations */
salla-products-list {
    contain: layout style;
}

salla-products-list custom-salla-product-card {
    contain: layout style paint;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Smooth scrolling integration */
@media (prefers-reduced-motion: no-preference) {
    salla-products-list {
        scroll-behavior: smooth;
    }
}
