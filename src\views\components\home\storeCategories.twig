{#
| Variable                      | Type      | Description                                                         |
|-------------------------------|-----------|---------------------------------------------------------------------|
| component                     | object    | Contains merchant settings for fields from twilight.json            |
| component.title               | string    | Title of the categories section                                     |
| component.show_controls       | bool      | Show slider navigation controls                                     |
| component.merge_with_top_component | bool | Merge with top component                                           |
| component.categories          | array     | List of categories                                                  |
| component.categories[].image  | string    | Category image URL                                                  |
| component.categories[].name   | string    | Category name                                                       |
| component.categories[].url    | string    | Category link URL                                                   |
| position                      | int       | Sorting number starts from zero                                     |
#}

<section class="s-block s-block--store-categories {{ component.merge_with_top_component and position ? 'merge-with-top-component' : '' }} {{ not component.title and component.merge_with_top_component ? 'merged-has-no-title' : '' }}">
    <div class="container">
        {% if component.title %}
            <div class="categories-title">
                <h2>{{ component.title }}</h2>
                <div class="categories-divider"></div>
            </div>
        {% endif %}

        {% if component.categories and component.categories|length > 0 %}
            {% set categories_count = component.categories|length %}

            {% if categories_count > 5 %}
                {# Use slider for more than 5 categories #}
                <salla-slider
                    type="carousel"
                    {% if component.title %}
                        block-title="{{ component.title }}"
                    {% endif %}
                    controls-outer
                    show-controls="{{ component.show_controls ? 'true' : 'false'}}"
                    slides-per-view="5"
                    slides-per-view-mobile="2"
                    slides-per-view-tablet="3"
                    space-between="20"
                    id="store-categories-{{ position }}"
                    class="categories-slider">
                    <div slot="items">
                        {% for category in component.categories %}
                            {% if category.name %}
                                <div class="swiper-slide">
                                    <a href="{{ category.url }}" class="category-card block">
                                        {% if category.image %}
                                            <div class="category-image">
                                                <img src="{{ category.image }}" alt="{{ category.name }}" loading="lazy">
                                            </div>
                                        {% endif %}
                                        <h3 class="category-name">{{ category.name }}</h3>
                                    </a>
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </salla-slider>
            {% else %}
                {# Use grid layout for 5 or less categories #}
                <div class="categories-grid categories-{{ categories_count }}">
                    {% for category in component.categories %}
                        {% if category.name %}
                            <a href="{{ category.url }}" class="category-card block">
                                {% if category.image %}
                                    <div class="category-image">
                                        <img src="{{ category.image }}" alt="{{ category.name }}" loading="lazy">
                                    </div>
                                {% endif %}
                                <h3 class="category-name">{{ category.name }}</h3>
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
            {% endif %}
        {% else %}
            <div class="text-center text-gray-500 py-8">
                <p>لا توجد تصنيفات متاحة حالياً</p>
            </div>
        {% endif %}
    </div>
</section>

