<section class="s-block special-gallery">
    <div class="special-gallery-flex">
        {% for index, banner in component.banners %}
            <a href="{{ banner.url }}"
               aria-label="{{ banner.title ? banner.title : 'banner-' ~ index }}"
               class="special-gallery-item">
                <div class="special-gallery-image-container">
                    <div class="special-gallery-image lazy" data-bg="{{ banner.image }}"></div>
                </div>
                {% if banner.title %}
                <article class="special-gallery-content">
                    <div class="special-gallery-text">
                        <h3 class="special-gallery-title">{{ banner.title }}</h3>
                        {% if banner.description %}
                            <p class="special-gallery-description">{{ banner.description }}</p>
                        {% endif %}
                        <span class="special-gallery-button">{{ banner.buttonTitle ? banner.buttonTitle : 'اكتشف الآن' }}</span>
                    </div>
                </article>
                {% endif %}
            </a>
        {% endfor %}
    </div>
</section>



