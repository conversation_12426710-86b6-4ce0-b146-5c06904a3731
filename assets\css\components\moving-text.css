/* Gaming Theme Moving Text Component - Enhanced Styles */

/* CSS Variables for Gaming Theme */
:root {
    --gaming-primary: #1DE9B6;
    --gaming-primary-alpha-10: rgba(29, 233, 182, 0.1);
    --gaming-primary-alpha-20: rgba(29, 233, 182, 0.2);
    --gaming-primary-alpha-30: rgba(29, 233, 182, 0.3);
    --gaming-accent-blue: #00d4ff;
    --gaming-accent-purple: #8b5cf6;
    --gaming-bg: #0f0f23;
    --gaming-darker: #0a0a0a;
    --gaming-dark: #1a1a2e;
    --gaming-text-primary: #ffffff;
    --gaming-border: #2d3748;
}

/* Enhanced Gaming Moving Text Section */
.gaming-moving-text-section {
    position: relative;
    overflow: hidden;
    width: 100%;
    z-index: 10;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
}

/* Enhanced container with better performance */
.gaming-moving-text-container {
    position: relative;
    height: 30px;
    display: flex;
    align-items: center;
    overflow: hidden;
    contain: layout style paint;
}

/* Enhanced moving text with better animations and text repetition */
.gaming-moving-text {
    position: absolute;
    white-space: nowrap;
    font-family: var(--font-main, 'DINNextLTArabic'), Arial, sans-serif;
    font-size: 16px;
    font-weight: 700;
    color: var(--gaming-text-primary);
    text-shadow:
        0 0 15px var(--gaming-accent-blue),
        0 0 25px rgba(0, 212, 255, 0.5),
        2px 2px 6px rgba(0, 0, 0, 0.9),
        0 0 35px rgba(0, 212, 255, 0.3);
    animation: moveTextDesktop var(--animation-speed, 25s) linear infinite;
    will-change: transform;
    letter-spacing: 2px;
    text-transform: uppercase;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    display: flex;
    align-items: center;
}

/* Text repetition using pseudo-elements */
.gaming-moving-text::after {
    content: attr(data-text);
    margin-left: 100px;
    margin-right: 100px;
}

.gaming-moving-text::before {
    content: attr(data-text);
    margin-left: 100px;
    margin-right: 100px;
}

/* Enhanced keyframe animation with better performance for desktop */
@keyframes moveTextDesktop {
    0% {
        transform: translate3d(100%, 0, 0);
    }
    100% {
        transform: translate3d(-100%, 0, 0);
    }
}

/* Mobile animation with faster speed */
@keyframes moveTextMobile {
    0% {
        transform: translate3d(100%, 0, 0);
    }
    100% {
        transform: translate3d(-100%, 0, 0);
    }
}

/* Enhanced scanning line effect */
.gaming-moving-text-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        var(--gaming-primary-alpha-10) 30%,
        var(--gaming-primary-alpha-20) 50%, 
        var(--gaming-primary-alpha-10) 70%,
        transparent 100%);
    animation: scanLine 3s ease-in-out infinite;
    z-index: 1;
    pointer-events: none;
}

@keyframes scanLine {
    0%, 100% {
        left: -100%;
        opacity: 0;
    }
    10%, 90% {
        opacity: 1;
    }
    50% {
        left: 100%;
        opacity: 1;
    }
}

/* Circuit pattern overlay for enhanced gaming effect */
.gaming-moving-text-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(90deg, transparent 24%, var(--gaming-primary-alpha-10) 25%, var(--gaming-primary-alpha-10) 26%, transparent 27%, transparent 74%, var(--gaming-primary-alpha-10) 75%, var(--gaming-primary-alpha-10) 76%, transparent 77%, transparent),
        linear-gradient(0deg, transparent 24%, var(--gaming-primary-alpha-10) 25%, var(--gaming-primary-alpha-10) 26%, transparent 27%, transparent 74%, var(--gaming-primary-alpha-10) 75%, var(--gaming-primary-alpha-10) 76%, transparent 77%, transparent);
    background-size: 50px 50px;
    opacity: 0.3;
    animation: circuitMove 20s linear infinite;
    pointer-events: none;
    z-index: 0;
}

@keyframes circuitMove {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* Hover effects for enhanced interactivity */
.gaming-moving-text-section:hover .gaming-moving-text {
    animation-play-state: paused;
    text-shadow: 
        0 0 15px var(--gaming-accent-blue),
        0 0 25px rgba(0, 212, 255, 0.6),
        2px 2px 4px rgba(0, 0, 0, 0.8),
        0 0 35px rgba(0, 212, 255, 0.4);
    transform: scale(1.05);
    transition: all 0.3s ease;
}

.gaming-moving-text-section:hover::before {
    animation-play-state: paused;
}

/* Enhanced responsive design */
@media (min-width: 1024px) {
    .gaming-moving-text {
        font-size: 18px;
        animation-duration: 30s;
        letter-spacing: 3px;
    }

    .gaming-moving-text-container {
        height: 35px;
    }

    .gaming-moving-text-section {
        padding: 8px 0;
    }
}

@media (max-width: 1023px) {
    .gaming-moving-text {
        font-size: 14px;
        animation: moveTextMobile 18s linear infinite;
        letter-spacing: 1.5px;
    }

    .gaming-moving-text::before,
    .gaming-moving-text::after {
        margin-left: 80px;
        margin-right: 80px;
    }
}

@media (max-width: 768px) {
    .gaming-moving-text {
        font-size: 13px;
        animation: moveTextMobile 15s linear infinite;
        letter-spacing: 1px;
    }

    .gaming-moving-text::before,
    .gaming-moving-text::after {
        margin-left: 50px;
        margin-right: 50px;
    }

    .gaming-moving-text-container {
        height: 28px;
    }

    .gaming-moving-text-section {
        padding: 6px 0;
    }

    .gaming-moving-text-section::after {
        background-size: 30px 30px;
    }
}

@media (max-width: 480px) {
    .gaming-moving-text {
        font-size: 12px;
        animation: moveTextMobile 12s linear infinite;
        letter-spacing: 0.5px;
    }

    .gaming-moving-text::before,
    .gaming-moving-text::after {
        margin-left: 30px;
        margin-right: 30px;
    }

    .gaming-moving-text-container {
        height: 25px;
    }

    .gaming-moving-text-section {
        padding: 5px 0;
    }

    .gaming-moving-text-section::after {
        background-size: 25px 25px;
        opacity: 0.2;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .gaming-moving-text {
        animation: none;
        position: static;
        transform: none;
        text-align: center;
    }
    
    .gaming-moving-text-section::before,
    .gaming-moving-text-section::after {
        animation: none;
    }
    
    .gaming-moving-text-container {
        justify-content: center;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gaming-moving-text {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
        color: #ffffff;
    }
    
    .gaming-moving-text-section {
        border-top: 3px solid #ffffff;
        border-bottom: 3px solid #ffffff;
    }
}

/* Print styles */
@media print {
    .gaming-moving-text-section {
        background: transparent !important;
        border: 1px solid #000000;
        box-shadow: none;
    }
    
    .gaming-moving-text {
        animation: none;
        position: static;
        color: #000000;
        text-shadow: none;
        text-align: center;
    }
    
    .gaming-moving-text-section::before,
    .gaming-moving-text-section::after {
        display: none;
    }
}

/* Performance optimizations */
.gaming-moving-text-section {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000px;
    -webkit-perspective: 1000px;
}

/* RTL support */
[dir="rtl"] .gaming-moving-text {
    animation-name: moveTextRTL;
}

@keyframes moveTextRTL {
    0% {
        transform: translate3d(-100%, 0, 0);
    }
    100% {
        transform: translate3d(100%, 0, 0);
    }
}
